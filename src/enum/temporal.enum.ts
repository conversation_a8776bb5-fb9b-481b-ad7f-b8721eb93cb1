export enum TemporalOrderStatus {
	SUBMITTED = 'SUBMITTED',
	ORDER_CANCELED = 'ORDER-CANCELED',
	ORDER_PROCESSING = 'ORDER-PROCESSING',
	ORDER_IN_PROGRESS = 'ORDER-IN-PROGRESS',
	ORDER_COMPLETED = 'ORDER-COMPLETED'
}

export enum TemporalOrderProgress {
	INITIAL = 'INITIAL',
	ORDER_CANCELED = 'ORDER-CANCELED',
	ORDER_PROCESSING = 'ORDER-PROCESSING',
	ORDER_DOES_NOT_EXIST = 'ORDER-DOES-NOT-EXIST',
	ADVANCED_PAYMENT_OUTSTANDING = 'ADVANCED-PAYMENT-OUTSTANDING',
	AWC_OUTSTANDING = 'AWC-OUTSTANDING',
	AWC_BLACKLISTED_FRAUD = 'AWC-BLACKLISTED-FRAUD',
	MSR_VIOLATED = 'MSR-VIOLATED',
	ADDRESS_NOT_FOUND = 'ADDRESS-NOT-FOUND',
	ADDRESS_NOT_WITHIN_HSBB = 'ADDRESS-NOT-WITHIN-HSBB',
	BUSINESS_ADDRESS = 'BUSINESS-ADDRESS',
	SPEED_NOT_SUPPORTED = 'SPEED-NOT-SUPPORTED',
	PLAN_CHANGED = 'PLAN-CHANGED',
	ADDRESS_PORT_NOT_AVAILABLE = 'ADDRESS-PORT-NOT-AVAILABLE',
	PORT_NO_HOPE = 'PORT-NO-HOPE',
	NOVA_ORDER_ACCEPTED = 'NOVA-ORDER-ACCEPTED',
	NOVA_ORDER_SUBMITTED = 'NOVA-ORDER-SUBMITTED',
	NOVA_ORDER_PENDING = 'NOVA-ORDER-PENDING',
	NOVA_ORDER_PROCESSING = 'NOVA-ORDER-PROCESSING',
	NOVA_ORDER_CANCELED = 'NOVA-ORDER-CANCELED',
	NOVA_ORDER_COMPLETED = 'NOVA-ORDER-COMPLETED',
	CUSTOMER_DECISION = 'CUSTOMER-DECISION',
	UNIFI_LITE_NOT_AVAILABLE = 'UNIFI-LITE-NOT-AVAILABLE',
	SMART_PARTNERSHIP = 'SMART-PARTNERSHIP',
	GENERAL_ERROR = 'GENERAL-ERROR',
	DEVICE_NOT_ELIGIBLE = 'DEVICE-NOT-ELIGIBLE',
	TRANSFER_REQUEST = 'TRANSFER-REQUEST',
	TROIKA_DUPLICATE = 'TROIKA-DUPLICATE',
	MSR_PROCESSING = 'MSR-PROCESSING',
	TROIKA_CANCELED = 'TROIKA-CANCELED',
	TROIKA_TM_CANCELED = 'TROIKA-TM-CANCELED',
	CUSTOMER_UNREACHABLE = 'CUSTOMER-UNREACHABLE',
	DUPLICATE_ORDER = 'DUPLICATE-ORDER',
	MANUAL_PROCESS_DBS = 'MANUAL-PROCESS-DBS',
	PACKAGE_PERPADUAN_NOT_ELIGIBLE = 'PACKAGE-PERPADUAN-NOT-ELIGIBLE',
	PACKAGE_PERPADUAN_ALREADY_SUBSCRIBED = 'PACKAGE-PERPADUAN-ALREADY-SUBSCRIBED',
	PACKAGE_2G_NOT_SUPPORTED = '2G-NOT-SUPPORTED',
	TROIKA_OFFER_UNIFI_AIR = 'TROIKA-OFFER-UNIFI-AIR',
	TROIKA_NO_UNIFI_AIR = 'TROIKA-NO-UNIFI-AIR',
	TROIKA_UPDATE_RFS = 'TROIKA-UPDATE-RFS',
	TROIKA_PFL_CANCELLED = 'TROIKA-PFL-CANCELLED',
	TROIKA_NI_CANCELLED = 'TROIKA-NI-CANCELLED',
	PROSPECTLIST_CANCELLED = 'PROSPECTLIST-CANCELLED'
}

export enum DemandAction {
	CANCEL = 'CANCEL',
	ATTACHMENT = 'ATTACHMENT',
	REMARK = 'REMARK'
}

export enum DemandStatus {
	SUBMITTED = 'SUBMITTED',
	OPEN = 'OPEN',
	INPROGRESS = 'INPROGRESS',
	POC = 'PENDING ORDER CREATION',
	PFL = 'PENDING FL FEEDBACK',
	CANCEL = 'CANCEL'
}

export enum TemporalNamespaces {
	IJOIN = 'ijoin',
	IRENEW = 'irenew'
}
