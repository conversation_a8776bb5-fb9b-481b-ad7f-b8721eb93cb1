export enum StatusCodeEnum {
	OK = 200,
	CREATED = 201,
	ACCEPTED = 202,
	NO_CONTENT = 204,
	BAD_REQUEST_ERROR = 400,
	UNAUTHORIZED_ERROR = 401,
	FORBIDDEN_ERROR = 403,
	NOT_FOUND_ERROR = 404,
	NOT_ACCEPTABLE_ERROR = 406,
	CONFLICT = 409,
	UNPROCESSABLE_ENTITY = 422,
	LOCKED = 423,
	LIMIT_EXCEEDED_ERROR = 429,
	UE_INTERNAL_SERVER = 500,
	/**
	 * The decimal part is intentionally used to distinguish between different system-level errors that share the same HTTP status code (e.g., 503).
	 * This allows us to identify the specific source of the error while still adhering to the HTTP status code standard.
	 * Currently, we have an issue with the way we display the error name in the response.
	 * The error name is derived from the enum key, which returns the first key that matches the value.
	 * Thus, if we have multiple errors enum with the same value, it will always return the first one.
	 * While this approach isn't ideal, it provides a low-impact solution that minimizes changes across the codebase.
	 * We can revisit and refactor this logic in the future if a more robust or standardized solution is needed. */
	WSO2_ERROR = 503.01,
	OMG_ERROR = 503.02,
	CLOUD_CONNECT_ERROR = 503.04,
	E_COMMERCE_ERROR = 503.05,
	APIM_ERROR = 503.06,
	WERAS_SERVICE_ERROR = 503.07,
	MMAG_ERROR = 503.08,
	TEMPORAL_ERROR = 503.09,
	UNKNOWN_ERROR = 520,
	PAYLOAD_TOO_LARGE = 413
}
