export enum AddOnsCatalogueCategoryEnum {
	SMART_DEVICE = 'SMART DEVICE',
	SME_SMART_DEVICE = 'SME SMART DEVICE',
	SMART_HOME = 'SMART HOME',
	UPB = 'UPB',
	MESH_WIFI = 'MESH WIFI',
	MESH_WIFI_6 = 'MESH WIFI 6',
	BLACKNUT = 'BLACKNUT',
	TV_PACK = 'TV PACK',
	OTT = 'OTT',
	OTT_CHANGE_PLAN = 'OTT CHANGE PLAN',
	OTHERS = 'OTHERS',
	XO = 'XO',
	DEFAULT = 'DEFAULT'
}

export enum AddOnsRequestCategoryEnum {
	SMART_DEVICE = 'SMART DEVICE',
	SME_SMART_DEVICE = 'SME SMART DEVICE',
	SMART_HOME = 'SMART HOME',
	UPB = 'UPB',
	MESH_WIFI = 'MESH WIFI',
	MESH_WIFI_6 = 'MESH WIFI 6',
	BLACKNUT = 'BLACKNUT',
	OTT = 'OTT',
	TV_PACK = 'TV PACK',
	UPBRM0 = 'UPBRM0'
}

export enum AddOnsPrefixCategoryEnum {
	SMART_DEVICE_PREFIX = 'SMD',
	SMART_HOME_PREFIX = 'SMH',
	CLOUD_GAMING_PREFIX = 'CG',
	MESH_WIFI_PREFIX = 'MWA',
	MESH_WIFI_6_PREFIX = 'MW6',
	TV_PREFIX = 'TV',
	BIZ_PREFIX = 'BIZ',
	OTT_PREFIX = 'OTT',
	UPB_PREFIX = 'UTVADS'
}

export enum SubscribedAddOnsTagsEnum {
	TV_PACKS = 'TV Packs',
	STREAMING_APPS = 'Streaming Apps',
	GAMING = 'Gaming',
	LIFESTYLE = 'Lifestyle',
	DEFAULT = 'Devices'
}

export enum TvPackPlanTypeEnum {
	BUNDLE = 'Bundle',
	ALA_CARTE = 'Ala Carte',
	SOFT_BUNDLE = 'Soft Bundle',
	NAKED_BUNDLE = 'Naked Bundle',
	CAMPAIGN_BUNDLE = 'Campaign Bundle'
}

export enum OttCategoryEnum {
	OTT_CUST_CHOICE = 'OttSelectionCustChoice',
	OTT_FIXED = 'OttSelectionFixed',
	OTT_ALA_CARTE = 'OttAlaCarte',
	OTT_NETFLIX = 'OttSelectionNetflix'
}

export enum OttMerchantIdEnum {
	NETFLIX = 14,
	HBO = 55,
	CMGO = 61,
	TVB = 25,
	DISNEY = 38,
	YUPPTV = 15
}

export enum OmgChannelEnum {
	Store = 'Store',
	Telesales = 'Telesales',
	DoorToDoor = 'DoorToDoor',
	Web = 'Web',
	Mobile = 'Mobile',
	STB = 'STB',
	Other = 'Other'
}

export enum NetflixOttTypeEnum {
	NETFLIX_ACTIVATION = 'NETFLIX-ACTIVATION',
	NETFLIX_RECOVERY = 'NETFLIX-RECOVERY',
	NETFLIX_CHANGE_PLAN = 'NETFLIX-CHANGE-PLAN'
}

export enum AccountTypeEnum {
	Broadband = 'Broadband',
	Mobile = 'Mobile'
}

export enum OmgTokenTypeEnum {
	Subscription = 'Subscription',
	Activation = 'Activation',
	AccountRecovery = 'AccountRecovery'
}

export enum OmgResponseCodeEnum {
	OK = '200',
	CREATED = '201',
	BAD_REQUEST = '400',
	FORBIDDEN = '403',
	INTERNAL_SERVER_ERROR = '500',
	AVAILABLE = '000',
	NOT_AVAILABLE = '111',
	EXISTS_ALA_CARTE = '222'
}

export enum ActivationCategoryEnum {
	NETFLIX = 'NETFLIX',
	OTHERS = 'OTHERS'
}

export enum OttPlanTypeEnum {
	BUNDLE = 'Bundle',
	ALA_CARTE = 'Ala Carte'
}

export enum OMGTokenTypeEnum {
	BUNDLE = 'Bundle',
	ACCOUNT_RECOVERY = 'AccountRecovery',
	PROMO = 'Promo',
	INT_PAY = 'IntPay'
}

export enum OmgTokenChannelEnum {
	WEB = 'Web',
	MOBILE = 'Mobile'
}

export enum BuildingTypeEnum {
	HIGH_RISE = 'HIGH RISE',
	LANDED = 'LANDED'
}
