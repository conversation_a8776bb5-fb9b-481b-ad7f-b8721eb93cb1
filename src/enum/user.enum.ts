export enum IdTypeEnum {
	NEW_NRIC = 'New NRIC',
	OLD_NRIC = 'Old NRIC',
	PASSPORT = 'Passport',
	MILITARY = 'Military',
	POLICE = 'Police',
	MYPR = 'MyPR',
	MYKAS = 'MyKAS',
	BRN = 'Business Registration Number',
	NON_BRN = 'Company without BRN'
}

export enum ConsumerIdTypeEnum {
	NEW_NRIC = 'New NRIC',
	OLD_NRIC = 'Old NRIC',
	PASSPORT = 'Passport',
	MILITARY = 'Military',
	POLICE = 'Police',
	MYPR = 'MyPR',
	MYKAS = 'MyKAS'
}

export enum SmeIdTypeEnum {
	BRN = 'Business Registration Number',
	NON_BRN = 'Company without BRN'
}

export enum SmeUserRoleEnum {
	SUPER_ADMIN = 'Super Admin',
	DEPARTMENT_ADMIN = 'Department Admin',
	CUSTOMER_ADMIN = 'Customer Admin',
	USER = 'User'
}

export enum CredentialTypeEnum {
	MOBILE = 'mobile',
	EMAIL = 'email'
}
