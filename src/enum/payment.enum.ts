export enum AutopayReqTypeEnum {
	REGISTRATION = 'Registration',
	TERMINATION = 'Termination',
	MODIFICATION = 'Modification'
}

export enum AutopayRelationshipEnum {
	SELF = 'Self',
	PARENT = 'Parent',
	SPOUSE = 'Spouse',
	CHILDREN = 'Children',
	SIBLINGS = 'Siblings',
	COUSIN = 'Cousin',
	FRIEND = 'Friend',
	GRANDPARENT = 'Grandparent',
	EMPLOYEE = 'Employee',
	EMPLOYER = 'Employer',
	PEER = 'Peer',
	PARTNER = 'Partner',
	RELATIVE = 'Relative'
}

export enum CreditCardTypeEnum {
	VISA = 'Visa',
	MASTERCARD = 'MasterCard',
	AMEX = 'AMEX'
}

export enum OsesPaymentTypeEnum {
	PFM = 'Pay For Me',
	PFL = 'Pay For Linked',
	PFML = 'Pay For Me & Linked',
	PFAP = 'Pay For Anyone - prelogin',
	PFAU = 'Pay For Anyone - postlogin'
}
