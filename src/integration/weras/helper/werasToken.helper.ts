import { getCache, setCache } from '../../../config/cache.config';
import { envConfig } from '../../../config/env.config';
import { CacheKeyEnum } from '../../../enum/cacheKey.enum';
import { StatusCodeEnum } from '../../../enum/statusCode.enum';
import { UE_ERROR } from '../../../middleware/error';
import { fetchApi } from '../../helper/fetchApi.helper';
import type { WerasTokenRes } from './schemas/api/werasToken.schema';

export const getWerasToken = async (useCache = true): Promise<string> => {
	const cacheName = CacheKeyEnum.WERAS_TOKEN;
	const cache = await getCache(cacheName);
	if (cache && useCache) {
		return cache;
	}
	try {
		const url = envConfig().WERAS_TOKEN;
		const body = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({
				grant_type: 'password',
				client_id: Number.parseInt(envConfig().WERAS_CLIENT_ID),
				client_secret: envConfig().WERAS_CLIENT_SECRET,
				username: envConfig().WERAS_USERNAME,
				password: envConfig().WERAS_PASSWORD
			})
		};

		const res = await fetchApi('werasToken', url, body);
		const resBodyText = await res.text();

		if (resBodyText === '' || !res.ok) {
			throw new UE_ERROR(
				`Token API failed with status ${res.status}`,
				StatusCodeEnum.WERAS_SERVICE_ERROR,
				{ integrationId: 'werasToken', response: resBodyText }
			);
		}

		const resBody = JSON.parse(resBodyText) as WerasTokenRes;
		if (!resBody.access_token) {
			throw new UE_ERROR(
				'Invalid token response format',
				StatusCodeEnum.WERAS_SERVICE_ERROR,
				{ integrationId: 'werasToken', response: resBody }
			);
		}

		await setCache(cacheName, resBody.access_token, 86400);
		return resBody.access_token;
	} catch (error) {
		throw new UE_ERROR(
			'Failed to obtain token after three attempts',
			StatusCodeEnum.WERAS_SERVICE_ERROR,
			{
				integrationId: 'werasToken',
				response: error
			}
		);
	}
};
