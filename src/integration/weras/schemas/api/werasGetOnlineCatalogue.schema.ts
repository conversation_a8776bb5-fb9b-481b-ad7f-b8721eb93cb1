import { type Static, t } from 'elysia';

export const werasGetOnlineCatalogueResSchema = t.Object({
	status: t.<PERSON>({
		description: 'Indicates whether the request was successful',
		example: true
	}),
	code: t.Number({
		description: 'HTTP-like status code of the response',
		example: 200
	}),
	data: t.Object({
		category: t.Array(
			t.Object({
				id: t.Number({
					description: 'Unique identifier for the category',
					example: 71
				}),
				name: t.String({
					description: 'Name of the category',
					example: 'Shopping'
				})
			})
		)
	})
});

export type WerasGetOnlineCatalogueRes = Static<
	typeof werasGetOnlineCatalogueResSchema
>;
