import { type Static, t } from 'elysia';

// Schema for redemption_limit object
export const werasRedemptionLimitSchema = t.Object({
	quantity: t.Number(),
	quantity_period: t.String(),
	quantity_used: t.Number(),
	quantity_available: t.Number()
});

// Schema for each stock inside stocks[]
export const werasStockSchema = t.Object({
	id: t.Number(),
	branch_id: t.Number(),
	item_id: t.Number(),
	tier: t.String(), // Confirmed present in WERAS
	type: t.String(),
	quantity: t.Number(),
	quantity_threshold: t.Number(),
	type_threshold: t.String(),
	quantity_used: t.Number(),
	infinite: t.Number(),
	created_by: t.Optional(t.Number()),
	modified_by: t.Number(),
	deleted_by: t.Optional(t.Number()),
	created_at: t.String(),
	updated_at: t.String(),
	deleted_at: t.Optional(t.String())
});

// Schema for each reward item in items[]
export const werasItemSchema = t.Object({
	id: t.Number(),
	name: t.String(),
	description: t.String(),
	category: t.Number(),
	brand: t.Number(),
	inventory_channel: t.String(),
	redemption_flow: t.String(),
	redemption_scope: t.String(),
	game_flag: t.Number(),
	status: t.String(),
	segment: t.String(),
	additional_description: t.Optional(t.String()),
	rm_value: t.Optional(t.Number()),
	point_value: t.Optional(t.Number()),
	code_value: t.Optional(t.String()),
	start_date: t.String(),
	end_date: t.String(),
	expiry_date: t.String(),
	image: t.String(),
	csvfile: t.Optional(t.String()),
	fast_track: t.Number(),
	tnc: t.String(),
	download_url: t.Optional(t.String()),
	highlighted: t.Number(),
	exclude: t.Number(),
	barcode: t.String(),
	qrcode: t.String(),
	display: t.String(),
	created_by: t.Number(),
	modified_by: t.Number(),
	deleted_by: t.Optional(t.Number()),
	created_at: t.String(),
	updated_at: t.String(),
	deleted_at: t.Optional(t.String()),
	campaign: t.Optional(t.String()),
	tier: t.String(),
	stocks: t.Array(werasStockSchema),
	redemption_limit: werasRedemptionLimitSchema,
	barcode_link: t.Optional(t.String()),
	qr_code_link: t.Optional(t.String())
});

// Schema for pagination details object
export const werasPaginationSchema = t.Object({
	per_page: t.Number(),
	current_page: t.Number(),
	total: t.Number(),
	last_page: t.Number()
});

// Schema for data wrapper
export const werasDataSchema = t.Object({
	error: t.Optional(t.String()),
	message: t.Optional(t.String()),
	items: t.Array(werasItemSchema),
	paginationDetails: werasPaginationSchema
});

// Final response schema from WERAS
export const werasGetItemsResSchema = t.Object({
	status: t.Boolean(),
	code: t.Number(),
	data: werasDataSchema,
	redemptionLimit: t.Optional(t.Number())
});

export type WerasGetItemsRes = Static<typeof werasGetItemsResSchema>;
