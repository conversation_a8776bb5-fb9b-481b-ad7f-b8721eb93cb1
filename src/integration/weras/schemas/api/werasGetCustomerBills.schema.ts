import { type Static, t } from 'elysia';

export const werasGetCustomerBillsReqSchema = t.Object({
	customerId: t.Integer(),
	redemptionScope: t.Optional(t.String())
});

export type WerasGetCustomerBillsReq = Static<
	typeof werasGetCustomerBillsReqSchema
>;

export const werasCustomerBillSchema = t.Object({
	id: t.Number(),
	source: t.String(),
	customer_cpc_id: t.String(),
	id_number: t.String(),
	id_type: t.String(),
	account_number: t.String(),
	bill_period: t.Nullable(t.String()),
	bill_number: t.String(),
	bill_date: t.String(),
	outstanding_balance: t.Number(),
	current_month_charge: t.Number(),
	total_due: t.Number(),
	processed_amount: t.Number(),
	payment_due_date: t.String(),
	created_by: t.Number(),
	modified_by: t.Number(),
	created_at: t.String(),
	updated_at: t.String(),
	synced_at: t.Nullable(t.String()),
	account_id: t.Number()
});

export const werasGetCustomerBillsResSchema = t.Object({
	status: t.Boolean(),
	code: t.Number(),
	data: t.Object({
		error: t.Optional(t.String()),
		message: t.Optional(t.String()),
		bill: t.Array(werasCustomerBillSchema)
	})
});

export type WerasCustomerBill = Static<typeof werasCustomerBillSchema>;
export type WerasGetCustomerBillsRes = Static<
	typeof werasGetCustomerBillsResSchema
>;
