import { type Static, t } from 'elysia';

export const werasGetMembershipReqSchema = t.Object({
	id_type: t.String(),
	id_number: t.String(),
	login_scope: t.String(),
	login_source: t.String(),
	action_flag: t.Optional(t.String())
});

export type WerasGetMembershipReq = Static<typeof werasGetMembershipReqSchema>;

export const werasTierInfoSchema = t.Object({
	name: t.Optional(t.String()),
	benefit: t.Optional(t.Array(t.String())),
	banner_url: t.Optional(t.String()),
	bannerh_url: t.Optional(t.String())
});

export type TierInfo = Static<typeof werasTierInfoSchema>;

export const werasPromoInfoSchema = t.Object({
	name: t.Optional(t.String()),
	banner_url: t.Optional(t.String()),
	redirect_url: t.Optional(t.String())
});

export const customerSchema = t.Object({
	id: t.Optional(t.Number()),
	name: t.Optional(t.String()),
	id_number: t.Optional(t.String()),
	contact_number: t.Optional(t.String()),
	email: t.Optional(t.String()),
	weras_member_status: t.Optional(t.String()),
	member_no: t.Optional(t.String()),
	member_category: t.Optional(t.String()),
	loyalty_card_no: t.Optional(t.String()),
	registration_date: t.Optional(t.String()),
	earned_points: t.Optional(t.Number()),
	redeemed_points: t.Optional(t.Number()),
	expired_points: t.Optional(t.Number()),
	member_status: t.Optional(t.String()),
	points_expiry: t.Optional(t.String()),
	redirect_url: t.Optional(t.String()),
	points_expired_thismonth: t.Optional(t.Number()),
	available_points: t.Optional(t.Number()),
	game: t.Optional(t.String()),
	banner_url: t.Optional(t.String()),
	game_url: t.Optional(t.String()),
	tier: t.Optional(t.String()),
	tier_previous: t.Optional(t.String())
});

export const werasMembershipDataSchema = t.Object({
	customer: t.Optional(customerSchema),
	tier_info: t.Optional(t.Array(werasTierInfoSchema)),
	promo_info: t.Optional(t.Array(werasPromoInfoSchema))
});

export const werasGetMembershipResSchema = t.Object({
	status: t.Boolean(),
	code: t.Number(),
	data: t.Optional(werasMembershipDataSchema)
});

export type WerasGetMembershipRes = Static<typeof werasGetMembershipResSchema>;
