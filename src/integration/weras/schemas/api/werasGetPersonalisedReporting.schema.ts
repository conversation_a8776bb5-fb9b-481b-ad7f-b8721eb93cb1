import { type Static, t } from 'elysia';

export const werasGetPersonalisedReportingReqSchema = t.Object({
	customer_id: t.Number(),
	from_date: t.String(),
	to_date: t.String()
});

export type WerasGetPersonalisedReportingReq = Static<
	typeof werasGetPersonalisedReportingReqSchema
>;

export const werasGetPersonalisedReportingResSchema = t.Object({
	status: t.<PERSON>({
		description: 'Indicates whether the request was successful',
		example: true
	}),
	code: t.Number({
		description: 'HTTP-like status code of the response',
		example: 200
	}),
	data: t.Array(
		t.Object({
			points: t.Object({
				total_available: t.Number({
					description: 'Total points available',
					example: 244
				}),
				total_earned: t.Number({
					description: 'Total points earned',
					example: 2144
				}),
				total_used: t.Number({
					description: 'Total points used',
					example: 1900
				}),
				redeemed_in_date_range: t.Number({
					description: 'Points redeemed within the specified date range',
					example: 1900
				})
			}),
			redeemed_points_in_date_range_RM: t.Number({
				description:
					'Monetary value (RM) of points redeemed in the specified date range',
				example: 19
			}),
			bill_rebate_points: t.Number({
				description: 'Points used for bill rebates',
				example: 200
			}),
			bill_rebate_RM: t.Number({
				description: 'Monetary value (RM) of bill rebates',
				example: 2
			}),
			voucher_redeemed_count: t.Number({
				description: 'Number of vouchers redeemed',
				example: 1
			}),
			voucher_save_amount_RM: t.Number({
				description: 'Total monetary savings (RM) from redeemed vouchers',
				example: 0
			}),
			donation_redeemed_points: t.Number({
				description: 'Points donated',
				example: 0
			}),
			donation_redeemed_RM: t.Number({
				description: 'Monetary value (RM) of donated points',
				example: 0
			})
		})
	)
});

export type WerasGetPersonalisedReportingRes = Static<
	typeof werasGetPersonalisedReportingResSchema
>;
