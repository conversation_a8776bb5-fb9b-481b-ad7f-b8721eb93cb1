import fetchRetry from 'fetch-retry';
import { pinoLog } from '../../config/pinoLog.config';
import { StatusCodeEnum } from '../../enum/statusCode.enum';
import { UE_ERROR } from '../../middleware/error';
import { getWerasToken } from '../weras/helper/werasToken.helper';
import { getApimToken } from '../wso2/helper/apimToken.helper';

const fetchWithNative = fetchRetry(global.fetch as typeof fetch);
type FlexibleRequestInit = Omit<RequestInit, 'body'> & {
	body?: BodyInit | FormData | null;
};

const generateCurlCommand = (url: string, req: FlexibleRequestInit) => {
	const method = req.method || 'GET';
	const headers = req.headers
		? Object.entries(req.headers as Record<string, string>)
				.map(([key, value]) => `-H "${key}: ${value}"`)
				.join(' ')
		: '';

	const body =
		req.body && typeof req.body === 'string'
			? `--data '${req.body.replace(/'/g, "'\\''")}'`
			: '';

	return `curl -X ${method} ${headers} ${body} "${url}"`;
};

export const fetchApi = async (
	integrationId: string,
	url: string,
	req: FlexibleRequestInit,
	options: { isEnabledErrorException?: boolean; retries?: number } = {}
): Promise<Response> => {
	const { isEnabledErrorException = true, retries = 3 } = options;

	const curlCommand = generateCurlCommand(url, req);

	pinoLog.info({
		integrationId,
		url,
		curl: curlCommand
	});

	const res = await fetchWithNative(url, {
		...req,
		retryDelay: 1000,
		retryOn: async (
			attempt: number,
			error: unknown,
			response: Response | null
		) => {
			if (
				(error || (response && response.status >= 400)) &&
				attempt < retries
			) {
				const resClone = response?.clone();
				const errorRes = await resClone?.text();
				pinoLog.error({
					integrationId,
					attempt,
					url,
					status: response?.statusText,
					code: response?.status,
					curl: curlCommand,
					error: JSON.stringify(errorRes)
				});

				// retry APIM token
				if (
					response &&
					response.status === 401 &&
					(response.headers
						.get('X-Forwarded-Host')
						?.includes('api.apigate.tm.com.my') ||
						response.headers
							.get('X-Forwarded-Host')
							?.includes('apigw.dev.tmoip.tm.com.my')) &&
					errorRes?.toLowerCase().includes('invalid credentials')
				) {
					const newToken = await getApimToken(false);

					if (req.headers && 'Authorization' in req.headers) {
						req.headers.Authorization = `Bearer ${newToken}`;
						pinoLog.info(
							`New APIM token has been set at ${new Date()}: ${newToken}`
						);
					}
				}

				// retry weras token
				if (
					response &&
					response.status === 401 &&
					(response.headers
						.get('X-Forwarded-Host')
						?.includes('rewardsstg.unifi.com.my') ||
						response.headers
							.get('X-Forwarded-Host')
							?.includes('rewardssit.unifi.com.my') ||
						response.headers
							.get('X-Forwarded-Host')
							?.includes('rewards.unifi.com.my'))
				) {
					const newToken = await getWerasToken(false);

					if (req.headers && 'Authorization' in req.headers) {
						req.headers.Authorization = `Bearer ${newToken}`;
						pinoLog.info(
							`New weras token has been set at ${new Date()}: ${newToken}`
						);
					}
				}

				return true;
			}

			return false;
		}
	});
	const resClone = res.clone();
	const resBody = await resClone.text();
	try {
		pinoLog.info({
			integrationId,
			url,
			status: res.statusText,
			code: res.status,
			req: JSON.stringify(req),
			res: resBody,
			curl: curlCommand
		});
		return res;
	} catch (error) {
		if (isEnabledErrorException) {
			throw new UE_ERROR(String(error), StatusCodeEnum.UNPROCESSABLE_ENTITY, {
				integrationId
			});
		}
		return res;
	}
};
