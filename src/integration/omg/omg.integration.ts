import { envConfig } from '../../config/env.config';
import { StatusCodeEnum } from '../../enum/statusCode.enum';
import { UE_ERROR } from '../../middleware/error';
import { fetchApi } from '../helper/fetchApi.helper';
import type {
	OmgActivateOttReq,
	OmgActivateOttRes
} from './schemas/api/omgActivateOtt.schema';
import type { OmgBodyReq } from './schemas/api/omgBase.schema';
import type {
	OmgDisneyChangeMobileReq,
	OmgDisneyChangeMobileRes
} from './schemas/api/omgDisneyChangeMobile.schema';
import type {
	OmgGetNetflixAccRecoveryUrlRes,
	OmgNetflixAccRecoveryUrlReq
} from './schemas/api/omgNetflixAccountRecovery.schema';
import type {
	OmgNetflixActivationUrlReq,
	OmgNetflixActivationUrlRes
} from './schemas/api/omgNetflixActivation.schema';
import type {
	OmgNetflixCancelPlanReq,
	OmgNetflixCancelPlanRes
} from './schemas/api/omgNetflixCancelPlan.schema';
import type {
	OmgNetflixChangePlanReq,
	OmgNetflixChangePlanRes
} from './schemas/api/omgNetflixChangePlan.schema';
import type {
	OmgGetCurrentNetflixPlanReq,
	OmgGetCurrentNetflixPlanRes
} from './schemas/api/omgNetflixCurrentPlan.schema';
import type {
	OmgGetOttActivationAlaCarteUrlReq,
	OmgGetOttActivationAlaCarteUrlRes,
	OmgGetOttActivationBundleUrlReq,
	OmgGetOttActivationBundleUrlRes
} from './schemas/api/omgOttActivation.schema';
import type {
	OmgOttEntitlementReq,
	OmgOttEntitlementRes
} from './schemas/api/omgOttEntitlement.schema';
import type {
	OmgGetOttSubscriptionReq,
	OmgGetOttSubscriptionRes
} from './schemas/api/omgOttSubscription.schema';
import type {
	OmgOttSwapOrderReq,
	OmgOttSwapOrderRes
} from './schemas/api/omgSwapOtt.schema';
import type {
	OmgOttUserVerifyReq,
	OmgOttUserVerifyRes
} from './schemas/api/omgVerifyOttUserId.schema';

class OmgIntegration {
	private integrationId: string;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
	}

	async getOmgGetOttSubscription(
		bodyRequest: OmgGetOttSubscriptionReq,
		isEnabledErrorException = true
	): Promise<OmgGetOttSubscriptionRes> {
		const url: string = envConfig().OMG_GET_OTT_SUBSCRIPTION;
		const xApiKey: string = envConfig().OMG_X_API_KEY;
		const body: OmgBodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'X-ApiKey': xApiKey
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body, {
			isEnabledErrorException
		});
		const resBody: OmgGetOttSubscriptionRes = await res.json().catch(error => {
			if (isEnabledErrorException) {
				throw new UE_ERROR(
					'Failed to parse JSON response',
					StatusCodeEnum.OMG_ERROR,
					{ integrationId: this.integrationId, response: String(error) }
				);
			}
			return null;
		});

		if (!res.ok) {
			if (isEnabledErrorException) {
				throw new UE_ERROR(
					'OMG Get Subscription Throw Error',
					StatusCodeEnum.OMG_ERROR,
					{
						integrationId: this.integrationId
					}
				);
			}
			return null;
		}
		return resBody;
	}

	async getOmgOttEntitlement(
		bodyRequest: OmgOttEntitlementReq
	): Promise<OmgOttEntitlementRes> {
		const url: string = envConfig().OMG_GET_OTT_ENTITLEMENT;
		const xApiKey: string = envConfig().OMG_X_API_KEY;
		const body: OmgBodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'X-ApiKey': xApiKey
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody: OmgOttEntitlementRes = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.OMG_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'OMG Get OTT Entitlement Throw Error',
				StatusCodeEnum.OMG_ERROR,
				{
					integrationId: this.integrationId
				}
			);
		}
		return resBody;
	}

	async getCurrentNetflixPlan(
		bodyRequest: OmgGetCurrentNetflixPlanReq
	): Promise<OmgGetCurrentNetflixPlanRes> {
		const url: string = envConfig().OMG_NETFLIX_GET_PLAN;
		const xApiKey: string = envConfig().OMG_X_API_KEY;
		const body: OmgBodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'X-ApiKey': xApiKey
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody: OmgGetCurrentNetflixPlanRes = await res
			.json()
			.catch(error => {
				throw new UE_ERROR(
					'Failed to parse JSON response',
					StatusCodeEnum.OMG_ERROR,
					{ integrationId: this.integrationId, response: String(error) }
				);
			});

		if (!res.ok) {
			throw new UE_ERROR(
				'OMG Get Netflix Plan Throw Error',
				StatusCodeEnum.OMG_ERROR,
				{
					integrationId: this.integrationId
				}
			);
		}
		return resBody;
	}

	async changeNetflixPlan(
		bodyRequest: OmgNetflixChangePlanReq
	): Promise<OmgNetflixChangePlanRes> {
		const url: string = envConfig().OMG_NETFLIX_CHANGE_PLAN_ORDER;
		const xApiKey: string = envConfig().OMG_X_API_KEY;
		const body: OmgBodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'X-ApiKey': xApiKey
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody: OmgNetflixChangePlanRes = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.OMG_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});
		return resBody;
	}

	async omgCancelNetflixPlan(
		bodyRequest: OmgNetflixCancelPlanReq
	): Promise<OmgNetflixCancelPlanRes> {
		const url: string = envConfig().OMG_NETFLIX_CANCEL;
		const xApiKey: string = envConfig().OMG_X_API_KEY;
		const body: OmgBodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'X-ApiKey': xApiKey
			},
			body: JSON.stringify(bodyRequest)
		};

		const res = await fetchApi(this.integrationId, url, body);
		const resBody: OmgNetflixCancelPlanRes = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.OMG_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		return resBody;
	}

	async getOmgOttUserVerify(
		bodyRequest: OmgOttUserVerifyReq
	): Promise<OmgOttUserVerifyRes> {
		const url: string = envConfig().OMG_VERIFY_OTT_USER_ID;
		const xApiKey: string = envConfig().OMG_X_API_KEY;

		const body: OmgBodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'X-ApiKey': xApiKey
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody: OmgOttUserVerifyRes = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.OMG_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'OMG Verify OTT User ID Throw Error',
				StatusCodeEnum.OMG_ERROR,
				{
					integrationId: this.integrationId
				}
			);
		}

		return resBody;
	}

	async omgOttOrder(
		bodyRequest: OmgActivateOttReq,
		isEnabledErrorException = true
	): Promise<OmgActivateOttRes> {
		const url: string = envConfig().OMG_NEW_OTT_ORDER;
		const xApiKey: string = envConfig().OMG_X_API_KEY;
		const body: OmgBodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'X-ApiKey': xApiKey
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body, {
			isEnabledErrorException
		});
		const resBody: OmgActivateOttRes = await res.json();

		if (resBody.responseCode !== '201') {
			throw new UE_ERROR('OMG Throw Error', StatusCodeEnum.OMG_ERROR, {
				integrationId: this.integrationId,
				response: resBody
			});
		}

		return resBody as OmgActivateOttRes;
	}

	async omgOttSwapOrder(
		bodyRequest: OmgOttSwapOrderReq,
		isEnabledErrorException = true
	): Promise<OmgOttSwapOrderRes> {
		const url: string = envConfig().OMG_NEW_OTT_SWAP_ORDER;
		const xApiKey: string = envConfig().OMG_X_API_KEY;
		const body: OmgBodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'X-ApiKey': xApiKey
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body, {
			isEnabledErrorException
		});
		const resBody: OmgOttSwapOrderRes = await res.json();

		if (resBody.responseCode !== '201') {
			throw new UE_ERROR('OMG Throw Error', StatusCodeEnum.OMG_ERROR, {
				integrationId: this.integrationId,
				response: resBody
			});
		}

		return resBody as OmgOttSwapOrderRes;
	}

	async getOmgOttActivationBundleUrl(
		bodyRequest: OmgGetOttActivationBundleUrlReq
	): Promise<OmgGetOttActivationBundleUrlRes> {
		const url: string = envConfig().OMG_HBO_GET_ACTIVATION_BUNDLE_URL;
		const xApiKey: string = envConfig().OMG_X_API_KEY;
		const body: OmgBodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'X-ApiKey': xApiKey
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody: OmgGetOttActivationBundleUrlRes = await res
			.json()
			.catch(error => {
				throw new UE_ERROR(
					'Failed to parse JSON response',
					StatusCodeEnum.OMG_ERROR,
					{ integrationId: this.integrationId, response: String(error) }
				);
			});

		if (!res.ok) {
			throw new UE_ERROR(
				'OMG Get OTT Activation Bundle URL Throw Error',
				StatusCodeEnum.OMG_ERROR,
				{
					integrationId: this.integrationId
				}
			);
		}
		return resBody;
	}

	async getOmgOttActivationAlaCarteUrl(
		bodyRequest: OmgGetOttActivationAlaCarteUrlReq
	): Promise<OmgGetOttActivationAlaCarteUrlRes> {
		const url: string = envConfig().OMG_HBO_GET_ACTIVATION_ALA_CARTE_URL;
		const xApiKey: string = envConfig().OMG_X_API_KEY;
		const body: OmgBodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'X-ApiKey': xApiKey
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody: OmgGetOttActivationAlaCarteUrlRes = await res
			.json()
			.catch(error => {
				throw new UE_ERROR(
					'Failed to parse JSON response',
					StatusCodeEnum.OMG_ERROR,
					{ integrationId: this.integrationId, response: String(error) }
				);
			});
		if (!res.ok) {
			throw new UE_ERROR(
				'OMG Get OTT Activation Ala Carte URL Throw Error',
				StatusCodeEnum.OMG_ERROR,
				{
					integrationId: this.integrationId
				}
			);
		}
		return resBody;
	}

	async getOmgNetflixActivationUrl(
		bodyRequest: OmgNetflixActivationUrlReq
	): Promise<OmgNetflixActivationUrlRes> {
		const url: string = envConfig().OMG_NETFLIX_GET_TOKEN;
		const xApiKey: string = envConfig().OMG_X_API_KEY;
		const body: OmgBodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'X-ApiKey': xApiKey
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody: OmgNetflixActivationUrlRes = await res
			.json()
			.catch(error => {
				throw new UE_ERROR(
					'Failed to parse JSON response',
					StatusCodeEnum.OMG_ERROR,
					{ integrationId: this.integrationId, response: String(error) }
				);
			});

		if (!res.ok) {
			throw new UE_ERROR(
				'OMG Get Netflix Activation URL Throw Error',
				StatusCodeEnum.OMG_ERROR,
				{
					integrationId: this.integrationId
				}
			);
		}
		return resBody;
	}

	async getNetflixAccRecoveryToken(
		bodyRequest: OmgNetflixAccRecoveryUrlReq
	): Promise<OmgGetNetflixAccRecoveryUrlRes> {
		const url: string = envConfig().OMG_NETFLIX_GET_TOKEN;
		const xApiKey: string = envConfig().OMG_X_API_KEY;

		const body: OmgBodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'X-ApiKey': xApiKey
			},
			body: JSON.stringify(bodyRequest)
		};

		const res = await fetchApi(this.integrationId, url, body);
		const resBody: OmgGetNetflixAccRecoveryUrlRes = await res
			.json()
			.catch(error => {
				throw new UE_ERROR(
					'Failed to parse JSON response',
					StatusCodeEnum.OMG_ERROR,
					{ integrationId: this.integrationId, response: String(error) }
				);
			});

		return resBody;
	}

	async omgGetDisneyChangeMobile(
		bodyRequest: OmgDisneyChangeMobileReq
	): Promise<OmgDisneyChangeMobileRes> {
		const url: string = envConfig().OMG_DISNEY_CHANGE_MOBILE_NO;
		const xApiKey: string = envConfig().OMG_X_API_KEY;
		const body: OmgBodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'X-ApiKey': xApiKey
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody: OmgDisneyChangeMobileRes = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.OMG_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (
			!resBody ||
			resBody.responseCode === '500' ||
			resBody.responseCode === '400'
		) {
			throw new UE_ERROR('OMG Throw Error', StatusCodeEnum.OMG_ERROR, {
				integrationId: this.integrationId,
				response: resBody
			});
		}

		return resBody as OmgDisneyChangeMobileRes;
	}
}

export default OmgIntegration;
