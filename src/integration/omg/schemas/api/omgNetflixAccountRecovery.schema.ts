import { type Static, t } from 'elysia';
import {
	AccountTypeEnum,
	OmgChannelEnum,
	OmgTokenTypeEnum
} from '../../../../enum/addOns.enum';

export const omgNetflixAccRecoveryUrlReqSchema = t.Object({
	accountType: t.Enum(AccountTypeEnum, {
		description: 'Type of account (Broadband/Mobile)'
	}),
	accountId: t.String({ description: 'User’s account ID' }),
	tokenRefNo: t.String({
		description: 'Unique reference number for token request'
	}),
	tokenType: t.Enum(OmgTokenTypeEnum, { description: 'Type of token request' }),
	tokenChannel: t.Enum(OmgChannelEnum, {
		description: 'Channel used for token request'
	}),
	tokenErrorURL: t.String({
		description: 'URL to redirect if an error occurs'
	})
});

export type OmgNetflixAccRecoveryUrlReq = Static<
	typeof omgNetflixAccRecoveryUrlReqSchema
>;

export const omgNetflixAccRecoveryUrlResSchema = t.Object({
	responseCode: t.String({ description: 'Response code from OMG API' }),
	responseMsg: t.String({ description: 'Response message from OMG API' }),
	tokenURL: t.String({ description: 'Netflix account recovery URL' }),
	tokenExpiry: t.String({
		description: 'The expiry date and time of the retrieved Netflix token.',
		example: '03/26/2023 06:20:01 PM'
	})
});

export type OmgGetNetflixAccRecoveryUrlRes = Static<
	typeof omgNetflixAccRecoveryUrlResSchema
>;
