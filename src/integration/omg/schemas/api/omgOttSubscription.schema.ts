import { type Static, t } from 'elysia';

export const omgGetOttSubscriptionReqSchema = t.Object({
	accountType: t.String(),
	accountId: t.String()
});

export type OmgGetOttSubscriptionReq = Static<
	typeof omgGetOttSubscriptionReqSchema
>;

export const omgOttSubscribedResSchema = t.Object({
	ottMerchantId: t.Number(),
	ottOmgId: t.Number(),
	ottName: t.String(),
	ottTxnId: t.Number(),
	ottStartDate: t.String(),
	ottExpiryDate: t.String(),
	ottStatus: t.String(),
	ottLoginType: t.String(),
	ottUserId: t.String(),
	ottUniversalLink: t.String(),
	ottIconPath: t.String(),
	swapAvailable: t.String(),
	swapped: t.String(),
	swappedOttTxnId: t.Number(),
	swapMode: t.String(),
	swapOttExpiryDate: t.String(),
	swapOttStartDate: t.String(),
	allowDisneyUpgrade: t.String(),
	allowMaxUpgrade: t.String(),
	netflixTxnId: t.String(),
	allowNetflixCancel: t.String()
});

export type OmgOttSubscribedRes = Static<typeof omgOttSubscribedResSchema>;

export const omgPlanSubscribedResSchema = t.Array(
	t.Object({
		ottPlanId: t.String(),
		ottPlanName: t.String(),
		ottSubscribed: t.Array(omgOttSubscribedResSchema)
	})
);

export type OmgPlanSubscribedRes = Static<typeof omgPlanSubscribedResSchema>;

export const omgGetOttSubscriptionResSchema = t.Nullable(
	t.Object({
		responseCode: t.String(),
		responseMsg: t.String(),
		accountType: t.String(),
		accountId: t.String(),
		planSubscribed: omgPlanSubscribedResSchema
	})
);

export type OmgGetOttSubscriptionRes = Static<
	typeof omgGetOttSubscriptionResSchema
>;
