import { type Static, t } from 'elysia';

export const omgGetCurrentNetflixPlanReqSchema = t.Object({
	accountType: t.Enum({
		Broadband: 'Broadband',
		Mobile: 'Mobile'
	}),
	accountId: t.String()
});

export type OmgGetCurrentNetflixPlanReq = Static<
	typeof omgGetCurrentNetflixPlanReqSchema
>;

export const omgGetCurrentNetflixPlanResSchema = t.Object({
	responseCode: t.String(),
	responseMsg: t.String(),
	netflixCurrentPlan: t.Nullable(
		t.Array(
			t.Object({
				netflixPAI: t.String(),
				netflixBundleId: t.String(),
				netflixOfferId: t.String(),
				TMbundleId: t.String()
			})
		)
	)
});

export type OmgGetCurrentNetflixPlanRes = Static<
	typeof omgGetCurrentNetflixPlanResSchema
>;
