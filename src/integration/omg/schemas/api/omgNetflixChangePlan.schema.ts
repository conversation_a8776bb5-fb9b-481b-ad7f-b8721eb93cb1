import { type Static, t } from 'elysia';
import { OmgChannelEnum } from '../../../../enum/addOns.enum';

export const omgNetflixChangePlanRequestSchema = t.Object({
	accountType: t.Enum({
		Broadband: 'Broadband',
		Mobile: 'Mobile'
	}),
	accountId: t.String(),
	netflixPAI: t.String(),
	netflixBundleId: t.String(),
	netflixBillingEventId: t.String(),
	netflixNewOfferId: t.String(),
	channel: t.Enum(
		Object.fromEntries(Object.values(OmgChannelEnum).map(v => [v, v]))
	),
	TMnewBundleId: t.String()
});

export type OmgNetflixChangePlanReq = Static<
	typeof omgNetflixChangePlanRequestSchema
>;

export const omgNetflixChangePlanResponseSchema = t.Object({
	responseCode: t.Enum({
		OK: '200',
		CREATED: '201',
		BAD_REQUEST: '400',
		FORBIDDEN: '403',
		INTERNAL_SERVER_ERROR: '500',
		AVAILABLE: '000',
		NOT_AVAILABLE: '111',
		EXISTS_ALA_CARTE: '222'
	}),
	responseMsg: t.String()
});

export type OmgNetflixChangePlanRes = Static<
	typeof omgNetflixChangePlanResponseSchema
>;
