import { type Static, t } from 'elysia';

const freeUPBDetailsSchema = t.Object({
	deviceDescription: t.Optional(t.String()),
	deviceImage: t.Optional(t.String()),
	price: t.Optional(t.String())
});

const deviceListSchema = t.Object({
	deviceName: t.String(),
	price: t.String(),
	quantity: t.Number()
});

const NotificationEmailRequestAddonSchema = t.Object({
	CustName: t.String(),
	CustContactNo: t.String(),
	BillingContactNo: t.String(),
	CustEmail: t.String(),
	BillingEmail: t.String(),
	OrderNumber: t.String(),
	ProductName: t.Optional(t.String()),
	PlanName: t.Optional(t.String()),
	Quantity: t.Optional(t.Number()),
	Price: t.Optional(t.String()),
	billingAddress1: t.Optional(t.String()),
	billingAddress2: t.Optional(t.String()),
	billingCity: t.Optional(t.String()),
	billingPostcode: t.Optional(t.String()),
	billingState: t.Optional(t.String()),
	country: t.Optional(t.String()),
	ShippingCustName: t.Optional(t.String()),
	shippingAddress1: t.Optional(t.String()),
	shippingAddress2: t.Optional(t.String()),
	shippingCity: t.Optional(t.String()),
	shippingPostcode: t.Optional(t.String()),
	shippingState: t.Optional(t.String()),
	tvPackName: t.Optional(t.String()),
	iptvId: t.Optional(t.String()),
	freeUPBDetails: t.Optional(freeUPBDetailsSchema),
	tvPackImage: t.Optional(t.String()),
	ControllerAddOn: t.Optional(t.String()),
	appDate: t.Optional(t.String()),
	deviceList: t.Optional(t.Array(deviceListSchema))
});

export type NotificationEmailRequestAddon = Static<
	typeof NotificationEmailRequestAddonSchema
>;
