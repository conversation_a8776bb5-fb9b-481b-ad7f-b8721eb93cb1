import { fetchApi } from '../helper/fetchApi.helper';
import type { OsesReceiptEmailTemplateReq } from './schemas/api/osesTemplate.schema';
import type {
	DisneyCheckChangeMobileEmailTemplateReq,
	OttNotificationEmailRequest
} from './schemas/api/ottEmailTemplate.schema';
import type { NotificationEmailRequestAddon } from './schemas/api/sendEmailNotification';

class EmailIntegration {
	integrationId: string;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
	}

	async getEmailBodyTemplate(url: string): Promise<string> {
		const body = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			}
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.text();

		if (!res.ok) {
			return '';
		}
		return resBody;
	}

	async getEmailBodyTemplateWithBodyRequest(
		url: string,
		bodyRequest:
			| OsesReceiptEmailTemplateReq
			| OttNotificationEmailRequest
			| DisneyCheckChangeMobileEmailTemplateReq
			| NotificationEmailRequestAddon
	): Promise<string> {
		const body = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.text();
		if (!res.ok) {
			return '';
		}
		return resBody;
	}
}

export default EmailIntegration;
