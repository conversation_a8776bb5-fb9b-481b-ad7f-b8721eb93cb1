import { type Static, t } from 'elysia';

const wso2SSMInfoReqSchema = t.Object({
	CheckSSMInfoRequest: t.Object({
		CheckSSMInfoReq: t.Object({
			SupportingDocumentFlag: t.String(),
			Platform: t.String(),
			EaiId: t.String(),
			SSMType: t.String(),
			SSMId: t.String(),
			LoginId: t.String()
		})
	})
});

export type Wso2SSMInfoReq = Static<typeof wso2SSMInfoReqSchema>;

export const wso2SSMInfoResSchema = t.Object({
	CheckSSMInfoResponse: t.Optional(
		t.Object({
			CheckSSMInfoRes: t.Object({
				ReferenceNumber: t.Optional(t.String()),
				LastUpdateSSM: t.Optional(t.String()),
				CompanyName: t.Optional(t.String()),
				SSMStatus: t.Optional(t.String()),
				SSMId: t.Optional(t.String()),
				StatusCode: t.Optional(t.String()),
				ListOfOwners: t.Optional(
					t.Array(
						t.Object({
							Name: t.Optional(t.String()),
							Id: t.Optional(t.String())
						})
					)
				),
				ListOfDirectors: t.Optional(
					t.Array(
						t.Object({
							Name: t.Optional(t.String()),
							Id: t.Optional(t.String())
						})
					)
				),
				ListOfAddresses: t.Array(
					t.Object({
						AddressType: t.Optional(t.String()),
						AddressLine1: t.MaybeEmpty(t.String()),
						AddressLine2: t.MaybeEmpty(t.String()),
						AddressLine3: t.MaybeEmpty(t.String()),
						Town: t.MaybeEmpty(t.String()),
						State: t.MaybeEmpty(t.String()),
						PostalCode: t.MaybeEmpty(t.String())
					})
				)
			})
		})
	),
	faultMessage: t.MaybeEmpty(
		t.Object({
			code: t.String(),
			text: t.String(),
			detailedText: t.String(),
			severity: t.String(),
			type: t.String()
		})
	)
});

export type Wso2SSMInfoRes = Static<typeof wso2SSMInfoResSchema>;
