import { type Static, t } from 'elysia';

export const wso2CTTCheckEligibilityResSchema = t.Object({
	RetrieveUnclaimedCTT: t.Array(
		t.MaybeEmpty(
			t.Object({
				Area: t.MaybeEmpty(t.String()),
				SRNumber: t.<PERSON>(t.String()),
				ServiceID: t.<PERSON>(t.String()),
				AccountNo: t.Maybe<PERSON>(t.String())
			})
		)
	)
});

export type Wso2CTTCheckEligibilityRes = Static<
	typeof wso2CTTCheckEligibilityResSchema
>;
