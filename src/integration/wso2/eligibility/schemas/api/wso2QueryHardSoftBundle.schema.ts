import { type Static, t } from 'elysia';

const wso2QueryHardSoftBundleReqSchema = t.Object({
	pmeNumber: t.String()
});

export type Wso2QueryHardSoftBundleReq = Static<
	typeof wso2QueryHardSoftBundleReqSchema
>;

export const wso2QueryHardSoftBundleResSchema = t.Object({
	responseCode: t.Number(),
	responseMsg: t.String(),
	datalist: t.Array(
		t.Object({
			promotion: t.String(),
			partNoPromo: t.String(),
			promoMinQty: t.Number(),
			promoDfltQty: t.Number(),
			promoMaxQty: t.Number(),
			moli: t.String(),
			partNoMoli: t.String(),
			moliMinQty: t.Nullable(t.Number()),
			moliDfltQty: t.Number(),
			moliMaxQty: t.Nullable(t.Number()),
			relationship: t.String(),
			oli: t.String(),
			partNoOli: t.String(),
			oliMinQty: t.Nullable(t.Number()),
			oliDfltQty: t.Number(),
			oliMaxQty: t.Nullable(t.Number())
		})
	)
});

export type Wso2QueryHardSoftBundleRes = Static<
	typeof wso2QueryHardSoftBundleResSchema
>;
