import { type Static, t } from 'elysia';

const wso2TmDeviceBundleObjSchema = t.Object({
	Id: t.Optional(t.String()),
	TMActiveFlag: t.String(),
	TMBundleName: t.String(),
	TMContractMonth: t.Optional(t.String()),
	TMDeviceName: t.Optional(t.String()),
	TMDeviceOfferingGrp: t.Optional(t.String()),
	TMDevicePrice: t.Optional(t.String()),
	TMLeasingType: t.Optional(t.String()),
	TMMRC: t.Optional(t.String()),
	TMPMEConfigCalc: t.Optional(t.String()),
	TMPartnerID: t.Optional(t.String()),
	TMPurchasePrice: t.Optional(t.String()),
	TMIsSmartDevice: t.Optional(t.String()),
	TMDeliveryPartner: t.Optional(t.String())
});

const wso2DeviceBundleReqSchema = t.Object({
	DeviceBundleRetrieveRequest: t.Object({
		TmDeviceBundle: wso2TmDeviceBundleObjSchema
	})
});

export type Wso2DeviceBundleReq = Static<typeof wso2DeviceBundleReqSchema>;

export const wso2DeviceBundleResSchema = t.Nullable(
	t.Object({
		Response: t.Nullable(
			t.Object({
				DeviceBundleRetrieveResponse: t.Nullable(
					t.Object({
						TmDeviceBundle: t.Array(wso2TmDeviceBundleObjSchema)
					})
				)
			})
		)
	})
);

export type Wso2DeviceBundleRes = Static<typeof wso2DeviceBundleResSchema>;
