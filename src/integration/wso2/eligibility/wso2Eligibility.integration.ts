import { getCache, setCache } from '../../../config/cache.config';
import { envConfig } from '../../../config/env.config';
import { CacheKeyEnum } from '../../../enum/cacheKey.enum';
import { StatusCodeEnum } from '../../../enum/statusCode.enum';
import { UE_ERROR } from '../../../middleware/error';
import { fetchApi } from '../../helper/fetchApi.helper';
import { getApimToken } from '../helper/apimToken.helper';
import type { Wso2BodyReq } from '../helper/schemas/api/wso2Base.schema';
import type {
	Wso2CustomerProfileCheckReq,
	Wso2CustomerProfileCheckRes
} from './schemas/api/wso2CustomerProfileCheck.schema';
import type {
	Wso2EdwhMoanaReq,
	Wso2EdwhMoanaRes
} from './schemas/api/wso2EdwhMoana.schema';
import type {
	Wso2CheckOpenTransferReq,
	Wso2CheckOpenTransferRes
} from './schemas/api/wso2OpenTransferReq.schema';
import type {
	Wso2QueryHardSoftBundleReq,
	Wso2QueryHardSoftBundleRes
} from './schemas/api/wso2QueryHardSoftBundle.schema';
import type { Wso2CTTCheckEligibilityRes } from './schemas/api/wso2Rebate.schema';
import type {
	Wso2DeviceBundleReq,
	Wso2DeviceBundleRes
} from './schemas/api/wso2RetrieveBundleDevice.schema';
import type {
	Wso2SSMInfoReq,
	Wso2SSMInfoRes
} from './schemas/api/wso2SSMInfo.schema';

class Wso2EligibilityIntegration {
	integrationId: string;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
	}

	async getWso2CustomerProfileCheck(
		bodyRequest: Wso2CustomerProfileCheckReq
	): Promise<Wso2CustomerProfileCheckRes> {
		const cacheName = `${CacheKeyEnum.WSO2_CUSTOMER_PROFILE_CHECK}-${bodyRequest.CustomerProfileCheckRequest.IdValue}`;
		const cache = await getCache(cacheName);
		if (cache) {
			return JSON.parse(cache) as Wso2CustomerProfileCheckRes;
		}
		const url: string = envConfig().WSO2_CUSTOMER_PROFILE_CHECK;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'Customer Profile Check throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		await setCache(cacheName, JSON.stringify(resBody), 900); // 15 minutes
		return resBody as Wso2CustomerProfileCheckRes;
	}

	async getWso2EdwhMoana(
		bodyRequest: Wso2EdwhMoanaReq
	): Promise<Wso2EdwhMoanaRes> {
		const cacheName = `${CacheKeyEnum.WSO2_EDWH_MOANA}-${bodyRequest.edwhRequest.customerSvcId}-${bodyRequest.edwhRequest.segment}`;
		const cache = await getCache(cacheName);
		if (cache) {
			return JSON.parse(cache) as Wso2EdwhMoanaRes;
		}
		const url: string = envConfig().WSO2_EDWH_MOANA;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR('EDWH Moana throw error', StatusCodeEnum.WSO2_ERROR, {
				integrationId: this.integrationId
			});
		}
		await setCache(cacheName, JSON.stringify(resBody), 3600); // 1 hour
		return resBody as Wso2EdwhMoanaRes;
	}

	async getWso2OpenTransferReq(
		bodyRequest: Wso2CheckOpenTransferReq
	): Promise<Wso2CheckOpenTransferRes> {
		const url: string = envConfig().WSO2_NOVA_TRANSFER_REQUEST_STATUS;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'WSO2 Check Open Transfer Request Status throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return resBody as Wso2CheckOpenTransferRes;
	}

	async getWso2QueryHardSoftBundle(
		bodyRequest: Wso2QueryHardSoftBundleReq
	): Promise<Wso2QueryHardSoftBundleRes> {
		const cacheName = `${CacheKeyEnum.WSO2_QUERY_HARD_SOFT_BUNDLE}-${bodyRequest.pmeNumber}`;
		const cache = await getCache(cacheName);
		if (cache) {
			return JSON.parse(cache) as Wso2QueryHardSoftBundleRes;
		}
		const url: string = envConfig().WSO2_HARD_SOFT_BUNDLE;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'WSO2 Query Hard Soft Bundle throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}
		await setCache(cacheName, JSON.stringify(resBody), 86400); // 24 hours
		return resBody as Wso2QueryHardSoftBundleRes;
	}

	async wso2CTTCheckEligibility(
		customerID: string
	): Promise<Wso2CTTCheckEligibilityRes> {
		const url: string = `${
			envConfig().WSO2_CTT_CHECK_ELIGIBILITY
		}?customerid=${customerID}`;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'MYUNIFI'
			}
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});
		if (!res.ok) {
			throw new UE_ERROR(
				'WSO2 CTT Check Eligibility throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}
		return resBody as Wso2CTTCheckEligibilityRes;
	}

	async getWso2SSMInfo(bodyRequest: Wso2SSMInfoReq): Promise<Wso2SSMInfoRes> {
		const url: string = envConfig().WSO2_SSM_INFO;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});
		if (!res.ok && res.status !== 500) {
			throw new UE_ERROR(
				'WSO2 SSM Info throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}
		return resBody as Wso2SSMInfoRes;
	}

	async getWso2DeviceBundle(
		bodyRequest: Wso2DeviceBundleReq,
		isEnabledErrorException = true
	): Promise<Wso2DeviceBundleRes> {
		const url: string = envConfig().WSO2_RETRIEVE_BUNDLE_DEVICE;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body, {
			isEnabledErrorException
		});
		const resBody = await res.json().catch(error => {
			if (isEnabledErrorException)
				throw new UE_ERROR(
					'Failed to parse JSON response',
					StatusCodeEnum.WSO2_ERROR,
					{ integrationId: this.integrationId, response: String(error) }
				);

			return null;
		});

		if (!res.ok) {
			if (isEnabledErrorException)
				throw new UE_ERROR(
					'Device bundle throw error',
					StatusCodeEnum.WSO2_ERROR,
					{
						integrationId: this.integrationId,
						response: JSON.stringify(resBody)
					}
				);
			return null;
		}
		return resBody as Wso2DeviceBundleRes;
	}
}

export default Wso2EligibilityIntegration;
