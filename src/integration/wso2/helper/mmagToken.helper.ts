import { type Static, t } from 'elysia';
import { getCache, setCache } from '../../../config/cache.config';
import { envConfig } from '../../../config/env.config';
import { CacheKeyEnum } from '../../../enum/cacheKey.enum';
import { StatusCodeEnum } from '../../../enum/statusCode.enum';
import { UE_ERROR } from '../../../middleware/error';
import { fetchApi } from '../../helper/fetchApi.helper';

export const mmagStockTokenResSchema = t.Object({
	status: t.String(),
	code: t.Number(),
	message: t.String(),
	token: t.String({
		example:
			'6B99ED6C4526AE6D0B30B8A7109BEF255924178B02BC4AF8B03F6E0C48B29045249512'
	}),
	tokenexpiry: t.String({ example: '2025-03-13 11:45:11' })
});

type MmagTokenRes = Static<typeof mmagStockTokenResSchema>;

export async function getMmagToken(token: string): Promise<string> {
	const cacheKey = CacheKeyEnum.MMAG_TOKEN;
	const cacheValue = await getCache(cacheKey);
	if (cacheValue) return cacheValue;

	const url = envConfig().WSO2_MMAG_TOKEN_URL;

	const body = {
		method: 'POST',
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
			Authorization: `Bearer ${token}`
		}
	};

	const res = await fetchApi(cacheKey, url, body, { retries: 3 });
	const resBody = await res.json().catch(() => {
		throw new UE_ERROR(
			'Failed to parse JSON response from stock token',
			StatusCodeEnum.WSO2_ERROR,
			{ integrationId: null }
		);
	});
	if (!res.ok) {
		throw new UE_ERROR(
			'Failed to get MMAG Stock token',
			StatusCodeEnum.MMAG_ERROR,
			{
				integrationId: null,
				response: resBody
			}
		);
	}

	const obj = resBody as MmagTokenRes;
	await setCache(cacheKey, obj.token, 3600); // 1 hour
	return obj.token;
}
