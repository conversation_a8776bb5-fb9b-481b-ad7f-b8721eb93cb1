import { type Static, t } from 'elysia';
import { getCache, setCache } from '../../../config/cache.config';
import { envConfig } from '../../../config/env.config';
import { CacheKeyEnum } from '../../../enum/cacheKey.enum';
import { StatusCodeEnum } from '../../../enum/statusCode.enum';
import { UE_ERROR } from '../../../middleware/error';
import { fetchApi } from '../../helper/fetchApi.helper';

const apimTokenResSchema = t.Object({
	access_token: t.String(),
	scope: t.String({ example: 'default' }),
	token_type: t.String({ example: 'Bearer' }),
	expires_in: t.Number({ example: 3600 })
});

type ApimTokenRes = Static<typeof apimTokenResSchema>;

export async function getApimToken(useCache = true): Promise<string> {
	const cacheName = CacheKeyEnum.APIM_TOKEN;
	const cache = await getCache(cacheName);
	if (cache && useCache) {
		return cache;
	}
	const wso2BasicAuth = envConfig().WSO2_BASIC_AUTH;
	const url = envConfig().WSO2_APIM_HOURLY_TOKEN;

	const bodyRequest = new URLSearchParams();
	bodyRequest.append('grant_type', 'client_credentials');

	const body = {
		method: 'POST',
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
			Authorization: `Basic ${wso2BasicAuth}`
		},
		body: bodyRequest
	};

	const res = await fetchApi('apimToken', url, body, { retries: 3 });
	const resBody = await res.json().catch(() => {
		throw new UE_ERROR(
			'Failed to parse JSON response from apim token',
			StatusCodeEnum.WSO2_ERROR,
			{ integrationId: null }
		);
	});
	if (!res.ok) {
		throw new UE_ERROR('Failed to get APIM token', StatusCodeEnum.APIM_ERROR, {
			integrationId: null,
			response: resBody
		});
	}

	const obj = resBody as ApimTokenRes;
	await setCache(cacheName, obj.access_token, 3600); // 1 hour
	return obj.access_token;
}
