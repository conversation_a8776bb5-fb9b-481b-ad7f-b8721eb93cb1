import { type Static, t } from 'elysia';
import { AutopayReqTypeEnum } from '../../../../../enum/payment.enum';
import { SystemNameEnum } from '../../../../../enum/wso2.enum';

const wso2EnableAutopayReqSchema = t.Object({
	idType: t.String(),
	idValue: t.String(),
	BillingAccountNo: t.String(),
	SystemName: t.Enum(SystemNameEnum),
	RequestType: t.Enum(AutopayReqTypeEnum),
	GetCreditCardInfoRequest: t.Partial(
		t.Object({
			ccNumber: t.String(),
			ccIssuerBank: t.String(),
			ccSiebelIssuerBankCode: t.String(),
			ccIssuerBankName: t.String(),
			ccExpiryMonth: t.String(),
			ccExpiryYear: t.String(),
			ccHolderName: t.String(),
			ccOwnerIdType: t.String(),
			ccOwnIdVal: t.String(),
			ccOwnRelAcc: t.String(),
			ccCardType: t.String(),
			contactId: t.Nullable(t.String()),
			emailAddress: t.String()
		})
	)
});

export type Wso2EnableAutopayReq = Static<typeof wso2EnableAutopayReqSchema>;

export const wso2EnableAutopayResSchema = t.Object({
	Status: t.Object({
		Type: t.String(),
		Code: t.String(),
		Message: t.Nullable(t.String())
	}),
	Response: t.Optional(
		t.Object({
			ListOfTmEaiSrPayMethCreateReq: t.Object({
				TmServiceRequestIntegration: t.Object({
					SRNumber: t.String(),
					BillingAccount: t.String()
				})
			})
		})
	)
});

export type Wso2EnableAutopayRes = Static<typeof wso2EnableAutopayResSchema>;
