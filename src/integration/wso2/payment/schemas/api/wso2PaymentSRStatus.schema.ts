import { type Static, t } from 'elysia';

const wso2CheckPaymentSRStatusReqSchema = t.Object({
	idType: t.String(),
	idValue: t.String(),
	BillingAccountNo: t.String(),
	SystemName: t.String()
});
export type Wso2CheckPaymentSRStatusReq = Static<
	typeof wso2CheckPaymentSRStatusReqSchema
>;

export const wso2CheckPaymentSRStatusResSchema = t.Object({
	Status: t.Object({ Type: t.String(), Code: t.String(), Message: t.String() }),
	Response: t.Object({
		ListOfTmEaiSrPayMethCreateReq: t.MaybeEmpty(
			t.Object({
				TmServiceRequestIntegration: t.MaybeEmpty(
					t.Object({ SRNumber: t.String() })
				)
			})
		)
	})
});

export type Wso2CheckPaymentSRStatusRes = Static<
	typeof wso2CheckPaymentSRStatusResSchema
>;
