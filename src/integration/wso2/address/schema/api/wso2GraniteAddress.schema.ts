import { type Static, t } from 'elysia';

// Schema for Address Details (shared across responses)
export const wso2GraniteAddressDetailsSchema = t.Object({
	FLOOR_NUMBER: t.MaybeEmpty(t.String({ alias: ['floor'] })),
	ADDR_SERVICE_CATEGORY: t.Optional(t.String({ alias: ['platform'] })),
	RESOURCE_INST_ID: t.Optional(t.Number({ alias: ['address_id'] })),
	POSTCODE: t.Optional(t.String({ alias: ['postcode'] })),
	MAIN_DP: t.Optional(t.String({ alias: ['main_dp'] })),
	STATE: t.Optional(t.String({ alias: ['state'] })),
	PREMISE_TYPE: t.Optional(t.String({ alias: ['premise_type'] })),
	ADDRESS_FULL: t.Optional(t.String({ alias: ['area'] })),
	COUNTRY: t.Optional(t.String({ alias: ['country'] })),
	CITY: t.Optional(t.String({ alias: ['city'] })),
	STREET_TYPE: t.Optional(t.String({ alias: ['street_type'] })),
	STREET_NAME: t.Optional(t.String({ alias: ['street_name'] })),
	SECTION: t.Optional(t.String({ alias: ['section'] })),
	HYBRID: t.Optional(t.Union([t.String(), t.Null()])),
	HOUSE_UNIT_LOT: t.Optional(t.String({ alias: ['unit'] })),
	BUILDING_NAME: t.Optional(
		t.Union([t.String(), t.Null()], { alias: ['building'] })
	),
	ADDRESSTYPE: t.Optional(t.String({ alias: ['addresstype'] })),
	COPPER_OWN_TM: t.Optional(t.String({ alias: ['copper_own_tm'] })),
	DDP: t.Optional(t.Union([t.String({ alias: ['ddp'] }), t.Null()])),
	ROWNUM: t.Optional(t.Integer({ alias: ['rownum'] })),
	DISTANCE: t.Optional(t.Number({ alias: ['distance'] })),
	NTW_LOC_LAT: t.Optional(t.String({ alias: ['ntw_loc_lat'] })),
	NTW_LOC_LONG: t.Optional(t.String({ alias: ['ntw_loc_long'] }))
});

export type Wso2GraniteAddressDetails = Static<
	typeof wso2GraniteAddressDetailsSchema
>;

// Schema for the overall response structure (with address details)
const wso2GraniteAddressResSchema = t.Object({
	status: t.String(),
	error: t.String(),
	result: t.String(),
	data: t.Array(wso2GraniteAddressDetailsSchema)
});

export type Wso2GraniteAddressRes = Static<typeof wso2GraniteAddressResSchema>;

// --- Request and Response Schemas ---

// WSO2 Get Address By ID
const Wso2GetAddressByIDReqSchema = t.Object({
	AddressId: t.String()
});
export type Wso2GetAddressByIDReq = Static<typeof Wso2GetAddressByIDReqSchema>;

export const Wso2GetAddressByIDResSchema = t.Object({
	status: t.String(),
	error: t.String(),
	result: t.String(),
	data: t.Integer(),
	addressDetails: t.Array(wso2GraniteAddressDetailsSchema)
});
export type Wso2GetAddressByIDRes = Static<typeof Wso2GetAddressByIDResSchema>;

// WSO2 Get Address By Keyword and State
const wsoGetAddressByKeywordStateReqSchema = t.Object({
	query: t.String(),
	state: t.String()
});
export type WsoGetAddressByKeywordStateReq = Static<
	typeof wsoGetAddressByKeywordStateReqSchema
>;

// WSO2 Get Address By Coordinates
const wsoGetAddressByCoordinateReqSchema = t.Object({
	lat: t.String(),
	lng: t.String()
});
export type WsoGetAddressByCoordinateReq = Static<
	typeof wsoGetAddressByCoordinateReqSchema
>;
