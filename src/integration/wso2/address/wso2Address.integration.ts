import { getCache, setCache } from '../../../config/cache.config';
import { envConfig } from '../../../config/env.config';
import { CacheKeyEnum } from '../../../enum/cacheKey.enum';
import { StatusCodeEnum } from '../../../enum/statusCode.enum';
import { UE_ERROR } from '../../../middleware/error';
import { fetchApi } from '../../helper/fetchApi.helper';
import { getApimToken } from '../helper/apimToken.helper';
import type { Wso2BodyReq } from '../helper/schemas/api/wso2Base.schema';
import type {
	Wso2GetAddressByIDReq,
	Wso2GetAddressByIDRes,
	Wso2GraniteAddressRes,
	WsoGetAddressByCoordinateReq,
	WsoGetAddressByKeywordStateReq
} from './schema/api/wso2GraniteAddress.schema';

class Wso2AddressIntegration {
	integrationId: string;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
	}

	async getGraniteAddressByAddressId(
		bodyRequest: Wso2GetAddressByIDReq
	): Promise<Wso2GetAddressByIDRes> {
		const cacheName = `${CacheKeyEnum.WSO2_GRANITE_ADDRESS_BY_ADDRESS_ID}-${bodyRequest.AddressId}`;
		const cache = await getCache(cacheName);
		if (cache) {
			return JSON.parse(cache) as Wso2GetAddressByIDRes;
		}
		const url: string = envConfig().WSO2_ADDRESS_BY_ID;
		const token: string = await getApimToken();

		// Construct the URL with query parameters
		const urlWithParams = new URL(url);
		urlWithParams.searchParams.append('addressid', bodyRequest.AddressId);

		const payload: Wso2BodyReq = {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			}
		};
		const res = await fetchApi(
			this.integrationId,
			urlWithParams.toString(),
			payload
		);
		const resBody = await res.json();

		if (!res.ok) {
			throw new UE_ERROR(
				'Get Address By Address ID throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		await setCache(cacheName, JSON.stringify(resBody), 900); // 15 minutes
		return resBody as Wso2GetAddressByIDRes;
	}

	async getGraniteAddressByCoordinates(
		bodyRequest: WsoGetAddressByCoordinateReq
	): Promise<Wso2GraniteAddressRes> {
		const cacheName = `${CacheKeyEnum.WSO2_GRANITE_ADDRESS_BY_COORDINATE}-${bodyRequest.lat}-${bodyRequest.lng}`;
		const cache = await getCache(cacheName);
		if (cache) {
			return JSON.parse(cache) as Wso2GraniteAddressRes;
		}
		const url: string = envConfig().WSO2_ADDRESS_BY_COORDINATE;
		const token: string = await getApimToken();

		// Construct the URL with query parameters
		const urlWithParams = new URL(url);
		urlWithParams.searchParams.append('lng', bodyRequest.lng);
		urlWithParams.searchParams.append('lat', bodyRequest.lat);

		const payload: Wso2BodyReq = {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			}
		};
		const res = await fetchApi(
			this.integrationId,
			urlWithParams.toString(),
			payload
		);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'Get Address By Coordinate throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		await setCache(cacheName, JSON.stringify(resBody), 900); // 15 minutes
		return resBody as Wso2GraniteAddressRes;
	}

	async getGraniteAddressByKeywordAndState(
		bodyRequest: WsoGetAddressByKeywordStateReq
	): Promise<Wso2GraniteAddressRes> {
		const cacheName = `${CacheKeyEnum.WSO2_GRANITE_ADDRESS_BY_KEYWORD_AND_STATE}-${bodyRequest.query}-${bodyRequest.state}`;
		const cache = await getCache(cacheName);
		if (cache) {
			return JSON.parse(cache) as Wso2GraniteAddressRes;
		}
		const url: string = envConfig().WSO2_ADDRESS_BY_KEYWORD_STATE;
		const token: string = await getApimToken();

		// Construct the URL with query parameters
		const urlWithParams = new URL(url);
		urlWithParams.searchParams.append('query', bodyRequest.query);
		urlWithParams.searchParams.append('state', bodyRequest.state);

		const payload: Wso2BodyReq = {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			}
		};
		const res = await fetchApi(
			this.integrationId,
			urlWithParams.toString(),
			payload
		);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'Get Address By Keyword And State throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		await setCache(cacheName, JSON.stringify(resBody), 900); // 15 minutes
		return resBody as Wso2GraniteAddressRes;
	}
}

export default Wso2AddressIntegration;
