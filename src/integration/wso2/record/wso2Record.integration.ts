import { envConfig } from '../../../config/env.config';
import { StatusCodeEnum } from '../../../enum/statusCode.enum';
import { UE_ERROR } from '../../../middleware/error';
import { fetchApi } from '../../helper/fetchApi.helper';
import { getApimToken } from '../helper/apimToken.helper';
import type {
	Wso2BaseResponse,
	Wso2BodyReq
} from '../helper/schemas/api/wso2Base.schema';
import type { Wso2AcknowledgeAppointmentReq } from './schemas/api/wso2AckAppointment.schema';
import type {
	Wso2GetAppointmentSlotReq,
	Wso2GetAppointmentSlotRes
} from './schemas/api/wso2AppointmentSlot.schema';
import type {
	Wso2CreateSRReq,
	Wso2CreateSRRes
} from './schemas/api/wso2CreateSR.schema';
import type { Wso2CreateSRICP } from './schemas/api/wso2CreateSRIcp.schema';
import type {
	Wso2CreateSRNovaReq,
	Wso2CreateSRNovaRes
} from './schemas/api/wso2CreateSRNova.schema';
import type {
	Wso2EasyfixTnpsSurveyReq,
	Wso2EasyfixTnpsSurveyRes
} from './schemas/api/wso2EasyfixTnpsSurvey.schema';
import type { Wso2NovaCTT } from './schemas/api/wso2NovaCTT.schema';
import type { Wso2NovaCreateCustomer } from './schemas/api/wso2NovaCreateCustomer.schema';
import type {
	Wso2NovaRetrieveCustomerReq,
	Wso2NovaRetrieveCustomerRes
} from './schemas/api/wso2NovaRetrieveCustomer.schema';
import type {
	Wso2OrderTrackingReq,
	Wso2OrderTrackingRes
} from './schemas/api/wso2OrderTracking.schema';
import type {
	Wso2RetrieveNTTReq,
	Wso2RetrieveNTTRes
} from './schemas/api/wso2RetrieveNTT.schema';
import type {
	Wso2SRDetailReq,
	Wso2SRDetailRes
} from './schemas/api/wso2SRDetail.schema';
import type {
	Wso2SubmitNesSurveyReq,
	Wso2SubmitNesSurveyRes
} from './schemas/api/wso2SubmitNesSurvey.schema';
import type {
	Wso2SubmitRebateReq,
	Wso2SubmitRebateRes
} from './schemas/api/wso2SubmitRebate.schema';
import type { Wso2TmForceAcceptanceFormRes } from './schemas/api/wso2TmForceAcceptanceForm.schema';
import type { Wso2TmForceProgressUpdateRes } from './schemas/api/wso2TmForceGetProgressUpdate.schema';
import type { Wso2TmForceOrderDetailsRes } from './schemas/api/wso2TmForceOrderDetails.schema';
import type {
	Wso2TmForcePostponeAppointmentReq,
	Wso2TmForcePostponeAppointmentRes
} from './schemas/api/wso2TmForcePostponeAppointment.schema';
import type {
	Wso2TmForceTTProgressUpdateReq,
	Wso2TmForceTTProgressUpdateRes
} from './schemas/api/wso2TmForceTTProgressUpdate.schema';
import type {
	Wso2TmForceTechinicianDetailsReq,
	Wso2TmForceTechinicianDetailsRes
} from './schemas/api/wso2TmForceTechinicianDetails.schema';
import type {
	Wso2UpdateAppointmentReq,
	Wso2UpdateAppointmentRes
} from './schemas/api/wso2UpdateAppointment.schema';
import type { Wso2UpdateCustomerResponseReq } from './schemas/api/wso2UpdateCustomerRes.schema';

class Wso2RecordIntegration {
	integrationId: string;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
	}

	async getWso2RetrieveAppointmentSlot(
		bodyRequest: Wso2GetAppointmentSlotReq
	): Promise<Wso2GetAppointmentSlotRes> {
		const url: string = envConfig().WSO2_RETRIEVE_APPOINTMENT_SLOT;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});
		const parsedBody = resBody as Wso2GetAppointmentSlotRes;

		if (!res.ok || parsedBody.Status.Type === 'NOK') {
			throw new UE_ERROR(
				'Retrieve Appointment Slot throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return parsedBody;
	}

	async getWso2UpdateCustomerResponse(
		bodyRequest: Wso2UpdateCustomerResponseReq
	): Promise<Wso2BaseResponse> {
		const url: string = envConfig().WSO2_SWIFT_UPDATE_CUSTOMER_RESPONSE;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});
		const parsedBody = resBody as Wso2BaseResponse;
		if (!res.ok || parsedBody.Status?.Type === 'NOK') {
			throw new UE_ERROR(
				'Update Customer Response throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return parsedBody;
	}

	async getWso2AcknowledgeAppointment(
		bodyRequest: Wso2AcknowledgeAppointmentReq
	): Promise<Wso2BaseResponse> {
		const url: string = envConfig().WSO2_SIEBEL_UPDATE_CONFIRM_FLAG;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});
		const parsedBody = resBody as Wso2BaseResponse;
		if (!res.ok || parsedBody.Status?.Type === 'NOK') {
			throw new UE_ERROR(
				'Acknowledge Appointment throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return parsedBody;
	}

	async getWso2UpdateAppointment(
		bodyRequest: Wso2UpdateAppointmentReq
	): Promise<Wso2UpdateAppointmentRes> {
		const url: string = envConfig().WSO2_UPDATE_APPOINTMENT;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});
		const parsedBody = resBody as Wso2UpdateAppointmentRes;
		if (!res.ok || parsedBody.Status?.Type === 'NOK') {
			throw new UE_ERROR(
				'Update Appointment throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return parsedBody;
	}

	async wso2NovaCreateCustomer(
		bodyRequest: Wso2NovaCreateCustomer
	): Promise<Wso2NovaCreateCustomer> {
		const url: string = envConfig().WSO2_CREATE_CUSTOMER_NOVA;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'MYUNIFI'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'WSO2 Create Customer throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return resBody as Wso2NovaCreateCustomer;
	}

	async wso2NovaRetrieveCustomer(
		bodyRequest: Wso2NovaRetrieveCustomerReq
	): Promise<Wso2NovaRetrieveCustomerRes> {
		const url: string = envConfig().WSO2_RETRIEVE_CUSTOMER_NOVA;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'MYUNIFI'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'NOVA Retrieve Customer throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}
		return resBody as Wso2NovaRetrieveCustomerRes;
	}

	async wso2NovaCreateTT(bodyRequest: Wso2NovaCTT): Promise<Wso2NovaCTT> {
		const url: string = envConfig().WSO2_CREATE_TT_NOVA;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'MYUNIFI'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'NOVA Create CTT throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}
		return resBody as Wso2NovaCTT;
	}

	async wso2RetrieveNTT(
		bodyRequest: Wso2RetrieveNTTReq
	): Promise<Wso2RetrieveNTTRes> {
		const url: string = envConfig().WSO2_CREATE_TT_NOVA;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'MYUNIFI'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});
		if (!res.ok) {
			throw new UE_ERROR(
				'NOVA Retrieve NTT throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}
		return resBody as Wso2RetrieveNTTRes;
	}

	async getWso2OrderTracking(
		bodyRequest: Wso2OrderTrackingReq
	): Promise<Wso2OrderTrackingRes> {
		const url: string = envConfig().WSO2_ORDER_TRACKING;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'Order Tracking throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return resBody as Wso2OrderTrackingRes;
	}

	async wso2SubmitRebate(customerID: Wso2SubmitRebateReq): Promise<boolean> {
		const url: string = envConfig().WSO2_CTT_CHECK_ELIGIBILITY + customerID;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'MYUNIFI'
			}
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});
		const parsedBody = resBody as Wso2SubmitRebateRes;

		if (
			res.ok &&
			parsedBody.Status &&
			parsedBody.Status.Message === 'SUCCESS'
		) {
			return true;
		}

		throw new UE_ERROR(
			'Rebate Submission throw error',
			StatusCodeEnum.WSO2_ERROR,
			{ integrationId: this.integrationId }
		);
	}

	async getWso2SRDetails(
		bodyRequest: Wso2SRDetailReq,
		version = 'V1'
	): Promise<Wso2SRDetailRes> {
		const url: string = envConfig().WSO2_SR_RETRIEVE;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE',
				'x-version': version
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR('SR Details throw error', StatusCodeEnum.WSO2_ERROR, {
				integrationId: this.integrationId
			});
		}

		return resBody as Wso2SRDetailRes;
	}

	async wso2CreateSR(bodyRequest: Wso2CreateSRReq): Promise<Wso2CreateSRRes> {
		const url: string = envConfig().WSO2_CREATE_SR;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});
		const obj = resBody as Wso2CreateSRRes;
		if (!res.ok || obj?.Status?.Type === 'nok') {
			throw new UE_ERROR(
				'SR Submission throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}
		return obj;
	}

	async wso2CreateSRNova(
		bodyRequest: Wso2CreateSRNovaReq
	): Promise<Wso2CreateSRNovaRes> {
		const url: string = envConfig().WSO2_CREATE_SR_NOVA;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'MYUNIFI'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'SR Nova Submission throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}
		return resBody as Wso2CreateSRNovaRes;
	}

	async wso2CreateSRICP(
		bodyRequest: Wso2CreateSRICP
	): Promise<Wso2CreateSRICP> {
		const url: string = envConfig().WSO2_CREATE_SR_ICP;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'MYUNIFI'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});
		if (!res.ok) {
			throw new UE_ERROR(
				'SR ICP Submission throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}
		return resBody as Wso2CreateSRICP;
	}

	async wso2SubmitNesSurvey(
		bodyRequest: Wso2SubmitNesSurveyReq,
		callingApplication: string
	): Promise<Wso2SubmitNesSurveyRes> {
		const url: string = envConfig().WSO2_SUBMIT_NES_SURVEY;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': callingApplication,
				'x-type': 'PERFORM'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});
		if (!res.ok) {
			throw new UE_ERROR(
				'Nes Survey Submission throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}
		return resBody as Wso2SubmitNesSurveyRes;
	}

	async wso2EasyfixTnpsSurvey(
		bodyRequest: Wso2EasyfixTnpsSurveyReq
	): Promise<Wso2EasyfixTnpsSurveyRes> {
		const url: string = envConfig().WSO2_EASYFIX_TNPS_SURVEY;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'MYUNIFI'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'Easyfix TNPS Survey Submission throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return resBody as Wso2EasyfixTnpsSurveyRes;
	}

	async getWso2TmForceOrderDetails(
		orderNumber: string
	): Promise<Wso2TmForceOrderDetailsRes> {
		const url: string = `${
			envConfig().WSO2_TMFORCE_ORDER_DETAILS
		}?OrderNumber=${orderNumber}`;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			}
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'TM Force Order Details throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return resBody as Wso2TmForceOrderDetailsRes;
	}

	async getWso2TmForceTechinicianDetails(
		bodyRequest: Wso2TmForceTechinicianDetailsReq
	): Promise<Wso2TmForceTechinicianDetailsRes> {
		const url: string = envConfig().WSO2_TMFORCE_TECHNICIAN_DETAILS;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'TM Force Technician Details throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return resBody as Wso2TmForceTechinicianDetailsRes;
	}

	async getWso2TmForceOrderProgressUpdate(
		orderNumber: string
	): Promise<Wso2TmForceProgressUpdateRes> {
		const url: string = `${
			envConfig().WSO2_TMFORCE_ORDER_PROGRESS_UPDATE
		}?OrderNumber=${orderNumber}`;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			}
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'TM Force Order Progress Update throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return resBody as Wso2TmForceProgressUpdateRes;
	}

	async getWso2TmForceTTProgressUpdate(
		bodyRequest: Wso2TmForceTTProgressUpdateReq
	): Promise<Wso2TmForceTTProgressUpdateRes> {
		const url: string = envConfig().WSO2_TMFORCE_TT_PROGRESS_UPDATE;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'TM Force TT Progress Update throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return resBody as Wso2TmForceTTProgressUpdateRes;
	}

	async getWso2TmForcePostponeAppointment(
		bodyRequest: Wso2TmForcePostponeAppointmentReq
	): Promise<Wso2TmForcePostponeAppointmentRes> {
		const url: string = envConfig().WSO2_TMFORCE_POSTPONE_APPOINTMENT;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'TM Force Postpone Appointment throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return resBody as Wso2TmForcePostponeAppointmentRes;
	}

	async getWso2TmForceAcceptanceForm(
		orderNumber: string
	): Promise<Wso2TmForceAcceptanceFormRes> {
		const url: string = `${
			envConfig().WSO2_TMFORCE_ACCEPTANCE_FORM
		}/${orderNumber}`;
		const token: string = await getApimToken();
		const body = {
			method: 'GET',
			headers: {
				Accept:
					'application/octet-stream, application/json, application/*+json, */*',
				// 'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'goer'
			}
		};
		const res = await fetchApi(this.integrationId, url, body);

		if (!res.ok) {
			throw new UE_ERROR(
				`TM Force Acceptance Form throw error: ${res.statusText}`,
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return {
			ContentDisposition:
				res.headers.get('Content-Disposition') ||
				'attachment; filename=acceptance-form.pdf',
			ContentType: res.headers.get('Content-Type') || 'application/pdf',
			Body: new Uint8Array(await res.arrayBuffer())
		};
	}
}

export default Wso2RecordIntegration;
