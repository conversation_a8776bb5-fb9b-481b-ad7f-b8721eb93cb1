import { type Static, t } from 'elysia';

const wso2TmForceTTProgressUpdateReqSchema = t.Object({
	ProgressUpdateRequest: t.Object({
		TTNumber: t.String()
	})
});

export type Wso2TmForceTTProgressUpdateReq = Static<
	typeof wso2TmForceTTProgressUpdateReqSchema
>;

export const wso2TmForceTTProgressUpdateResSchema = t.Object({
	Status: t.Object({
		Type: t.String(),
		Code: t.String(),
		Message: t.Nullable(t.String())
	}),
	Response: t.Optional(
		t.Object({
			ProgressUpdate: t.Array(
				t.Object({
					TicketId: t.Nullable(t.String()),
					ActivityId: t.Nullable(t.String()),
					CTTNumber: t.Nullable(t.String()),
					OldStatus: t.Nullable(t.String()),
					NewStatus: t.Nullable(t.String()),
					LogDateTime: t.Nullable(t.String()),
					ActivityType: t.Nullable(t.String()),
					Description: t.Nullable(t.String())
				})
			)
		})
	)
});

export type Wso2TmForceTTProgressUpdateRes = Static<
	typeof wso2TmForceTTProgressUpdateResSchema
>;
