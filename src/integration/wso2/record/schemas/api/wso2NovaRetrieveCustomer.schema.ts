import { type Static, t } from 'elysia';

export const wso2NovaRetrieveCustomerReqSchema = t.Object({
	ListOfTmEaiRetrieveCustInfoIo: t.Object({
		TmAccountIntegration: t.Array(
			t.Object({
				CustomerID: t.String({
					description: 'Encrypted customer ID'
				}),
				CustomerName: t.String({
					description: 'Encrypted customer name'
				})
			})
		)
	})
});

export type Wso2NovaRetrieveCustomerReq = Static<
	typeof wso2NovaRetrieveCustomerReqSchema
>;

export const wso2NovaRetrieveCustomerResSchema = t.Object({
	ListOfTmEaiRetrieveCustInfoIo: t.Object({
		TmAccountIntegration: t.Array(
			t.Object({
				AccountRowID: t.MaybeEmpty(t.String()),
				CustomerID: t.MaybeEmpty(t.String()),
				CustomerName: t.MaybeEmpty(t.String()),
				ListOfTmContactIntegration: t.Object({
					TmContactIntegration: t.Array(
						t.Object({
							ContactRowID: t.MaybeEmpty(t.String()),
							MobilePhone: t.MaybeEmpty(t.String()),
							EmailAddress: t.MaybeEmpty(t.String()),
							HomePhone: t.MaybeEmpty(t.String()),
							ContactName: t.MaybeEmpty(t.String()),
							OfficePhone: t.MaybeEmpty(t.String())
						})
					)
				})
			})
		)
	})
});

export type Wso2NovaRetrieveCustomerRes = Static<
	typeof wso2NovaRetrieveCustomerResSchema
>;
