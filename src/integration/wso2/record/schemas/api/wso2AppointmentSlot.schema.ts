import { type Static, t } from 'elysia';

const wso2GetAppointmentSlotReqSchema = t.Object({
	SystemName: t.Optional(t.String()),
	OrderId: t.String(),
	RNORegion: t.String(),
	EarliestStartDate: t.String(),
	LatestStartDate: t.String()
});

export type Wso2GetAppointmentSlotReq = Static<
	typeof wso2GetAppointmentSlotReqSchema
>;

export const wso2GetAppointmentSlotResSchema = t.Object({
	Status: t.Object({
		Type: t.MaybeEmpty(t.String()),
		Code: t.MaybeEmpty(t.String()),
		Message: t.String()
	}),
	Response: t.Object({
		AppointmentSlot: t.Array(
			t.Object({
				AppointmentId: t.Nullable(t.String({ examples: ['S2A-191212E9E554'] })),
				SlotStart: t.Nullable(t.String({ examples: ['12/13/2019 10:00:00'] })),
				SlotEnd: t.Nullable(t.String({ examples: ['12/13/2019 10:00:00'] }))
			})
		)
	})
});

export type Wso2GetAppointmentSlotRes = Static<
	typeof wso2GetAppointmentSlotResSchema
>;
