import { type Static, t } from 'elysia';

export const wso2TmForcePostponeAppointmentReqSchema = t.Object({
	OrderNumber: t.String(),
	ConfirmPostpone: t.String({ examples: ['Y'] })
});

export type Wso2TmForcePostponeAppointmentReq = Static<
	typeof wso2TmForcePostponeAppointmentReqSchema
>;

export const wso2TmForcePostponeAppointmentResSchema = t.Object({
	ReplyHeader: t.Nullable(
		t.Object({
			ErrMsg: t.Nullable(t.String()),
			ErrCd: t.Nullable(t.String())
		})
	),
	OrderDetail: t.Object({
		OrderId: t.String(),
		OrderNumber: t.String(),
		OrderStatus: t.String(),
		OrderCreatedDate: t.String(),
		ServiceItemDetail: t.Object({
			ServiceItemName: t.String(),
			PostalCode: t.String(),
			City: t.String(),
			StreetType: t.String(),
			StreetAddress: t.String(),
			PlannedEnd: t.String(),
			FloorNum: t.String(),
			State: t.String(),
			ApartmentNum: t.String(),
			Country: t.String(),
			SectionName: t.String()
		})
	})
});

export type Wso2TmForcePostponeAppointmentRes = Static<
	typeof wso2TmForcePostponeAppointmentResSchema
>;
