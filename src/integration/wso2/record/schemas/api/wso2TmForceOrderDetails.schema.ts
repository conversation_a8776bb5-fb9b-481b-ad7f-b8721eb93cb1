import { type Static, t } from 'elysia';

const wso2TmForceOrderDetailsResSchema = t.Object({
	responseHeader: t.Object({
		code: t.String(),
		requestId: t.Nullable(t.String()),
		responseDateTime: t.String()
	}),
	Response: t.Object({
		ReplyHeader: t.Object({
			ErrMsg: t.Nullable(t.String()),
			ErrCd: t.Nullable(t.String())
		}),
		OrderDetail: t.Object({
			OrderId: t.String(),
			OrderNumber: t.String(),
			OrderStatus: t.String(),
			OrderType: t.String(),
			HSBAFlag: t.String(),
			OrderCreatedDate: t.String(),
			ServiceItemDetail: t.Object({
				ServiceItemId: t.String(),
				ServiceItemName: t.String(),
				ServiceItemStatus: t.String(),
				PlannedStart: t.String(),
				PlannedEnd: t.String(),
				PostalCode: t.String(),
				City: t.String(),
				StreetType: t.String(),
				StreetAddress: t.String(),
				FloorNum: t.String(),
				State: t.String(),
				ApartmentNum: t.String(),
				Country: t.String(),
				SectionName: t.String(),
				BuildingName: t.String(),
				SalesOutletName: t.String()
			}),
			ActivityDetail: t.Array(
				t.Object({
					ActivityId: t.String(),
					ActivityStatus: t.String(),
					ActivityType: t.String(),
					ActivityDescription: t.String()
				})
			),
			Flag: t.Array(
				t.Object({
					Name: t.String(),
					Value: t.String()
				})
			)
		})
	})
});

export type Wso2TmForceOrderDetailsRes = Static<
	typeof wso2TmForceOrderDetailsResSchema
>;
