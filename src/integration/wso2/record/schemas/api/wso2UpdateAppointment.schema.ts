import { type Static, t } from 'elysia';
import { wso2BaseResponseSchema } from '../../../helper/schemas/api/wso2Base.schema';

const wso2UpdateAppointmentReqSchema = t.Object({
	SystemName: t.String(),
	OrderId: t.String(),
	Action: t.String(),
	Info: t.Object({
		ChangeCounter: t.String(),
		ChangeReason: t.String(),
		ChangeRemark: t.String(),
		CancelCounter: t.String(),
		CancelReason: t.String(),
		CancelRemark: t.String(),
		NewAppointmentId: t.String(),
		NewSlotStart: t.String(),
		NewSlotEnd: t.String()
	})
});

export type Wso2UpdateAppointmentReq = Static<
	typeof wso2UpdateAppointmentReqSchema
>;

export const wso2UpdateAppointmentResSchema = t.Object({
	...wso2BaseResponseSchema.properties,
	Response: t.Object({
		SystemName: t.String(),
		OrderId: t.String(),
		Action: t.String(),
		SetInfo: t.Object({
			ErrorCode: t.MaybeEmpty(t.String()),
			ErrorMessage: t.MaybeEmpty(t.String())
		}),
		CancelInfo: t.Object({
			ErrorCode: t.MaybeEmpty(t.String()),
			ErrorMessage: t.MaybeEmpty(t.String())
		}),
		SWIFTInfo: t.Object({
			ErrorCode: t.MaybeEmpty(t.String()),
			ErrorMessage: t.MaybeEmpty(t.String())
		})
	})
});

export type Wso2UpdateAppointmentRes = Static<
	typeof wso2UpdateAppointmentResSchema
>;
