import { type Static, t } from 'elysia';

const wso2OrderStatusProgress = t.Object({
	ServiceItemId: t.Nullable(t.String()),
	ActivityId: t.Nullable(t.String()),
	ActivityType: t.Nullable(t.String()),
	OrderNumber: t.Nullable(t.String()),
	OrderId: t.Nullable(t.String()),
	OldStatus: t.Nullable(t.String()),
	NewStatus: t.Nullable(t.String()),
	LogDatetime: t.Nullable(t.String()), // Change this to match the actual property name
	Description: t.Nullable(t.String()),
	CustomerResponse: t.Nullable(t.String()),
	PlannedStart: t.Nullable(t.String()),
	PlannedEnd: t.Nullable(t.String())
});

export type Wso2OrderStatusProgress = Static<typeof wso2OrderStatusProgress>;

export const wso2TmForceGetProgressUpdateResSchema = t.Optional(
	t.Object({
		ReplyHeader: t.Object({
			ErrMsg: t.Nullable(t.String()),
			ErrCd: t.Nullable(t.String())
		}),
		OrderStatusProgress: t.Array(wso2OrderStatusProgress)
	})
);

export type Wso2TmForceProgressUpdateRes = Static<
	typeof wso2TmForceGetProgressUpdateResSchema
>;
