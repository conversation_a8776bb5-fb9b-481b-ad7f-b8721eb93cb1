import { type Static, t } from 'elysia';

const wso2OrderTrackingReqSchema = t.Object({
	OrderTracking: t.Object({
		SystemName: t.Optional(t.String()),
		OrderList: t.Optional(
			t.Object({
				CustomerAccount: t.Optional(t.String()),
				CustomerID: t.String(),
				IdType: t.String(),
				StartDateTime: t.Optional(t.String()),
				EndDateTime: t.Optional(t.String())
			})
		),
		InstallerInfo: t.Optional(
			t.Object({
				CustomerAccountNOVA: t.String(),
				CustomerID: t.String(),
				IdType: t.String(),
				OrderNo: t.String()
			})
		),
		Orderdetails: t.Optional(
			t.Object({
				CustomerAccountNOVA: t.Optional(t.String()),
				CustomerID: t.String(),
				IdType: t.String(),
				OrderNo: t.String()
			})
		),
		InstallerLocator: t.Optional(
			t.Object({
				OrderNo: t.String()
			})
		)
	})
});

export type Wso2OrderTrackingReq = Static<typeof wso2OrderTrackingReqSchema>;

export const wso2OrderTrackingResSchema = t.Object({
	Status: t.Object({
		Type: t.MaybeEmpty(t.String()),
		Code: t.MaybeEmpty(t.String()),
		Message: t.String()
	}),
	Response: t.Object({
		startDateTime: t.MaybeEmpty(t.String()),
		endDateTime: t.MaybeEmpty(t.String()),
		diceInstallationStatus: t.MaybeEmpty(t.String()),
		installationStatus: t.MaybeEmpty(t.String()),
		InstallerInfo: t.MaybeEmpty(
			t.Object({
				name: t.MaybeEmpty(t.String()),
				phoneNumber: t.MaybeEmpty(t.String()),
				latitude: t.MaybeEmpty(t.String()),
				longitude: t.MaybeEmpty(t.String()),
				photo: t.MaybeEmpty(t.String()),
				installationStatus: t.MaybeEmpty(t.String()),
				startDate: t.MaybeEmpty(t.String()),
				endDate: t.MaybeEmpty(t.String()),
				tosFlag: t.MaybeEmpty(t.String()),
				etta: t.MaybeEmpty(t.String()),
				OrderNumber: t.MaybeEmpty(t.String()),
				TargetLatitude: t.MaybeEmpty(t.String()),
				TargetLongitude: t.MaybeEmpty(t.String())
			})
		),
		InstallerLocator: t.MaybeEmpty(
			t.Object({
				Installerlongitude: t.MaybeEmpty(t.String()),
				Installerlatitude: t.MaybeEmpty(t.String()),
				TosFlag: t.MaybeEmpty(t.String()),
				ETTA: t.MaybeEmpty(t.String()),
				OrderNumber: t.MaybeEmpty(t.String()),
				DiceInstallationStatus: t.MaybeEmpty(t.String())
			})
		),
		OrderList: t.MaybeEmpty(
			t.Object({
				'TmOrderEntry-OrdersIntegration-NOVA': t.Array(
					t.Object({
						'@Searchspec': t.MaybeEmpty(t.String()),
						OrderNumber: t.MaybeEmpty(t.String()),
						Account: t.MaybeEmpty(t.String()),
						TMEformId: t.MaybeEmpty(t.String()),
						Created: t.MaybeEmpty(t.String()),
						OrderId: t.MaybeEmpty(t.String()),
						OrderType: t.MaybeEmpty(t.String()),
						SubOrderType: t.MaybeEmpty(t.String()),
						Status: t.MaybeEmpty(t.String()),
						TMCompletionDate: t.MaybeEmpty(t.String()),
						TMOrderSource: t.MaybeEmpty(t.String()),
						AccountNo: t.MaybeEmpty(t.String()),
						BillingAccountNumber: t.MaybeEmpty(
							t.String({ error: 'BillingAccountNumber is required' })
						),
						TMPortalPartnerLevel4: t.MaybeEmpty(t.String()),
						TMPortalPartnerLevel3: t.MaybeEmpty(t.String()),
						TMPortalPartnerLevel2: t.MaybeEmpty(t.String()),
						TMPortalPartnerLevel1: t.MaybeEmpty(t.String()),
						TMPartnerOutletCode4: t.MaybeEmpty(t.String()),
						TMPortalPartnerLevel5: t.MaybeEmpty(t.String()),
						TMPartnerOutletCode3: t.MaybeEmpty(t.String()),
						TMPartnerOutletCode2: t.MaybeEmpty(t.String()),
						TMPartnerOutletCode1: t.MaybeEmpty(t.String()),
						TMPartnerOutletCode5: t.MaybeEmpty(t.String()),
						TMPrimaryCustomerIDNumber: t.MaybeEmpty(t.String()),
						TMPrimaryCustomerIDType: t.MaybeEmpty(t.String()),
						TMBTUServiceId: t.MaybeEmpty(t.String()),
						TMPromotionName: t.MaybeEmpty(t.String()),
						TMMSRFlag: t.MaybeEmpty(t.String()),
						TMDiCEOrderNumber: t.MaybeEmpty(t.String()),
						ListOfTmActionIntegration3: t.MaybeEmpty(t.Object({})) // this object is intended to be empty, can be removed after testing
					})
				),
				'TmOrderEntry-OrdersIntegration-ICP': t.Array(
					t.Object({
						SiebelOrderId: t.MaybeEmpty(t.String()),
						PortalOrderId: t.MaybeEmpty(t.String()),
						OrderType: t.MaybeEmpty(t.String()),
						CreationDate: t.MaybeEmpty(t.String()),
						OrderStatus: t.MaybeEmpty(t.String()),
						CustomerName: t.MaybeEmpty(t.String()),
						IdentityType: t.MaybeEmpty(t.String()),
						IdentityId: t.MaybeEmpty(t.String()),
						ResellerID: t.MaybeEmpty(t.String()),
						Remarks: t.MaybeEmpty(t.String()),
						CancellationFlag: t.MaybeEmpty(t.String())
					})
				)
			})
		),
		OrderDetails: t.MaybeEmpty(
			t.Object({
				ViewOrderDetailResponse: t.MaybeEmpty(
					t.Array(
						t.Object({
							System: t.MaybeEmpty(t.String()),
							DiceOrderID: t.MaybeEmpty(t.String()),
							SBLOrderID: t.MaybeEmpty(t.String()),
							OrderNumber: t.MaybeEmpty(t.String()),
							OrderPlanName: t.MaybeEmpty(t.String()),
							CreatedDate: t.MaybeEmpty(t.String()),
							OrderType: t.MaybeEmpty(t.String()),
							OrderStatus: t.MaybeEmpty(t.String()),
							BANo: t.MaybeEmpty(t.String()),
							BAStatus: t.MaybeEmpty(t.String()),
							BP: t.MaybeEmpty(t.String()),
							PaymentMethod: t.MaybeEmpty(t.String()),
							PaymentTerm: t.MaybeEmpty(t.String()),
							BillLanguage: t.MaybeEmpty(t.String()),
							HouseUnitLot: t.MaybeEmpty(t.String()),
							Level: t.MaybeEmpty(t.String()),
							BuildingName: t.MaybeEmpty(t.String()),
							StreetType: t.MaybeEmpty(t.String()),
							StreetName: t.MaybeEmpty(t.String()),
							Section: t.MaybeEmpty(t.String()),
							City: t.MaybeEmpty(t.String()),
							State: t.MaybeEmpty(t.String()),
							Postcode: t.MaybeEmpty(t.String()),
							Country: t.MaybeEmpty(t.String()),
							ServiceRegion: t.MaybeEmpty(t.String()),
							ServiceID: t.MaybeEmpty(t.String()),
							CustomerID: t.MaybeEmpty(t.String()),
							IdType: t.MaybeEmpty(t.String()),
							CancellationFlag: t.MaybeEmpty(t.String()),
							BAHouseUnitLot: t.MaybeEmpty(t.String()),
							BALevel: t.MaybeEmpty(t.String()),
							BABuildingName: t.MaybeEmpty(t.String()),
							BAStreetType: t.MaybeEmpty(t.String()),
							BAStreetName: t.MaybeEmpty(t.String()),
							BASection: t.MaybeEmpty(t.String()),
							BAPostCode: t.MaybeEmpty(t.String()),
							BACity: t.MaybeEmpty(t.String()),
							BAState: t.MaybeEmpty(t.String()),
							BACountry: t.MaybeEmpty(t.String()),
							AcknowledgeFlag: t.MaybeEmpty(t.String()),
							InstallationActStat: t.MaybeEmpty(t.String()),
							InstallationActTime: t.MaybeEmpty(t.String()),
							AddPoleFlag: t.MaybeEmpty(t.String()),
							AddPoleDate: t.MaybeEmpty(t.String()),
							ProductDetails: t.Array(
								t.MaybeEmpty(
									t.Object({
										MoliType: t.MaybeEmpty(t.String()),
										MoliName: t.MaybeEmpty(t.String()),
										MoliActionCode: t.MaybeEmpty(t.String()),
										MoliStatus: t.MaybeEmpty(t.String()),
										ServiceNo: t.MaybeEmpty(t.String()),
										ReferenceNo: t.MaybeEmpty(t.String()),
										OliType: t.MaybeEmpty(t.String()),
										OliName: t.MaybeEmpty(t.String()),
										OliActionCode: t.MaybeEmpty(t.String()),
										OliStatus: t.MaybeEmpty(t.String())
									})
								)
							)
						})
					)
				),
				'TmOrderEntry-OrdersIntegration': t.MaybeEmpty(
					t.Object({
						Id: t.MaybeEmpty(t.String()),
						Created: t.MaybeEmpty(t.String()),
						Account: t.MaybeEmpty(t.String()),
						TMBTULostDeclaration: t.MaybeEmpty(t.String()),
						BillingAccount: t.MaybeEmpty(t.String()),
						BillingAccountNumber: t.MaybeEmpty(t.String()),
						ContactEmailAddress: t.MaybeEmpty(t.String()),
						ContactLastName: t.MaybeEmpty(t.String()),
						CurrencyCode: t.MaybeEmpty(t.String()),
						Comments: t.MaybeEmpty(t.String()),
						OrderNumber: t.MaybeEmpty(t.String()),
						OrderType: t.MaybeEmpty(t.String()),
						Status: t.MaybeEmpty(t.String()),
						TMCompletionDate: t.MaybeEmpty(t.String()),
						TMBTUIndicator: t.MaybeEmpty(t.String()),
						TMCableType: t.MaybeEmpty(t.String()),
						TMPremiseCategory: t.MaybeEmpty(t.String()),
						TMServiceNumber: t.MaybeEmpty(t.String()),
						TMBTUServiceId: t.MaybeEmpty(t.String()),
						TMStreamyxLogin: t.MaybeEmpty(t.String()),
						TMTerminatePOTSFlag: t.MaybeEmpty(t.String()),
						TMTerminateStreamyxFlag: t.MaybeEmpty(t.String()),
						ReasonCode: t.MaybeEmpty(t.String()),
						TMOrderReference: t.MaybeEmpty(t.String()),
						TMCancellationComments: t.MaybeEmpty(t.String()),
						TMCancellationReason: t.MaybeEmpty(t.String()),
						TMWholesalerLogin: t.MaybeEmpty(t.String()),
						TMWholesalerEmail: t.MaybeEmpty(t.String()),
						TMPromotionName: t.MaybeEmpty(t.String()),
						TMCancellationId: t.MaybeEmpty(t.String()),
						TMCancellationEmail: t.MaybeEmpty(t.String()),
						TMPrimaryCustomerIDNumber: t.MaybeEmpty(t.String()),
						TMPrimaryCustomerIDType: t.MaybeEmpty(t.String()),
						TMDiCEOrderNumber: t.MaybeEmpty(t.String()),
						'ListOfTmOrderEntry-LineItemsIntegration': t.MaybeEmpty(
							t.Object({
								'TmOrderEntry-LineItemsIntegration': t.MaybeEmpty(
									t.Array(
										t.Object({
											TMHSBAOfferingsProductName: t.MaybeEmpty(t.String()),
											TMVLANId: t.MaybeEmpty(t.String()),
											ActionCode: t.MaybeEmpty(t.String()),
											CurrencyCode: t.MaybeEmpty(t.String()),
											ExchangeDate: t.MaybeEmpty(t.String()),
											ItemPrice: t.MaybeEmpty(t.String()),
											LineNumber: t.MaybeEmpty(t.String()),
											MRCCxTotal: t.MaybeEmpty(t.String()),
											NRCCxTotal: t.MaybeEmpty(t.String()),
											OrderHeaderId: t.MaybeEmpty(t.String()),
											PartNumber: t.MaybeEmpty(t.String()),
											ProdPromName: t.MaybeEmpty(t.String()),
											Product: t.MaybeEmpty(t.String()),
											ProductType: t.MaybeEmpty(t.String()),
											QuantityRequested: t.MaybeEmpty(t.String()),
											ServiceId: t.MaybeEmpty(t.String()),
											Status: t.MaybeEmpty(t.String()),
											TMEquipmentManufacturer: t.MaybeEmpty(t.String()),
											TMEquipmentModel: t.MaybeEmpty(t.String()),
											TMMACAddress: t.MaybeEmpty(t.String()),
											TMEquipmentType: t.MaybeEmpty(t.String()),
											ListOfTmOrderItemXaIntegration: t.MaybeEmpty(
												t.Object({})
											), // this object is intended to be empty, can be removed after testing, the reason we add because MS XE has this object
											'ListOfTmCutAssetMgmt-ServiceMeterIntegration':
												t.MaybeEmpty(
													t.Object({
														'TmCutAssetMgmt-ServiceMeterIntegration':
															t.MaybeEmpty(
																t.Array(
																	t.Object({
																		TMMACAddress: t.MaybeEmpty(t.String()),
																		ServicePointId: t.MaybeEmpty(t.String()),
																		TMEquipmentManufacturer: t.MaybeEmpty(
																			t.String()
																		),
																		TMEquipmentModel: t.MaybeEmpty(t.String()),
																		ListOfTmCutAddressIntegration: t.Object({
																			TmCutAddressIntegration: t.Array(
																				t.Object({
																					Id: t.MaybeEmpty(t.String()),
																					ApartmentNumber: t.MaybeEmpty(
																						t.String()
																					),
																					City: t.MaybeEmpty(t.String()),
																					Country: t.MaybeEmpty(t.String()),
																					PostalCode: t.MaybeEmpty(t.String()),
																					State2: t.MaybeEmpty(t.String()),
																					StreetName: t.MaybeEmpty(t.String()),
																					Section: t.MaybeEmpty(t.String()),
																					TMAddressType: t.MaybeEmpty(
																						t.String()
																					),
																					TMBuildingName: t.MaybeEmpty(
																						t.String()
																					),
																					TMFloorNo: t.MaybeEmpty(t.String()),
																					TMForeignAddrFlag: t.MaybeEmpty(
																						t.String()
																					),
																					TMForeignCountry: t.MaybeEmpty(
																						t.String()
																					),
																					TMForeignState: t.MaybeEmpty(
																						t.String()
																					),
																					TMStreetType: t.MaybeEmpty(t.String())
																				})
																			)
																		})
																	})
																)
															)
													})
												),
											'TmOrderEntry-LineItemsIntegration': t.MaybeEmpty(
												t.Array(
													t.Object({
														TMHSBAOfferingsProductName: t.MaybeEmpty(
															t.String()
														),
														TMVLANId: t.MaybeEmpty(t.String()),
														ActionCode: t.MaybeEmpty(t.String()),
														CurrencyCode: t.MaybeEmpty(t.String()),
														ExchangeDate: t.MaybeEmpty(t.String()),
														ItemPrice: t.MaybeEmpty(t.String()),
														LineNumber: t.MaybeEmpty(t.String()),
														MRCCxTotal: t.MaybeEmpty(t.String()),
														NRCCxTotal: t.MaybeEmpty(t.String()),
														OrderHeaderId: t.MaybeEmpty(t.String()),
														PartNumber: t.MaybeEmpty(t.String()),
														ProdPromName: t.MaybeEmpty(t.String()),
														Product: t.MaybeEmpty(t.String()),
														ProductType: t.MaybeEmpty(t.String()),
														QuantityRequested: t.MaybeEmpty(t.String()),
														ServiceId: t.MaybeEmpty(t.String()),
														Status: t.MaybeEmpty(t.String()),
														TMEquipmentManufacturer: t.MaybeEmpty(t.String()),
														TMEquipmentModel: t.MaybeEmpty(t.String()),
														TMMACAddress: t.MaybeEmpty(t.String()),
														TMEquipmentType: t.MaybeEmpty(t.String()),
														ListOfTmOrderItemXaIntegration: t.Object({
															TmOrderItemXaIntegration: t.Array(
																t.Object({
																	ActionCode: t.MaybeEmpty(t.String()),
																	Name: t.MaybeEmpty(t.String()),
																	Value: t.MaybeEmpty(t.String())
																})
															)
														}),
														'ListOfTmCutAssetMgmt-ServiceMeterIntegration':
															t.Object({
																'TmCutAssetMgmt-ServiceMeterIntegration':
																	t.Array(
																		t.Object({
																			TMMACAddress: t.MaybeEmpty(t.String()),
																			ServicePointId: t.MaybeEmpty(t.String()),
																			TMEquipmentManufacturer: t.MaybeEmpty(
																				t.String()
																			),
																			TMEquipmentModel: t.MaybeEmpty(
																				t.String()
																			),
																			ListOfTmCutAddressIntegration: t.Object({
																				TmCutAddressIntegration: t.Array(
																					t.Object({
																						Id: t.MaybeEmpty(t.String()),
																						ApartmentNumber: t.MaybeEmpty(
																							t.String()
																						),
																						City: t.MaybeEmpty(t.String()),
																						Country: t.MaybeEmpty(t.String()),
																						PostalCode: t.MaybeEmpty(
																							t.String()
																						),
																						State2: t.MaybeEmpty(t.String()),
																						StreetName: t.MaybeEmpty(
																							t.String()
																						),
																						Section: t.MaybeEmpty(t.String()),
																						TMAddressType: t.MaybeEmpty(
																							t.String()
																						),
																						TMBuildingName: t.MaybeEmpty(
																							t.String()
																						),
																						TMFloorNo: t.MaybeEmpty(t.String()),
																						TMForeignAddrFlag: t.MaybeEmpty(
																							t.String()
																						),
																						TMForeignCountry: t.MaybeEmpty(
																							t.String()
																						),
																						TMForeignState: t.MaybeEmpty(
																							t.String()
																						),
																						TMStreetType: t.String()
																					})
																				)
																			})
																		})
																	)
															})
													})
												)
											)
										})
									)
								)
							})
						),
						ListOfTmActionIntegration: t.Object({
							TmActionIntegration: t.Array(
								t.Object({
									SWIFTFlag: t.MaybeEmpty(t.String()),
									ActivityUID: t.MaybeEmpty(t.String()),
									Due: t.MaybeEmpty(t.String()),
									Planned: t.MaybeEmpty(t.String()),
									PlannedCompletion: t.MaybeEmpty(t.String()),
									Status: t.MaybeEmpty(t.String()),
									SubType: t.MaybeEmpty(t.String()),
									Type: t.MaybeEmpty(t.String()),
									ContactId: t.MaybeEmpty(t.String()),
									ServiceRegion: t.MaybeEmpty(t.String()),
									ContactName: t.MaybeEmpty(t.String()),
									ContactOfficePhone: t.MaybeEmpty(t.String()),
									ContactHomePhone: t.MaybeEmpty(t.String()),
									ContactCellularPhone: t.MaybeEmpty(t.String()),
									ContactEmailAddress: t.MaybeEmpty(t.String()),
									TMBTUReturned: t.String()
								})
							)
						})
					})
				),
				// For Mobile
				DMPOrderDetails: t.MaybeEmpty(
					t.Object({
						ErrorDescription: t.MaybeEmpty(t.String()),
						ResponseStatus: t.MaybeEmpty(t.String()),
						OrderDetail: t.MaybeEmpty(
							t.Array(
								t.Object({
									AccountNo: t.MaybeEmpty(t.String()),
									AcctProdType: t.MaybeEmpty(t.String()),
									AccType: t.MaybeEmpty(t.String()),
									DeliveryMode: t.MaybeEmpty(t.String()),
									DeliveryPartnerId: t.MaybeEmpty(t.String()),
									DeliveryPartnerName: t.MaybeEmpty(t.String()),
									DeliveryTrackingNo: t.MaybeEmpty(t.String()),
									DeliveryTrackingUrl: t.MaybeEmpty(t.String()),
									DmpOrderCreationDate: t.MaybeEmpty(t.String()),
									DmpOrderNo: t.MaybeEmpty(t.String()),
									EcomRefNo: t.MaybeEmpty(t.String()),
									LastOrderChangeDate: t.MaybeEmpty(t.String()),
									OrderReceiveDate: t.MaybeEmpty(t.String()),
									OrderStatus: t.MaybeEmpty(t.String()),
									OrderStatusName: t.MaybeEmpty(t.String()),
									OrderSubmissionDate: t.MaybeEmpty(t.String()),
									OrderType: t.MaybeEmpty(t.String()),
									ReasonCode: t.MaybeEmpty(t.String()),
									ReasonName: t.MaybeEmpty(t.String()),
									ReliefId: t.MaybeEmpty(t.String()),
									SalesRep: t.MaybeEmpty(t.String()),
									StaffID: t.MaybeEmpty(t.String()),
									StartResellerCode: t.MaybeEmpty(t.String()),
									StartSalesChannel: t.MaybeEmpty(t.String()),
									StartSalesmanID: t.MaybeEmpty(t.String()),
									CustomerProfile: t.MaybeEmpty(
										t.Object({
											AlterPhoneNo: t.MaybeEmpty(t.String()),
											CompanyBRN: t.MaybeEmpty(t.String()),
											CompanyName: t.MaybeEmpty(t.String()),
											CustType: t.MaybeEmpty(t.String()),
											Email: t.MaybeEmpty(t.String()),
											IdNo: t.MaybeEmpty(t.String()),
											IdType: t.MaybeEmpty(t.String()),
											MobileNo: t.MaybeEmpty(t.String()),
											Name: t.MaybeEmpty(t.String()),
											PassportIssueCountry: t.MaybeEmpty(t.String()),
											PassportType: t.MaybeEmpty(t.String())
										})
									),
									PaymentInfo: t.MaybeEmpty(
										t.Object({
											PaymentMode: t.MaybeEmpty(t.String()),
											PaymentModeName: t.MaybeEmpty(t.String()),
											PaymentAmount: t.MaybeEmpty(t.String()),
											PaymentNo: t.MaybeEmpty(t.String()),
											PaymentChannel: t.MaybeEmpty(t.String()),
											BankTransactionSn: t.MaybeEmpty(t.String())
										})
									),
									PickupInfo: t.MaybeEmpty(
										t.Object({
											PickupBranchCode: t.MaybeEmpty(t.String()),
											PickupBranchName: t.MaybeEmpty(t.String()),
											PickupDate: t.MaybeEmpty(t.String()),
											PickupTime: t.MaybeEmpty(t.String())
										})
									),
									ServiceLineInfoList: t.MaybeEmpty(
										t.Array(
											t.Object({
												AcctCode: t.MaybeEmpty(t.String()),
												AcctFlag: t.MaybeEmpty(t.String()),
												DeviceAcknowledge: t.MaybeEmpty(t.String()),
												DeviceCode: t.MaybeEmpty(t.String()),
												DeviceDiscountValidityEnd: t.MaybeEmpty(t.String()),
												DeviceDiscountValidityStart: t.MaybeEmpty(t.String()),
												DeviceInstallmentPeriod: t.MaybeEmpty(t.String()),
												DeviceName: t.MaybeEmpty(t.String()),
												DeviceSn: t.MaybeEmpty(t.String()),
												MnpAging: t.MaybeEmpty(t.String()),
												MnpLineType: t.MaybeEmpty(t.String()),
												MnpMainLine: t.MaybeEmpty(t.String()),
												MnpMsisdn: t.MaybeEmpty(t.String()),
												MnpStatus: t.MaybeEmpty(t.String()),
												MnpStatusName: t.MaybeEmpty(t.String()),
												MnpTelco: t.MaybeEmpty(t.String()),
												Msisdn: t.MaybeEmpty(t.String()),
												PortAdvise: t.MaybeEmpty(t.String()),
												ProductCode: t.MaybeEmpty(t.String()),
												ProductName: t.MaybeEmpty(t.String()),
												ProductType: t.MaybeEmpty(t.String()),
												RejectCode: t.MaybeEmpty(t.String()),
												RejectComment: t.MaybeEmpty(t.String()),
												RejectDesc: t.MaybeEmpty(t.String()),
												SettleAdvise: t.MaybeEmpty(t.String()),
												SimAcknowledge: t.MaybeEmpty(t.String()),
												TransTime: t.MaybeEmpty(t.String()),
												ServiceItemsFeeList: t.Array(
													t.Object({
														Amount: t.MaybeEmpty(t.String()),
														ItemFee: t.MaybeEmpty(t.String()),
														TaxCode: t.MaybeEmpty(t.String())
													})
												)
											})
										)
									)
								})
							)
						)
					})
				)
			})
		)
	})
});

export type Wso2OrderTrackingRes = Static<typeof wso2OrderTrackingResSchema>;
