import { type Static, t } from 'elysia';

export const wso2RetrieveNTTReqSchema = t.Object({
	RequestID: t.String(),
	ServiceNoVobb: t.String(),
	ServiceNoDell: t.String()
});

export type Wso2RetrieveNTTReq = Static<typeof wso2RetrieveNTTReqSchema>;

export const wso2RetrieveNTTResSchema = t.MaybeEmpty(
	t.Object({
		errorCode: t.Optional(t.String()),
		errorMessage: t.Optional(t.String()),
		Description: t.Optional(t.String()),
		ETTR: t.Optional(t.String()),
		ErrorCode: t.Optional(t.String()),
		ErrorMessage: t.Optional(t.String()),
		FaultCategory: t.Optional(t.String()),
		LRID: t.Optional(t.String()),
		NEID: t.Optional(t.String()),
		NTTID: t.Optional(t.String()),
		NoOfCTT: t.Optional(t.String()),
		RelatedCTTID: t.Optional(t.String()),
		RequestID: t.Optional(t.String()),
		ResponseID: t.Optional(t.String()),
		ServiceImpact: t.Optional(t.String()),
		ServiceNo: t.Optional(t.String()),
		Symptom: t.Optional(t.Array(t.String()))
	})
);

export type Wso2RetrieveNTTRes = Static<typeof wso2RetrieveNTTResSchema>;
