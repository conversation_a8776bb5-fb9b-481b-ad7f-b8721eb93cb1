import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../config/db.config';
import { envConfig } from '../../../config/env.config';
import { StatusCodeEnum } from '../../../enum/statusCode.enum';
import { UE_ERROR } from '../../../middleware/error';
import { fetchApi } from '../../helper/fetchApi.helper';
import { getApimToken } from '../helper/apimToken.helper';
import type { Wso2BodyReq } from '../helper/schemas/api/wso2Base.schema';
import type {
	Wso2EmailAttachmentReq,
	Wso2EmailReq
} from './schemas/api/wso2Email.schema';
import type { Wso2SmsReq, Wso2SmsRes } from './schemas/api/wso2Sms.schema';
import { wso2EmailTxnHistoryTableSchema } from './schemas/db/wso2EmailTxnHistory.schema';

class Wso2NotificationIntegration {
	private db: NodePgDatabase;
	private integrationId: string;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
		this.db = getDbInstance();
	}

	async getWso2SendEmail(
		bodyRequest: Wso2EmailReq,
		txnId: string,
		txnType: string
	): Promise<boolean> {
		const url: string = envConfig().WSO2_SEND_EMAIL;
		const token: string = await getApimToken();
		const xBpid: string = `${Math.random().toString(36).slice(2)}`;
		const xBoid: string = `${Math.random().toString(36).slice(2)}`;
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': xBpid,
				'x-boid': xBoid,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.text();

		if (!res.ok || resBody !== '') {
			await this.saveToDb(txnId, txnType, xBpid, xBoid, false, bodyRequest);
			return false;
		}
		await this.saveToDb(txnId, txnType, xBpid, xBoid, true, bodyRequest);
		return true;
	}

	async getWso2SendEmailWithAttachment(
		bodyRequest: Wso2EmailReq,
		attachments: Wso2EmailAttachmentReq,
		txnId: string,
		txnType: string
	): Promise<boolean> {
		const url: string = envConfig().WSO2_SEND_EMAIL_WITH_ATTACHMENT;
		const token: string = await getApimToken();
		const xBpid: string = `${Math.random().toString(36).slice(2)}`;
		const xBoid: string = `${Math.random().toString(36).slice(2)}`;

		const form = new FormData();

		// Add email request as JSON
		form.append('postdata', JSON.stringify(bodyRequest));

		// Add attachments
		for (const attachment of attachments) {
			// Convert Elysia File to Blob
			const blob = new Blob([await attachment.arrayBuffer()], {
				type: attachment.type || 'application/octet-stream'
			});

			form.append('fileName', blob, attachment.name);
		}

		const res = await fetchApi(this.integrationId, url, {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${token}`,
				'x-bpid': xBpid,
				'x-boid': xBoid,
				'x-calling-application': 'UE',
				Accept: 'text/plain, application/json, application/*+json, */*'
			},
			body: form
		});
		const resBody = await res.text();

		if (!res.ok || resBody !== '') {
			await this.saveToDb(txnId, txnType, xBpid, xBoid, false, bodyRequest);
			return false;
		}
		await this.saveToDb(txnId, txnType, xBpid, xBoid, true, bodyRequest);
		return true;
	}

	async getWso2SendSms(bodyRequest: Wso2SmsReq): Promise<Wso2SmsRes> {
		const url: string = envConfig().WSO2_SEND_SMS;
		const token: string = await getApimToken();

		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};

		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'WSO2_SEND_SMS response NOK',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return resBody as Wso2SmsRes;
	}

	private async saveToDb(
		txnId: string,
		txnType: string,
		xBpid: string,
		xBoid: string,
		isEmailSent: boolean,
		bodyRequest: Wso2EmailReq
	) {
		await this.db.insert(wso2EmailTxnHistoryTableSchema).values({
			TxnId: txnId,
			TxnType: txnType,
			RecipientEmail: bodyRequest.to,
			XBPID: xBpid,
			XBOID: xBoid,
			RequestBody: JSON.stringify(bodyRequest),
			IsEmailSent: isEmailSent
		});
	}
}

export default Wso2NotificationIntegration;
