import { type Static, t } from 'elysia';

export const wso2NovaAddressObjSchema = t.Object({
	City: t.String(),
	Country: t.String(),
	PostalCode: t.String(),
	State: t.String(),
	UnitNumber: t.String(),
	StreetName: t.String(),
	StreetType: t.String(),
	TMAddressType: t.String(),
	BuildingName: t.Optional(t.String()),
	FloorNo: t.Optional(t.String()),
	Section: t.Optional(t.String())
});

export const wso2BillingProfileNOVASchema = t.Object({
	BillingProfileSRCreate: t.Object({
		TmServiceRequestIntegration: t.Object({
			TMBillingAccount: t.String(),
			AccountId: t.String(),
			TMBillingProfileId: t.String(),
			ListOfTmEaiSrXmIntegration: t.Object({
				TmEaiSrXmIntegration: t.Array(
					t.Object({
						TMOldNew: t.String(),
						BillingEmailTO: t.String(),
						BillMedia: t.Optional(t.String()),
						MobileNo: t.Optional(t.String()),
						TMAddressId: t.Optional(t.String())
					})
				)
			})
		})
	}),
	SBLAddressCreate: t.Optional(wso2NovaAddressObjSchema)
});

export const wso2IcpAddressObjSchema = t.Object({
	Section: t.String(),
	StreetAddress: t.String(),
	TMBAddressNum: t.String(),
	TMBAddressType: t.String(),
	TMBBuildingName: t.String(),
	TMBFloor: t.String(),
	TMBForeignCity: t.String(),
	TMBForeignPostalCode: t.String(),
	TMBNewPostalRegionCode: t.String(),
	TMBStreetType: t.String(),
	Province: t.String(),
	PostalCode: t.String(),
	City: t.String(),
	State: t.String(),
	Country: t.String()
});

export const wso2BillingProfileICPSchema = t.Object({
	ICPSRCreate: t.Object({
		TmbEaiServiceRequest: t.Object({
			BillingAccountNo: t.String(),
			TMBNewBillMedia: t.Optional(t.String()),
			TMBBillingMedia: t.Optional(t.String()),
			TMBEmailAddress: t.Optional(t.String()),
			TMBNewMobileNumber: t.Optional(t.String()),
			TMBOldAddressId: t.Optional(t.String())
		})
	}),
	ICPCreateCUTAddress: t.Optional(
		t.Object({
			TmbSrCutAddress: t.Array(wso2IcpAddressObjSchema)
		})
	)
});

const wso2UpdateBillingProfileReqSchema = t.Object({
	UpdateBillingProfileRequest: t.Object({
		SystemName: t.String(),
		PerformAction: t.String(),
		IdType: t.String(),
		IdValue: t.String(),
		BillingAccountNo: t.String(),
		BillMediaFlag: t.Optional(t.String()),
		AddressFlag: t.Optional(t.String()),
		MobileNoFlag: t.Optional(t.String()),
		TMBEmailAddress: t.Optional(t.String()),
		BillingProfileNOVA: t.Optional(wso2BillingProfileNOVASchema),
		BillingProfileICP: t.Optional(wso2BillingProfileICPSchema)
	})
});

export type Wso2UpdateBillingProfileReq = Static<
	typeof wso2UpdateBillingProfileReqSchema
>;

export const wso2UpdateBillingProfileResSchema = t.Object({
	Status: t.Object({
		Type: t.String(),
		Code: t.String(),
		Message: t.Nullable(t.String())
	}),
	Response: t.Object({
		UpdateBillingProfileResponse: t.Object({
			BillingProfileSRCreateResponse: t.Array(
				t.Object({
					SRNumber: t.String(),
					ReturnCode: t.String(),
					ReturnMessage: t.Nullable(t.String())
				})
			)
		})
	})
});

export type Wso2UpdateBillingProfileRes = Static<
	typeof wso2UpdateBillingProfileResSchema
>;
