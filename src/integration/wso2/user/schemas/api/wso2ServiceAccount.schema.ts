import { type Static, t } from 'elysia';

const wso2ServiceAccountReqSchema = t.Object({
	idType: t.String(),
	idValue: t.String(),
	BillingAccountNo: t.String(),
	SystemName: t.String()
});

export type Wso2ServiceAccountReq = Static<typeof wso2ServiceAccountReqSchema>;

const wso2SubscribedAddOnsSchema = t.Optional(
	t.Array(
		t.Object({
			Name: t.Optional(t.String()),
			Code: t.Optional(t.String()),
			Description: t.Optional(t.String()),
			Category: t.Optional(t.String()),
			Price: t.Optional(t.String()),
			ChargeType: t.Optional(t.String()),
			ServiceType: t.Optional(t.String()),
			IsActive: t.Optional(t.String()),
			Quantity: t.Optional(t.String()),
			LeasePeriod: t.Optional(t.String())
		})
	)
);

export type Wso2SubscribedAddOns = Static<typeof wso2SubscribedAddOnsSchema>;

const wso2ServiceAccountOliSchema = t.Object({
	Id: t.Optional(t.String()),
	IntegrationId: t.Optional(t.String()),
	Status: t.Optional(t.String()),
	AccountNo: t.Optional(t.String()),
	BillingAccountNumber: t.Optional(t.String()),
	BillingAccountId: t.Optional(t.String()),
	PrimaryBillingProfileId: t.Optional(t.String()),
	ExpDiscFlg: t.Optional(t.String()),
	TMRootPromotionInstanceId: t.Optional(t.String()),
	InstallDate: t.Optional(t.String()),
	ParentAssetId: t.Optional(t.String()),
	ParentAssetName: t.Optional(t.String()),
	ProdPromInstanceId: t.Optional(t.String()),
	ProdPromName: t.Optional(t.String()),
	ProductDescription: t.Optional(t.String()),
	ProductId: t.Optional(t.String()),
	ProductName: t.Optional(t.String()),
	ProductPartNumber: t.Optional(t.String()),
	ProductDisplayName: t.Optional(t.String()),
	ProductType: t.Optional(t.String()),
	Quantity: t.Optional(t.String()),
	RootAssetId: t.Optional(t.String()),
	RootIntegrationId: t.Optional(t.String()),
	RootProductId: t.Optional(t.String()),
	SerialNumber: t.Optional(t.String()),
	ServicePointId: t.Optional(t.String()),
	StartDate: t.Optional(t.String()),
	ContractStartDate: t.Optional(t.String()),
	Type: t.Optional(t.String()),
	TMServiceInstanceId: t.Optional(t.String()),
	TMPortalPromotionName: t.Optional(t.String()),
	TMPortalPromotionStartDate: t.Optional(t.String()),
	TMPortalPromotionEndDate: t.Optional(t.String()),
	TMServiceTaxFlag: t.Optional(t.String()),
	TMRGVersion: t.Optional(t.String()),
	TMRGMode: t.Optional(t.String()),
	SmartDeviceFlag: t.Optional(t.String()),
	'ListOfTmAssetMgmt-AssetXaIntegration': t.Optional(
		t.Object({
			'TmAssetMgmt-AssetXaIntegration': t.Optional(
				t.Array(
					t.Object({
						AttritbuteParentId: t.Optional(t.String()),
						Name: t.Optional(t.String()),
						Value: t.Optional(t.String())
					})
				)
			)
		})
	)
});

export type Wso2ServiceAccountOli = Static<typeof wso2ServiceAccountOliSchema>;

const wso2ServiceAccountMoliSchema = t.Object({
	Id: t.Optional(t.String()),
	IntegrationId: t.Optional(t.String()),
	Status: t.Optional(t.String()),
	AccountNo: t.Optional(t.String()),
	BillingAccountNumber: t.Optional(t.String()),
	BillingAccountId: t.Optional(t.String()),
	PrimaryBillingProfileId: t.Optional(t.String()),
	ExpDiscFlg: t.Optional(t.String()),
	TMRootPromotionInstanceId: t.Optional(t.String()),
	InstallDate: t.Optional(t.String()),
	ParentAssetId: t.Optional(t.String()),
	ParentAssetName: t.Optional(t.String()),
	ProdPromInstanceId: t.Optional(t.String()),
	ProdPromName: t.Optional(t.String()),
	ProductDescription: t.Optional(t.String()),
	ProductId: t.Optional(t.String()),
	ProductName: t.Optional(t.String()),
	ProductPartNumber: t.Optional(t.String()),
	ProductDisplayName: t.Optional(t.String()),
	ProductType: t.Optional(t.String()),
	Quantity: t.Optional(t.String()),
	RootAssetId: t.Optional(t.String()),
	RootIntegrationId: t.Optional(t.String()),
	RootProductId: t.Optional(t.String()),
	SerialNumber: t.Optional(t.String()),
	ServicePointId: t.Optional(t.String()),
	StartDate: t.Optional(t.String()),
	ContractStartDate: t.Optional(t.String()),
	Type: t.Optional(t.String()),
	DatCode: t.Optional(t.String()),
	TMServiceInstanceId: t.Optional(t.String()),
	TMPortalPromotionName: t.Optional(t.String()),
	TMPortalPromotionStartDate: t.Optional(t.String()),
	TMPortalPromotionEndDate: t.Optional(t.String()),
	TMServiceTaxFlag: t.Optional(t.String()),
	TMRGVersion: t.Optional(t.String()),
	'TmCutAssetMgmt-ServiceMeterIntegration': t.Optional(
		t.Object({
			TMServiceTaxFlag: t.Optional(t.String()),
			TMReservationId: t.Optional(t.String()),
			ServicePointId: t.Optional(t.String()),
			TMServiceInstanceId: t.Optional(t.String()),
			ProductName: t.Optional(t.String()),
			TMExchangeName: t.Optional(t.String()),
			TMPremiseType: t.Optional(t.String()),
			TMBTUPortNumber: t.Optional(t.String()),
			TMCPEVendor: t.Optional(t.String()),
			TMAccessTechnology: t.Optional(t.String()),
			TMCPEType: t.Optional(t.String()),
			TMDPLocation: t.Optional(t.String()),
			TmCutAddressIntegration: t.Optional(
				t.Object({
					Id: t.Optional(t.String()),
					ApartmentNumber: t.Optional(t.String()),
					City: t.Optional(t.String()),
					Country: t.Optional(t.String()),
					IntegrationId: t.Optional(t.String()),
					PostalCode: t.Optional(t.String()),
					State: t.Optional(t.String()),
					StreetName: t.Optional(t.String()),
					Section: t.Optional(t.String()),
					TMAddressType: t.Optional(t.String()),
					TMBuildingName: t.Optional(t.String()),
					TMFloorNo: t.Optional(t.String()),
					TMForeignAddrFlag: t.Optional(t.String()),
					TMForeignCountry: t.Optional(t.String()),
					TMForeignState: t.Optional(t.String()),
					StreetType: t.Optional(t.String()),
					TMPremiseType: t.Optional(t.String())
				})
			)
		})
	),
	ServiceAccountOli: t.Optional(t.Array(wso2ServiceAccountOliSchema))
});

export type Wso2ServiceAccountMoli = Static<
	typeof wso2ServiceAccountMoliSchema
>;

const wso2ServiceAccountObjSchema = t.Object({
	Id: t.Optional(t.String()),
	IntegrationId: t.Optional(t.String()),
	Status: t.Optional(t.String()),
	AccountNo: t.Optional(t.String()),
	BillingAccountNumber: t.Optional(t.String()),
	BillingAccountId: t.Optional(t.String()),
	ExpDiscFlg: t.Optional(t.String()),
	UnitLot: t.Optional(t.String()),
	FloorNo: t.Optional(t.String()),
	BuildingName: t.Optional(t.String()),
	StreetType: t.Optional(t.String()),
	StreetName: t.Optional(t.String()),
	Section: t.Optional(t.String()),
	Postcode: t.Optional(t.String()),
	City: t.Optional(t.String()),
	State: t.Optional(t.String()),
	Country: t.Optional(t.String()),
	Products: t.Optional(
		t.Array(
			t.Object({
				ProductName: t.Optional(t.String()),
				SerialNumber: t.String()
			})
		)
	),
	ProductType: t.Optional(t.String()),
	ContractStartDate: t.Optional(t.String()),
	ContractEndDate: t.Optional(t.String()),
	ContractTenure: t.Optional(t.String()),
	ServiceID: t.Optional(t.String()),
	SubscribedAddOns: t.Optional(wso2SubscribedAddOnsSchema),
	PrimaryBillingProfileId: t.Optional(t.String()),
	TMRootPromotionInstanceId: t.Optional(t.String()),
	ParentAssetId: t.Optional(t.String()),
	ParentAssetName: t.Optional(t.String()),
	ProdPromInstanceId: t.Optional(t.String()),
	ProdPromName: t.Optional(t.String()),
	ProductDescription: t.Optional(t.String()),
	ProductId: t.Optional(t.String()),
	ProductName: t.Optional(t.String()),
	ProductPartNumber: t.Optional(t.String()),
	ProductDisplayName: t.Optional(t.String()),
	Quantity: t.Optional(t.String()),
	RootAssetId: t.Optional(t.String()),
	RootIntegrationId: t.Optional(t.String()),
	RootProductId: t.Optional(t.String()),
	SerialNumber: t.Optional(t.String()),
	ServicePointId: t.Optional(t.String()),
	StartDate: t.Optional(t.String()),
	Type: t.Optional(t.String()),
	DatCode: t.Optional(t.String()),
	TMServiceInstanceId: t.Optional(t.String()),
	TMPortalPromotionName: t.Optional(t.String()),
	TMPortalPromotionStartDate: t.Optional(t.String()),
	TMPortalPromotionEndDate: t.Optional(t.String()),
	TMServiceTaxFlag: t.Optional(t.String()),
	TMRGVersion: t.Optional(t.String()),
	'TmCutAssetMgmt-ServiceMeterIntegration': t.Optional(
		t.Object({
			TMServiceTaxFlag: t.Optional(t.String()),
			TMReservationId: t.Optional(t.String()),
			ServicePointId: t.Optional(t.String()),
			TMServiceInstanceId: t.Optional(t.String()),
			ProductName: t.Optional(t.String()),
			TMExchangeName: t.Optional(t.String()),
			TMPremiseType: t.Optional(t.String()),
			TMAccessTechnology: t.Optional(t.String()),
			TmCutAddressIntegration: t.Optional(
				t.Object({
					Id: t.Optional(t.String()),
					ApartmentNumber: t.Optional(t.String()),
					City: t.Optional(t.String()),
					Country: t.Optional(t.String()),
					IntegrationId: t.Optional(t.String()),
					PostalCode: t.Optional(t.String()),
					State: t.Optional(t.String()),
					StreetName: t.Optional(t.String()),
					Section: t.Optional(t.String()),
					TMAddressType: t.Optional(t.String()),
					TMBuildingName: t.Optional(t.String()),
					TMFloorNo: t.Optional(t.String()),
					TMForeignAddrFlag: t.Optional(t.String()),
					TMForeignCountry: t.Optional(t.String()),
					TMForeignState: t.Optional(t.String()),
					StreetType: t.Optional(t.String()),
					TMPremiseType: t.Optional(t.String())
				})
			)
		})
	),
	ServiceAccountMoli: t.Optional(t.Array(wso2ServiceAccountMoliSchema))
});

export type Wso2ServiceAccountObj = Static<typeof wso2ServiceAccountObjSchema>;

const wso2ServiceAccountSchema = t.Array(wso2ServiceAccountObjSchema);

export type Wso2ServiceAccount = Static<typeof wso2ServiceAccountSchema>;

export const wso2ServiceAccountResObjSchema = t.Object({
	ServiceAccount: t.Optional(wso2ServiceAccountSchema)
});

export const wso2ServiceAccountResSchema = t.Nullable(
	t.Object({
		Status: t.Optional(
			t.Object({
				Type: t.Optional(t.String()),
				Code: t.Optional(t.String()),
				Message: t.Optional(t.String())
			})
		),
		Response: t.Optional(wso2ServiceAccountResObjSchema)
	})
);

export type Wso2ServiceAccountRes = Static<typeof wso2ServiceAccountResSchema>;
