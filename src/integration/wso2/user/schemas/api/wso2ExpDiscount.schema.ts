import { type Static, t } from 'elysia';

export const wso2ExpDiscountReqSchema = t.Object({
	AccountNo: t.String(),
	Logins: t.Array(t.String())
});

export type Wso2ExpDiscountReq = Static<typeof wso2ExpDiscountReqSchema>;

export const wso2ExpDiscountResSchema = t.Object({
	Status: t.Object({ Type: t.String(), Code: t.String(), Message: t.String() }),
	Response: t.Array(
		t.Object({
			Account_No: t.String(),
			Login: t.String(),
			Service_Id: t.String(),
			Bundle_Name: t.Nullable(t.String()),
			Descr: t.String(),
			Disc_Amount: t.String(),
			CreateD_T: t.String(),
			Effective_T: t.String(),
			End_T: t.String(),
			Disc_Validity: t.String(),
			Day_To_Exp: t.String()
		})
	)
});

export type Wso2ExpDiscountRes = Static<typeof wso2ExpDiscountResSchema>;
