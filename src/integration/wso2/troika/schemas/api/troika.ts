import { type Static, t } from 'elysia';

const wso2CreateTroikaReqSchema = t.Object({
	addressId: t.Optional(t.String({ examples: ['19511100'] })),
	buildingName: t.String({ examples: ['PV1 PERDANA VILLA'] }),
	city: t.String({ examples: ['Petaling Jaya'] }),
	companyName: t.String({ examples: ['UNIFI Digital Solution'] }),
	creatorGroup: t.String({ examples: ['UNIFI Digital Solution'] }),
	creatorGroupCategory: t.String({ examples: ['UNIFI Digital Solution'] }),
	creatorId: t.String({ examples: ['TS20755'] }),
	creatorName: t.String({ examples: ['UNIFI Digital Solution'] }),
	customerId: t.String({ examples: ['901010-10-1010'] }),
	customerIdType: t.String({ examples: ['New NRIC'] }),
	customerName: t.String({ examples: ['Lim Kok Wing'] }),
	customerSegment: t.String({ examples: ['UNIFI'] }),
	demandId: t.String({ examples: ['UFNH-IDJTLI4II'] }),
	exchangeId: t.Optional(t.String({ examples: ['BAA'] })),
	floorNo: t.String({ examples: ['11'] }),
	lat: t.String({ examples: ['3.0123'] }),
	lon: t.String({ examples: ['101.0123'] }),
	neId: t.Optional(t.String({ examples: ['TDI_C999_006_DP0001'] })),
	postCode: t.String({ examples: ['52100'] }),
	section: t.Optional(t.String()),
	sourceSystem: t.String({ examples: ['UNIFIPORTAL'] }),
	state: t.String({ examples: ['Melaka'] }),
	streetName: t.String({ examples: ['Asam Kumbang'] }),
	streetType: t.String({ examples: ['Jalan'] }),
	ticketCategory: t.String({ examples: ['ADDRESS NOT FOUND'] }),
	unitId: t.String({ examples: ['LOT 2722-B'] }),
	outsideRadius: t.Optional(t.String()),
	dpLat: t.Optional(t.String()),
	dpLon: t.Optional(t.String()),
	premiseCategory: t.String({ examples: ['Landed/Linked House'] }),
	hybridTagging: t.Optional(t.String()),
	segmentType: t.String({ examples: ['SME', 'Consumer'] }),
	addressServiceCategory: t.Optional(t.String())
});

export type Wso2CreateTroikaReq = Static<typeof wso2CreateTroikaReqSchema>;

export const wso2TroikaResResourceSchema = t.Object({
	demandId: t.String(),
	fdp: t.Optional(t.String({ nullable: true })),
	nbaResult: t.String(),
	portNumber: t.Optional(t.String({ nullable: true })),
	reasonMessage: t.String({ examples: ['No Main DP lat/long given'] }),
	rfsDate: t.Optional(t.String({ examples: ['2023-01-01'], nullable: true })),
	troikaId: t.String({ examples: ['TRO-12341234'] }),
	unifiAir: t.Optional(t.String({ examples: ['4G', 'N'], nullable: true }))
});

export const wso2CreateTroikaResSchema = t.Object({
	status: t.Integer(),
	count: t.Integer(),
	success: t.Boolean(),
	message: t.String(),
	troikaId: t.String({ examples: ['TRO-1-1000002596'] }),
	nbaResult: t.String(),
	numberOfRequest: t.Optional(t.Integer({ nullable: true })),
	fdp: t.Optional(t.String({ nullable: true })),
	timestamp: t.Optional(t.String({ nullable: true })),
	error: t.Optional(t.String({ nullable: true })),
	path: t.Optional(t.String({ nullable: true })),
	resource: wso2TroikaResResourceSchema
});

export type Wso2CreateTroikaRes = Static<typeof wso2CreateTroikaResSchema>;

const wso2UpdateTroikaReqSchema = t.Object({
	troikaId: t.String({ examples: ['TRO-123456'] }),
	demandId: t.String({ examples: ['UNFH-123456'] }),
	demandAction: t.String({ examples: ['CANCEL', 'REMARK', 'ATTACHMENT'] }),
	requestorId: t.String({ examples: ['TS20755'] }),
	remark: t.Optional(t.String()),
	fileName: t.Optional(t.String()),
	attachment: t.Optional(t.String())
});

export type Wso2UpdateTroikaReq = Static<typeof wso2UpdateTroikaReqSchema>;

export const wso2UpdateTroikaResSchema = t.Object({
	count: t.Integer(),
	resource: t.Object({
		troikaId: t.String(),
		demandId: t.String(),
		demandStatus: t.String(),
		reasonMessage: t.String()
	}),
	status: t.Integer(),
	success: t.Boolean(),
	messages: t.String()
});

export type Wso2UpdateTroikaRes = Static<typeof wso2UpdateTroikaResSchema>;

const wso2QueryTroikaReqSchema = t.Object({
	troikaId: t.String(),
	demandId: t.String()
});

export type Wso2QueryTroikaReq = Static<typeof wso2QueryTroikaReqSchema>;

export const wso2QueryTroikaResSchema = t.Object({
	status: t.Number(),
	success: t.Boolean(),
	resource: t.Object({
		troikaId: t.String({ examples: ['TRO-1-1100186257'] }),
		demandStatus: t.String({ examples: ['CANCEL'] }),
		reasonMessage: t.Optional(
			t.String({ nullable: true, examples: ['No Main DP lat/long given'] })
		),
		nbaSuggestion: t.String({ examples: ['NO MAINDP LATLONG'] }),
		addressId: t.String({ examples: ['17364579'] }),
		lov: t.Optional(t.String({ nullable: true })),
		ndRemark: t.Optional(t.String({ nullable: true })),
		fdp: t.String({ examples: ['SK_C007_DP051A'] }),
		portNumber: t.Optional(t.String({ nullable: true })),
		rfsDate: t.String({ examples: ['2024-12-30T00:00:00.000+08:00'] })
	})
});

export type Wso2QueryTroikaRes = Static<typeof wso2QueryTroikaResSchema>;
