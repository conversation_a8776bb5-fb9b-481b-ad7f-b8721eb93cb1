import randomString from 'random-string-gen';
import { envConfig } from '../../../config/env.config';
import { StatusCodeEnum } from '../../../enum/statusCode.enum';
import { UE_ERROR } from '../../../middleware/error';
import { fetchApi } from '../../helper/fetchApi.helper';
import { getApimToken } from '../helper/apimToken.helper';
import { getMmagToken } from '../helper/mmagToken.helper';
import type { Wso2BodyReq } from '../helper/schemas/api/wso2Base.schema';
import type {
	Wso2CheckStockReq,
	Wso2CheckStockRes
} from './schemas/api/wso2CheckStock.schema';
import type {
	Wso2MwCreateOrderReq,
	Wso2MwCreateOrderSubmitRes
} from './schemas/api/wso2CreateAddOnsOrder';
import type {
	Wso2MWAppointmentIDReq,
	Wso2MWAppointmentIDRes
} from './schemas/api/wso2NovaIdReset';
import type {
	Wso2OrderMonitoringReq,
	Wso2OrderMonitoringRes
} from './schemas/api/wso2OrderMonitoring.schema';
import type {
	Wso2OrderReviewReq,
	Wso2OrderReviewRes
} from './schemas/api/wso2OrderReview.schema';
import type {
	Wso2MvReserveIptvRes,
	Wso2MwReserveIptvReq
} from './schemas/api/wso2ReserveIPTV';
import type {
	Wso2ReserveStockReq,
	Wso2ReserveStockRes
} from './schemas/api/wso2ReserveStock';
import type {
	Wso2AppointmentBookingReq,
	Wso2AppointmentBookingRes,
	Wso2MWSwiftAppointmentCreateReq,
	Wso2MWSwiftAppointmentCreateRes
} from './schemas/api/wso2SwiftAppointment';

class Wso2OrderIntegration {
	integrationId: string;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
	}

	async getWso2CheckStock(partnerIds: string[]): Promise<Wso2CheckStockRes> {
		const url: string = envConfig().WSO2_CHECK_STOCK;
		const token: string = await getApimToken();
		const stockToken = await getMmagToken(token);

		const bodyRequest: Wso2CheckStockReq = {
			token: stockToken,
			request: {
				Item_SKUs: partnerIds
			}
		};

		const res = await fetchApi(this.integrationId, url, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': Math.random().toString(36).slice(2),
				'x-boid': Math.random().toString(36).slice(2),
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		});

		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!resBody?.response) {
			throw new UE_ERROR(
				'Missing response from WSO2 Check Stock',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: resBody }
			);
		}

		const { status, message } = resBody.response;

		if (!res.ok || status !== 'Successful') {
			throw new UE_ERROR(
				message || 'WSO2 Check Stock Failed',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: resBody }
			);
		}

		return resBody as Wso2CheckStockRes;
	}

	/**
	 * @param bodyRequest
	 * @returns Wso2OrderReviewRes
	 */
	async getWso2OrderReview(
		bodyRequest: Wso2OrderReviewReq
	): Promise<Wso2OrderReviewRes> {
		const url: string = envConfig().WSO2_ORDER_REVIEW;
		const token: string = await getApimToken();

		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': Math.random().toString(36).slice(2),
				'x-boid': Math.random().toString(36).slice(2),
				'x-calling-application': 'UE',
				'Cache-Control': 'no-cache'
			},
			body: JSON.stringify(bodyRequest)
		};

		const res: Response = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'WSO2 Order Review Failed',
				StatusCodeEnum.WSO2_ERROR,
				{
					integrationId: this.integrationId,
					response: null
				}
			);
		}

		return resBody as Wso2OrderReviewRes;
	}

	async getWso2AppointmentBooking(
		bodyRequest: Wso2AppointmentBookingReq
	): Promise<Wso2AppointmentBookingRes> {
		const url: string = envConfig().WSO2_BOOK_APPOINTMENT;
		const token: string = await getApimToken();

		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': randomString(16),
				'x-boid': randomString(16),
				'x-calling-application': 'UE',
				'Cache-Control': 'no-cache'
			},
			body: JSON.stringify(bodyRequest)
		};

		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON from WSO2 Appointment Booking',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok || !resBody?.ReplyHeader) {
			throw new UE_ERROR(
				'Invalid response from WSO2 Appointment Booking',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: resBody }
			);
		}

		return resBody as Wso2AppointmentBookingRes;
	}

	/**
	 * @param bodyRequest
	 * @returns Wso2MWSwiftAppointmentCreateRes
	 */
	async getWso2SwiftAppointment(
		bodyRequest: Wso2MWSwiftAppointmentCreateReq
	): Promise<Wso2MWSwiftAppointmentCreateRes> {
		const url: string = envConfig().WSO2_SWIFT_APPOINTMENT_CREATE;
		const token: string = await getApimToken();

		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': Math.random().toString(36).slice(2),
				'x-boid': Math.random().toString(36).slice(2),
				'x-calling-application': 'UE',
				'Cache-Control': 'no-cache'
			},
			body: JSON.stringify(bodyRequest)
		};

		const res: Response = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		// Error handling if the request fails
		if (!res.ok) {
			throw new UE_ERROR(
				'WSO2 SWIFT Appointment Failed',
				StatusCodeEnum.WSO2_ERROR,
				{
					integrationId: this.integrationId,
					response: null
				}
			);
		}

		return resBody as Wso2MWSwiftAppointmentCreateRes;
	}

	/**
	 * @param bodyRequest
	 * @returns Wso2MWAppointmentIDRes
	 */
	async getWso2NovaIdReset(
		bodyRequest: Wso2MWAppointmentIDReq
	): Promise<Wso2MWAppointmentIDRes> {
		const url: string = envConfig().WSO2_NOVA_ID_RESET;
		const token: string = await getApimToken();

		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': Math.random().toString(36).slice(2),
				'x-boid': Math.random().toString(36).slice(2),
				'x-calling-application': 'UE',
				'Cache-Control': 'no-cache'
			},
			body: JSON.stringify(bodyRequest)
		};

		const res: Response = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		// Error handling if the request fails
		if (!res.ok) {
			throw new UE_ERROR(
				'WSO2 NOVA Id Reset Failed',
				StatusCodeEnum.WSO2_ERROR,
				{
					integrationId: this.integrationId,
					response: null
				}
			);
		}

		return resBody as Wso2MWAppointmentIDRes;
	}

	async getWso2ReserveStock(
		bodyRequest: Wso2ReserveStockReq
	): Promise<Wso2ReserveStockRes> {
		const url: string = envConfig().WSO2_RESERVE_ORDER_URL;
		const token: string = await getApimToken();

		const res = await fetchApi(this.integrationId, url, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Custom: `Bearer ${token}`,
				'x-bpid': Math.random().toString(36).slice(2),
				'x-boid': Math.random().toString(36).slice(2),
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		});

		const resBody = await res.json();

		if (!res.ok) {
			throw new UE_ERROR(
				resBody?.response?.message || 'Failed to reserve device from MMAG',
				StatusCodeEnum.WSO2_ERROR,
				{
					integrationId: this.integrationId,
					response: resBody
				}
			);
		}

		if (resBody?.response?.status === 'Unsuccessful') {
			throw new UE_ERROR(
				resBody?.response?.message,
				StatusCodeEnum.WSO2_ERROR,
				{
					integrationId: this.integrationId,
					response: resBody?.response?.message
				}
			);
		}

		return resBody;
	}

	/**
	 * @param bodyRequest
	 * @returns Wso2MvReserveIptvRes
	 */
	async getReserveIPTV(
		bodyRequest: Wso2MwReserveIptvReq
	): Promise<Wso2MvReserveIptvRes> {
		const url: string = envConfig().WSO2_RESERVE_IPTV_ID;
		const token: string = await getApimToken();

		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': Math.random().toString(36).slice(2),
				'x-boid': Math.random().toString(36).slice(2),
				'x-calling-application': 'UE',
				'Cache-Control': 'no-cache'
			},
			body: JSON.stringify(bodyRequest)
		};

		const res: Response = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json();

		if (!res.ok) {
			throw new UE_ERROR('Reserve IPTV Failed', StatusCodeEnum.WSO2_ERROR, {
				integrationId: this.integrationId,
				response: null
			});
		}

		return resBody as Wso2MvReserveIptvRes;
	}

	/**
	 * @param bodyRequest
	 * @returns Wso2MWCreateOrderSubmitRes
	 */
	async createAddonOrder(
		bodyRequest: Wso2MwCreateOrderReq
	): Promise<Wso2MwCreateOrderSubmitRes> {
		const url: string = envConfig().WSO2_ORDER_SUBMIT;
		const token: string = await getApimToken();

		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': Math.random().toString(36).slice(2),
				'x-boid': Math.random().toString(36).slice(2),
				'x-calling-application': 'UE',
				'Cache-Control': 'no-cache'
			},
			body: JSON.stringify(bodyRequest)
		};

		const res: Response = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json();

		if (!res.ok) {
			throw new UE_ERROR(
				'Create Order Addon Failed',
				StatusCodeEnum.WSO2_ERROR,
				{
					integrationId: this.integrationId,
					response: null
				}
			);
		}

		return resBody as Wso2MwCreateOrderSubmitRes;
	}

	async getWso2OrderMonitoringStatus(
		bodyRequest: Wso2OrderMonitoringReq
	): Promise<Wso2OrderMonitoringRes> {
		const url: string = envConfig().WSO2_ORDER_MONITORING;
		const token: string = await getApimToken();

		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'x-bpid': Math.random().toString(36).slice(2),
				'x-boid': Math.random().toString(36).slice(2),
				'x-calling-application': 'UE',
				Authorization: `Bearer ${token}`
			},
			body: JSON.stringify(bodyRequest)
		};

		const res: Response = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'WSO2 Order Monitoring Failed',
				StatusCodeEnum.WSO2_ERROR,
				{
					integrationId: this.integrationId,
					response: resBody
				}
			);
		}

		return resBody as Wso2OrderMonitoringRes;
	}
}

export default Wso2OrderIntegration;
