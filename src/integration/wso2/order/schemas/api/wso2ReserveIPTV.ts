import { type Static, t } from 'elysia';

const loginDetailsSchema = t.Object({
	orderLineItemId: t.String(),
	loginName: t.String(),
	offer: t.String()
});

export type LoginDetails = Static<typeof loginDetailsSchema>;

const performLoginReqSchema = t.Object({
	customerId: t.String(),
	LoginDetails: t.Array(loginDetailsSchema)
});

export type PerformLoginReq = Static<typeof performLoginReqSchema>;

const performLoginRequestSchema = t.Object({
	PerformLoginReq: t.Array(performLoginReqSchema)
});

export type PerformLoginRequest = Static<typeof performLoginRequestSchema>;

const mwReserveIptvRequestSchema = t.Object({
	PerformLoginRequest: performLoginRequestSchema
});

export type Wso2MwReserveIptvReq = Static<typeof mwReserveIptvRequestSchema>;

const reservationResultSchema = t.Object({
	orderLineItemId: t.String(),
	loginName: t.String(),
	password: t.String(),
	statusCode: t.String()
});

export type ReservationResult = Static<typeof reservationResultSchema>;

const headerSchema = t.Object({
	servicelabel: t.String(),
	transactionid: t.String()
});

export type Header = Static<typeof headerSchema>;

const loginResponseSchema = t.Object({
	header: headerSchema,
	reservationResult: t.Array(reservationResultSchema)
});

export type LoginResponse = Static<typeof loginResponseSchema>;

const performLoginResponseSchema = t.Object({
	LoginResponse: t.Array(loginResponseSchema)
});

export type PerformLoginResponse = Static<typeof performLoginResponseSchema>;

const mvReserveIptvResponseSchema = t.Object({
	PerformLoginResponse: performLoginResponseSchema
});

export type Wso2MvReserveIptvRes = Static<typeof mvReserveIptvResponseSchema>;
