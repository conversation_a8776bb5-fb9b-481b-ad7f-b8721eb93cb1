import { type Static, t } from 'elysia';

const wso2CheckStockReqSchema = t.Object({
	token: t.String(),
	request: t.Object({
		Page_No: t.Optional(t.Number()),
		Page_Size: t.Optional(t.Number()),
		Item_SKUs: t.Array(t.String())
	})
});

export type Wso2CheckStockReq = Static<typeof wso2CheckStockReqSchema>;

export const wso2CheckStockInventorySchema = t.Object({
	Branch_Code: t.String(),
	Branch_Name: t.String(),
	Qty_on_Hand: t.Number(),
	Qty_Reserved: t.Number(),
	Qty_Available: t.Number()
});

export type Wso2CheckStockInventory = Static<
	typeof wso2CheckStockInventorySchema
>;

export const wso2CheckStockItemResSchema = t.Object({
	Item_SKU: t.String(),
	Item_Name: t.String(),
	Item_Brand: t.String(),
	Item_Category: t.String(),
	status: t.String(),
	code: t.Number(),
	message: t.String(),
	Inventory: t.Optional(t.Array(wso2CheckStockInventorySchema))
});

export type Wso2CheckStockItemRes = Static<typeof wso2CheckStockItemResSchema>;

export const wso2CheckStockResSchema = t.Object({
	response: t.Object({
		status: t.String(),
		code: t.Number(),
		message: t.String(),
		Page_No: t.Number(),
		Page_Size: t.Number(),
		Pages_Count: t.Number(),
		Items_Count: t.Number(),
		Items: t.Array(wso2CheckStockItemResSchema)
	})
});

export type Wso2CheckStockRes = Static<typeof wso2CheckStockResSchema>;
