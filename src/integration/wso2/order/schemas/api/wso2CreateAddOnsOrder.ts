import { type Static, t } from 'elysia';

// ORDER SCHEMA START HERE

//
// ─── ORDER ITEM XA ──────────────────────────────────────────────────────────────
//

export const orderItemXaSchema = t.Object({
	ActionCode: t.Optional(t.String()),
	Name: t.Optional(t.String()),
	TextValue: t.<PERSON>tional(t.String()),
	ServiceID: t.Optional(t.String()),
	ReservationStatus: t.Optional(t.String())
});

export type OrderItemXaSchema = Static<typeof orderItemXaSchema>;

//
// ─── LIST OF ORDER ITEM XA ──────────────────────────────────────────────────────
//

export const listOfOrderItemXaSchema = t.Object({
	OrderItemXa: t.Array(orderItemXaSchema)
});

export type ListOfOrderItemXaSchema = Static<typeof listOfOrderItemXaSchema>;

//
// ─── ACTIVITY ───────────────────────────────────────────────────────────────────
//

export const activitySchema = t.Object({
	ActivityId: t.String()
});

export type ActivitySchema = Static<typeof activitySchema>;

//
// ─── LIST OF ACTIVITY ───────────────────────────────────────────────────────────
//

export const listOfActivitySchema = t.Object({
	Activity: t.Array(activitySchema)
});

export type ListOfActivitySchema = Static<typeof listOfActivitySchema>;

//
// ─── ORDER LINE ITEMS ───────────────────────────────────────────────────────────
//

export const orderLineItemsSchema = t.Recursive(self =>
	t.Object({
		ActionCode: t.String(),
		AssetIntegrationId: t.Optional(t.String()),
		BillingAccountId: t.String(),
		PartNumber: t.Optional(t.String()),
		ProdPromInstanceId: t.Optional(t.String()),
		Product: t.String(),
		ProductId: t.Optional(t.String()),
		ProdPromName: t.Optional(t.String()),
		ProductType: t.Optional(t.String()),
		ServiceAccountId: t.Optional(t.String()),
		ServicePointId: t.Optional(t.String()),
		ReservationStatus: t.Optional(t.String()),
		Qty: t.Optional(t.String()),
		TMServiceTaxFlag: t.String(),
		CfgStateCode: t.String(),
		IntegrationId: t.Optional(t.String()),
		ServiceId: t.Optional(t.String()),
		SecondaryServiceId: t.Optional(t.String()),
		BillingProfileId: t.Optional(t.String()),
		ListOfActivity: t.Optional(listOfActivitySchema),
		OrderLineItems: t.Optional(t.Array(self)),
		ListOfOrderItemXa: t.Optional(listOfOrderItemXaSchema)
	})
);

export type OrderLineItemsSchema = Static<typeof orderLineItemsSchema>;

//
// ─── LIST OF ORDER LINE ITEMS ───────────────────────────────────────────────────
//

export const listOfOrderLineItemsSchema = t.Object({
	OrderLineItems: t.Array(orderLineItemsSchema)
});

//
// ─── FINAL ORDER SCHEMA ─────────────────────────────────────────────────────────
//

export const orderSchema = t.Object({
	AccountId: t.String(),
	BillingAccountId: t.String(),
	BillingProfileId: t.String(),
	ContactId: t.String(),
	OrderType: t.String(),
	PriceList: t.String(),
	TMOrderSource: t.String(),
	TMPromotionName: t.String(),
	TMDiCEOrderNumber: t.String(),
	TMServingExchange: t.String(),
	Description: t.String(),
	OrderNumber: t.String(),
	ServicePointId: t.String(),
	DeliveryAddressId: t.String(),
	RecipientName: t.String(),
	RecipientContactNum: t.String(),
	TMModifyUpgradeDowngradeIndicator: t.Optional(t.String()),
	ListOfOrderLineItems: listOfOrderLineItemsSchema
});

export type OrderSchema = Static<typeof orderSchema>;

// ORDER SCHEMA END HERE

export const wso2MwCreateOrderRequestSchema = t.Object({
	OrderSubmitPortalRetailRequest: t.Object({
		OrderSubmitPortalRetailReq: t.Object({
			Order: orderSchema
		})
	})
});

export type Wso2MwCreateOrderReq = Static<
	typeof wso2MwCreateOrderRequestSchema
>;

export const statusSchema = t.Object({
	Type: t.String(),
	Code: t.String(),
	Message: t.String()
});

export const portalMessageSchema = t.Object({
	Status: t.String(),
	StatusMessageFromSiebel: t.String(),
	PortalOrderId: t.Optional(t.String()),
	IptvId: t.Optional(t.String())
});

export const wso2MwCreateOrderSubmitResponseSchema = t.Object({
	Status: statusSchema,
	Response: t.Object({
		PortalMessage: portalMessageSchema
	})
});

export type Wso2MwCreateOrderSubmitRes = Static<
	typeof wso2MwCreateOrderSubmitResponseSchema
>;
