import { type Static, t } from 'elysia';

const wso2OrderReviewReqSchema = t.Object({
	OrderReviewRequest: t.Object({
		AccountId: t.String({ examples: ['Account Id'] }),
		OrderType: t.String({ examples: ['Order Type'] }),
		SegmentGroup: t.String({ examples: ['Segment Group'] }),
		TransactionId: t.String({ examples: ['Transaction Id'] }),
		ReservationId: t.String({ examples: ['Reservation Id'] }),
		OrderId: t.String({ examples: ['Order Id'] }),
		ExchangeName: t.String({ examples: ['Exchange Name'] }),
		SystemName: t.String({ examples: ['System Name'] }),
		QueryAppointmentSlotsFlag: t.String({
			examples: ['Query Appointment Slots Flag']
		}),
		CustomerAccount: t.Object({
			AccountId: t.String({ examples: ['Account Id'] }),
			ContactId: t.String({ examples: ['Contact Id'] })
		}),
		QueryAppointmentSlots: t.Object({
			ServicePointID: t.String({ examples: ['Service Point Id'] }),
			TechnologyType: t.String({ examples: ['Technology Type'] }),
			DPLocation: t.String({ examples: ['Dp Location'] }),
			PremiseType: t.String({ examples: ['Premise Type'] }),
			EarliestStartDate: t.String({ examples: ['Earliest Start Date'] }),
			LatestStartDate: t.String({ examples: ['Latest Start Date'] }),
			AppointmentProducts: t.Object({
				AppointmentProduct: t.Array(
					t.Object({
						Name: t.String({ examples: ['Name'] }),
						PartNumber: t.String({ examples: ['Part Number'] }),
						Action: t.String({ examples: ['Action'] })
					})
				)
			})
		})
	})
});

export type Wso2OrderReviewReq = Static<typeof wso2OrderReviewReqSchema>;

const wso2OrderReviewResSchema = t.Object({
	Status: t.Object({
		Type: t.String({ examples: ['Type'] }),
		Code: t.String({ examples: ['Code'] }),
		Message: t.String({ examples: ['Message'] })
	}),
	Response: t.Object({
		OrderReviewResponse: t.Object({
			OrderType: t.String({ examples: ['Order Type'] }),
			SegmentGroup: t.String({ examples: ['Segment Group'] }),
			TransactionId: t.String({ examples: ['MW6-1000'] }),
			OrderId: t.String({ examples: ['Order Id'] }),
			GetAppointmentSlots: t.Object({
				ReturnCode: t.String({ examples: ['Return Code'] }),
				ReturnMessage: t.String({ examples: ['Return Message'] }),
				RNORegion: t.String({ examples: ['RNO Region'] }),
				BookingNumber: t.String({ examples: ['Booking Number'] }),
				Priority: t.String({ examples: ['Priority'] }),
				AppointmentSlot: t.Array(
					t.Object({
						AppointmentId: t.String({ examples: ['Appointment Id'] }),
						SlotStart: t.String({ examples: ['Slot Start'] }),
						SlotEnd: t.String({ examples: ['Slot End'] })
					})
				)
			})
		})
	})
});

export type Wso2OrderReviewRes = Static<typeof wso2OrderReviewResSchema>;
