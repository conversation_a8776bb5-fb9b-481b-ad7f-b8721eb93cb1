import { type Static, t } from 'elysia';

//
// ─── BOOKING REQUEST ─────────────────────────────────────────────────────────────
//

export const wso2AppointmentBookingRequestSchema = t.Object({
	appointmentBookingPortal: t.Object({
		RequestHeader: t.Object({
			ApptBookingStruct: t.Object({
				ExternalId: t.String(),
				OrderNumber: t.String(),
				AppointmentId: t.String(),
				AppointmentPreference: t.String(),
				SlotStart: t.String(),
				SlotEnd: t.String(),
				AddressIndicator: t.String()
			})
		})
	})
});

export type Wso2AppointmentBookingReq = Static<
	typeof wso2AppointmentBookingRequestSchema
>;

//
// ─── BOOKING RESPONSE ────────────────────────────────────────────────────────────
//

export const activityStructSchema = t.Object({
	ActivityId: t.String(),
	ExternalActivityId: t.Nullable(t.String()),
	ActivityType: t.String(),
	DateTimeStart: t.String(),
	DateTimeEnd: t.String(),
	Duration: t.String()
});

export type ActivityStruct = Static<typeof activityStructSchema>;

export const wso2AppointmentBookingResponseSchema = t.Object({
	ReplyHeader: t.Object({
		MessageID: t.String(),
		EventName: t.String(),
		DateTimeSent: t.String(),
		OrderNumber: t.String(),
		ExternalId: t.String(),
		OrderStatus: t.String(),
		ActivityStruct: t.Array(activityStructSchema)
	})
});

export type Wso2AppointmentBookingRes = Static<
	typeof wso2AppointmentBookingResponseSchema
>;

//
// ─── SWIFT APPOINTMENT REQUEST ──────────────────────────────────────────────────
//

export const tmActionIntegrationSchema = t.Object({
	ActivityId: t.String(),
	AssetId: t.String(),
	OrderId: t.Optional(t.Nullable(t.String())),
	DateTimeStart: t.String(),
	DateTimeEnd: t.String(),
	ServiceRegion: t.String(),
	Status: t.String(),
	SubType: t.String(),
	ExternalActivityId: t.String(),
	TypeActivityType: t.String(),
	Duration: t.String(),
	SwiftFlag: t.String(),
	TMAutoSlottedFlag: t.Optional(t.Nullable(t.String()))
});

export const listOfTmEaiCreateSlotActivityReqSchema = t.Object({
	TmActionIntegration: t.Array(tmActionIntegrationSchema)
});

export const swiftAppointmentCreateRequestSchema = t.Object({
	ListOfTmEaiCreateSlotActivityReq: listOfTmEaiCreateSlotActivityReqSchema
});

export const mwSwiftAppointmentCreateRequestSchema = t.Object({
	SWIFTAppointmentCreateRequest: swiftAppointmentCreateRequestSchema
});

export type SWIFTAppointmentCreateRequest = Static<
	typeof swiftAppointmentCreateRequestSchema
>;

export type ListOfTmEaiCreateSlotActivityReq = Static<
	typeof listOfTmEaiCreateSlotActivityReqSchema
>;

export type TmActionIntegration = Static<typeof tmActionIntegrationSchema>;

export type Wso2MWSwiftAppointmentCreateReq = Static<
	typeof mwSwiftAppointmentCreateRequestSchema
>;

//
// ─── SWIFT APPOINTMENT RESPONSE ─────────────────────────────────────────────────
//

export const listOfTmEaiCreateSlotActivityResSchema = t.Object({
	TmActionIntegration: t.Array(tmActionIntegrationSchema)
});

export const swiftAppointmentCreateResponseSchema = t.Object({
	ListOfTmEaiCreateSlotActivityRes: listOfTmEaiCreateSlotActivityResSchema
});

export const mwSwiftAppointmentCreateResponseSchema = t.Object({
	SWIFTAppointmentCreateResponse: swiftAppointmentCreateResponseSchema
});

export type SWIFTAppointmentCreateResponse = Static<
	typeof swiftAppointmentCreateResponseSchema
>;

export type ListOfTmEaiCreateSlotActivityRes = Static<
	typeof listOfTmEaiCreateSlotActivityResSchema
>;

export type Wso2MWSwiftAppointmentCreateRes = Static<
	typeof mwSwiftAppointmentCreateResponseSchema
>;

//
// ─── NOVA ID RESET ──────────────────────────────────────────────────────────────
//

export const wso2AppointmentIDRequestSchema = t.Object({
	ActivityId: t.String(),
	NewExternalActivityId: t.String(),
	CurrentExternalActivityId: t.String()
});

export const wso2AppointmentIDResponseSchema = t.Object({
	ReplyHeader: t.Object({
		ActivityId: t.String()
	})
});

export type Wso2MWAppointmentIDReq = Static<
	typeof wso2AppointmentIDRequestSchema
>;

export type Wso2MWAppointmentIDRes = Static<
	typeof wso2AppointmentIDResponseSchema
>;
