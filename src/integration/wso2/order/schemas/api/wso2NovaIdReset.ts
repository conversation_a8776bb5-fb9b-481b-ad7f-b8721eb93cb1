import { type Static, t } from 'elysia';

// Define the ApptIDStruct schema
export const apptIDStructSchema = t.Object({
	ActivityId: t.String(),
	CurrentExternalActivityId: t.String(),
	NewExternalActivityId: t.String()
});

export type ApptIDStruct = Static<typeof apptIDStructSchema>;

// Define the RequestHeader schema
export const requestHeaderSchema = t.Object({
	ApptIDStruct: t.Array(apptIDStructSchema)
});

export type RequestHeader = Static<typeof requestHeaderSchema>;

// Define the NovaActivityIDReset schema
export const novaActivityIDResetSchema = t.Object({
	RequestHeader: requestHeaderSchema
});

export type NovaActivityIDReset = Static<typeof novaActivityIDResetSchema>;

// Define the MWAppointmentIDRequest schema
export const mwAppointmentIDRequestSchema = t.Object({
	novaActivityIDReset: novaActivityIDResetSchema
});

export type Wso2MWAppointmentIDReq = Static<
	typeof mwAppointmentIDRequestSchema
>;

// Define the ReplyHeader schema
export const replyHeaderSchema = t.Object({
	MessageID: t.String(),
	OrderNumber: t.Nullable(t.String()),
	ExternalId: t.Nullable(t.String()),
	errCd: t.String(),
	errMsg: t.String(),
	activityId: t.Optional(t.String())
});

export type ReplyHeader = Static<typeof replyHeaderSchema>;

export const mwAppointmentIDResponseSchema = t.Object({
	ReplyHeader: replyHeaderSchema
});

export type Wso2MWAppointmentIDRes = Static<
	typeof mwAppointmentIDResponseSchema
>;
