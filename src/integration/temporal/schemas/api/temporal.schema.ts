import { type Static, t } from 'elysia';

const temporalUserTaskSignalReqSchema = t.Object({
	WorkflowId: t.String({ examples: ['UNFH-123456789-AddressMatching'] })
});

export type TemporalUserTaskSignalReq = Static<
	typeof temporalUserTaskSignalReqSchema
>;

export const temporalUserTaskSignalResSchema = t.Object({
	status: t.Boolean(),
	body: t.Object({
		Success: t.Boolean({ examples: [true] }),
		Code: t.String({ examples: ['200'] }),
		Response: t.Optional(
			t.String({ examples: ['Signal sent to workflow with ID:'] })
		),
		Message: t.Optional(t.String()),
		Stack: t.Optional(t.String())
	})
});

export type TemporalUserTaskSignalRes = Static<
	typeof temporalUserTaskSignalResSchema
>;

const temporalTriggerWorkflowReqSchema = t.Object({
	OrderId: t.String({ examples: ['UNFH-123456789'] }),
	IsOrderable: t.<PERSON>({ examples: [true] }),
	Namespace: t.String({ examples: ['irenew'] })
});

export type TemporalTriggerWorkflowReq = Static<
	typeof temporalTriggerWorkflowReqSchema
>;

export const temporalTriggerWorkflowResSchema = t.Object({
	status: t.Boolean(),
	body: t.Object({
		Success: t.Boolean({ examples: [true] }),
		Code: t.String({ examples: ['200'] }),
		Response: t.Optional(
			t.String({ examples: ['Signal sent to workflow with ID:'] })
		),
		Message: t.Optional(t.String()),
		Stack: t.Optional(t.String())
	})
});

export type TemporalTriggerWorkflowRes = Static<
	typeof temporalTriggerWorkflowResSchema
>;
