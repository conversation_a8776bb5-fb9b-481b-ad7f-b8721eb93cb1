import { envConfig } from '../../config/env.config';
import { StatusCodeEnum } from '../../enum/statusCode.enum';
import { UE_ERROR } from '../../middleware/error';
import { fetchApi } from '../helper/fetchApi.helper';
import type {
	TemporalTriggerWorkflowReq,
	TemporalTriggerWorkflowRes,
	TemporalUserTaskSignalReq,
	TemporalUserTaskSignalRes
} from './schemas/api/temporal.schema';

class TemporalIntegration {
	integrationId: string;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
	}

	async triggerUserTaskSignal(
		bodyRequest: TemporalUserTaskSignalReq
	): Promise<TemporalUserTaskSignalRes> {
		const url: string = envConfig().TEMPORAL_TRIGGER_USER_TASK_SIGNAL_URL;

		const payload = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(bodyRequest)
		};

		const res = await fetchApi(this.integrationId, url, payload);
		const resBody = await res.json();

		if (!res.ok) {
			throw new UE_ERROR(
				'Temporal trigger user task signal throw error',
				StatusCodeEnum.TEMPORAL_ERROR,
				{ integrationId: this.integrationId, response: resBody }
			);
		}

		return resBody;
	}

	async triggerWorkflow(
		bodyRequest: TemporalTriggerWorkflowReq
	): Promise<TemporalTriggerWorkflowRes> {
		const url: string = envConfig().TEMPORAL_TRIGGER_WORKFLOW_URL.replace(
			'/api/',
			`/api/${bodyRequest.Namespace}/`
		);

		const payload = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(bodyRequest)
		};

		const res = await fetchApi(this.integrationId, url, payload);
		const resBody = await res.json();

		if (!res.ok) {
			throw new UE_ERROR(
				'Temporal trigger workflow throw error',
				StatusCodeEnum.TEMPORAL_ERROR,
				{ integrationId: this.integrationId, response: resBody }
			);
		}

		return resBody;
	}
}

export default TemporalIntegration;
