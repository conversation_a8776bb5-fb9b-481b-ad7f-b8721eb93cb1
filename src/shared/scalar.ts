import jwt from '@elysiajs/jwt';
import Elysia, { t } from 'elysia';
import { envConfig } from '../config/env.config';

const serverPrefixPath = envConfig().SERVER_PREFIX_PATH;

export const tagsArray = [
	{ name: 'Address' },
	{ name: 'Batch' },
	{ name: 'Catalogue' },
	{ name: 'Eligibility' },
	{ name: 'Notification' },
	{ name: 'Order' },
	{ name: 'Payment' },
	{ name: 'Record' },
	{ name: 'Reward' },
	{ name: 'Security' },
	{ name: 'Settings' },
	{ name: 'User' },
	{ name: 'Util' },
	{ name: 'UDID: Identity' },
	{ name: 'UDID: Profile' },
	{ name: 'UDID: Session' },
	{ name: 'UDID: TAC' }
];

const htmlPage = `
      <html>
        <head>
			<meta charset="UTF-8" />
			<meta
				name="viewport"
				content="width=device-width, initial-scale=1.0"
			/>
			<title>Scalar API Reference</title>
			<style>
				html {
					scroll-behavior: smooth;
				}

				* {
				    margin: 0;
					padding: 0;
					box-sizing: border-box;
				}

				body {
					font-family: -apple-system, BlinkMacSystemFont, sans-serif;
					background: #121212; /* fallback for old browsers */
					overflow-x: hidden;
					height: 100%;
					/* code to make all text unselectable */
					-webkit-user-select: none;
					-khtml-user-select: none;
					-moz-user-select: none;
					-ms-user-select: none;
					-o-user-select: none;
					user-select: none;
				}

				/* Disables selector ring */
				body:not(.user-is-tabbing) button:focus,
				body:not(.user-is-tabbing) input:focus,
				body:not(.user-is-tabbing) select:focus,
				body:not(.user-is-tabbing) textarea:focus {
					outline: none;
				}

				/* ########################################################## */

				h1 {
					color: white;
					font-size: 35px;
					font-weight: 800;
				}

				.flex-container {
					width: 100vw;
					margin-top: 100px;
					display: flex;
					justify-content: center;
					align-items: center;
				}

				.content-container {
					width: 500px;
					height: 350px;
				}

				.form-container {
					display: flex;
					justify-content: center;
					align-items: center;
					width: 500px;
					height: 350px;
					margin-top: 5px;
					padding-top: 20px;
					border-radius: 12px;
					display: flex;
					justify-content: center;
					flex-direction: column;
					background: #1f1f1f;
					box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.199);
				}

				.subtitle {
					font-size: 11px;
					color: rgba(177, 177, 177, 0.3);
				}

				input {
					border: none;
					border-bottom: solid rgb(143, 143, 143) 1px;
					margin-bottom: 30px;
					background: none;
					color: rgba(255, 255, 255, 0.555);
					height: 35px;
					width: 300px;
				}

				.submit-btn {
					cursor: pointer;
					border: none;
					border-radius: 8px;
					box-shadow: 2px 2px 7px #38d39f70;
					background: #38d39f;
					color: rgba(255, 255, 255, 0.8);
					width: 80px;
					padding: 10px;
					transition: all 1s;
				}

				.submit-btn:hover {
					color: rgb(255, 255, 255);
					box-shadow: none;
				}
			</style>
		</head>
		<body>
			<div class="flex-container">
				<div class="content-container">
					<div class="form-container">
						<form action="${serverPrefixPath}/login" method="post">
							<h1>Login</h1>
							<br />
							<br />
							<span class="subtitle">USERNAME:</span>
							<br />
							<input type="text" id="username" name="username" required>
							<br />
							<span class="subtitle">PASSWORD:</span>
							<br />
							<input
								type="password"
								id="password"
								name="password"
								required>
							<br /><br />
							<button type="submit" class="submit-btn"> Login </button>
						</form>
					</div>
				</div>
			</div>
        </body>
      </html>
    `;

export const scalarRoutes = new Elysia()
	.use(
		jwt({
			name: 'jwt',
			secret: process.env.JWT_SECRET || 'SecretKey'
		})
	)
	// Login Setup for Scalar documentation
	.derive(({ jwt, cookie: { auth } }) => ({
		signUser: async (username: string) => {
			const token = await jwt.sign({ username });
			auth.set({
				value: token,
				httpOnly: true,
				path: '/',
				maxAge: 7 * 86400,
				sameSite: 'lax'
			});
		},
		getUser: async () => {
			const token = auth.value;
			if (!token) {
				return null;
			}
			if (typeof token === 'object' && 'value' in token) {
				const verifiedToken = await jwt.verify(token);
				return verifiedToken;
			}
			if (typeof token !== 'string') {
				return null;
			}
			const verifiedToken = await jwt.verify(token);
			return verifiedToken;
		}
	}))
	.onBeforeHandle(async ctx => {
		if (
			ctx.path.startsWith('/swagger') ||
			ctx.path.startsWith('/swagger/json') ||
			ctx.path.startsWith('/reference')
		) {
			return ctx.getUser().then(user => {
				if (!user) {
					return ctx.redirect(`${serverPrefixPath}/login`, 302);
				}
			});
		}
	})
	.get(
		'/login',
		({ set }) => {
			set.status = 302;
			set.headers['Content-Type'] = 'text/html; charset=utf8';
			return htmlPage;
		},
		{ detail: { hide: true } }
	)
	.post(
		'/login',
		async ctx => {
			if (
				ctx.body.username === process.env.SCALAR_USERNAME &&
				ctx.body.password === process.env.SCALAR_PASSWORD
			) {
				await ctx.signUser(ctx.body.username);
				return ctx.redirect(`${serverPrefixPath}/reference`, 302);
			}
			return new Response('Invalid credentials', { status: 401 });
		},
		{
			body: t.Object({
				username: t.String(),
				password: t.String()
			}),
			detail: { hide: true }
		}
	)
	.get(
		'/reference',
		ctx => {
			const forwardedHost = ctx.request.headers.get('x-forwarded-host');
			const host =
				forwardedHost || ctx.request.headers.get('host') || 'localhost:3000';

			const forwardedProto = ctx.request.headers.get('x-forwarded-proto');
			const protocol = forwardedProto || 'http';

			const fullUrl = `${protocol}://${host}${serverPrefixPath}/swagger/json`;
			ctx.set.headers['Content-Type'] = 'text/html; charset=utf8';

			return `<!doctype html>
<html>
  <head>
    <title>Universal Engine API Reference</title>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1" />
  </head>
  <body>
    <script
      id="api-reference"
      data-url="${fullUrl}"></script>
    <script src="https://cdn.jsdelivr.net/npm/@scalar/api-reference"></script>
  </body>
</html>`;
		},
		{ detail: { hide: true } }
	);
