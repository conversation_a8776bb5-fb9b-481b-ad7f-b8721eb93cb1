import { type Static, t } from 'elysia';
import { StatusCodeEnum } from '../../../enum/statusCode.enum';

export const errorBaseResponseSchema = t.Object({
	Success: t.Bo<PERSON>an({ example: false }),
	Code: t.Enum(StatusCodeEnum),
	RequestId: t.String(),
	IntegrationId: t.Optional(t.Nullable(t.String())),
	Name: t.String(),
	Message: t.String(),
	Cause: t.Unknown()
});

export type ErrorBaseResponse = Static<typeof errorBaseResponseSchema>;

export const baseResponseSchema = t.Object({
	Success: t.Boolean(),
	Code: t.Enum(StatusCodeEnum, {
		examples: [StatusCodeEnum.OK]
	}),
	IntegrationId: t.Optional(t.Nullable(t.String())),
	Message: t.Optional(t.String())
});

export type BaseResponse = Static<typeof baseResponseSchema>;
