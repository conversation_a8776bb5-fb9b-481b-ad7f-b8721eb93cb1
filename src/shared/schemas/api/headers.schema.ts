import { type Static, t } from 'elysia';
import { SegmentEnum, SourceEnum } from '../../../enum/header.enum';
import { LightweightFlagEnum } from '../../../enum/wso2.enum';

export const lightWeightFlagSchema = t.Object({
	lightweightflag: t.Enum(LightweightFlagEnum)
});

// ** INFO: elysia parse headers as a lowercase key only, thus the key for baseHeaderSchema is changed to lowercase
// ** Source: https://elysiajs.com/essential/validation#specs-3
export const baseHeaderSchema = t.Object({
	'x-api-key': t.String(),
	source: t.Enum(SourceEnum),
	segment: t.Enum(SegmentEnum),
	authorization: t.String()
});

export type BaseHeader = Static<typeof baseHeaderSchema>;
