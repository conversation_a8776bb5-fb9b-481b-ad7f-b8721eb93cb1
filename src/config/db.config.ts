import { type NodePgDatabase, drizzle } from 'drizzle-orm/node-postgres';
import { Pool, type PoolConfig } from 'pg';
import { pinoLog } from './pinoLog.config';

let dbClient: Pool;
let dbInstance: NodePgDatabase;
const dbConfig: PoolConfig = {
	user: `${process.env.DB_USER}`,
	host: `${process.env.DB_HOST}`,
	database: `${process.env.DB_NAME}`,
	password: `${process.env.DB_PASSWORD}`,
	port: Number.parseInt(`${process.env.DB_PORT}`),
	ssl:
		process.env.ENVIRONMENT !== 'production'
			? false
			: { rejectUnauthorized: false },
	idleTimeoutMillis: 30000, // 30 seconds
	connectionTimeoutMillis: 10000 // 10 seconds
};

export const createDbConnection = () => {
	pinoLog.info('Establishing database connection...');
	dbClient = new Pool(dbConfig);
	dbClient.on('error', err => {
		pinoLog.error('db error: ', err);
		process.exit(1);
	});
	dbInstance = drizzle(dbClient);
	pinoLog.info('Database connection established');
	return dbInstance;
};

export const getDbInstance = () => {
	if (!dbInstance) {
		pinoLog.error('Database connection not established');
		pinoLog.error('Attempting to establish connection...');
		dbClient = new Pool(dbConfig);
		dbClient.on('error', err => {
			pinoLog.error('db error: ', err);
			process.exit(1);
		});
		dbInstance = drizzle(dbClient);
	}
	return dbInstance;
};

export const closeDbConnection = async () => {
	if (dbClient) {
		await dbClient.end();
	}
};
