import { type Cluster, Redis } from 'ioredis';
import CircuitBreaker from 'opossum';
import { StatusCodeEnum } from '../enum/statusCode.enum';
import { UE_ERROR } from '../middleware/error';
import { pinoLog } from './pinoLog.config';

let redis: Cluster | Redis;

const createRedisConnection = async () => {
	if (!redis) {
		try {
			redis =
				process.env.ENVIRONMENT !== 'production'
					? new Redis({
							host: process.env.REDIS_HOST,
							port: 6379,
							retryStrategy: times => Math.min(times * 50, 2000), // Exponential backoff
							reconnectOnError: () => true,
							showFriendlyErrorStack: true
						})
					: new Redis.Cluster(
							[
								{ host: process.env.REDIS_NODE_0, port: 6379 },
								{ host: process.env.REDIS_NODE_1, port: 6379 },
								{ host: process.env.REDIS_NODE_2, port: 6379 }
							],
							{
								clusterRetryStrategy: times => Math.min(times * 100, 3000), // Exponential backoff
								enableReadyCheck: true
							}
						);

			redis.on('error', err => {
				pinoLog.error('❌ Redis connection error:', err);
			});

			redis.on('end', () => {
				pinoLog.info('⏳ Redis connection closed.');
			});

			redis.on('connect', () => {
				pinoLog.info('✅ Redis connected');
			});
		} catch (err) {
			pinoLog.error('Failed to connect to Redis cluster', err);
			throw new UE_ERROR(
				'Failed to connect to Redis',
				StatusCodeEnum.UE_INTERNAL_SERVER
			);
		}
	}
	return redis;
};

// Circuit Breaker Configuration
const breakerOptions = {
	timeout: 3000, // 3s timeout for Redis operations
	errorThresholdPercentage: 50, // Open circuit if 50% of requests fail
	resetTimeout: 10000 // Reset after 10s
};

// Wrappers for Redis operations with circuit breaker
const redisBreaker = new CircuitBreaker(
	async (command: string, ...args: (string | number)[]) => {
		if (!redis) redis = await createRedisConnection();

		// @ts-ignore
		return redis[command](...args);
	},
	breakerOptions
);

// Logging for circuit breaker state
redisBreaker.on('open', () =>
	pinoLog.warn('🔴 Circuit breaker OPEN - Redis failing!')
);
redisBreaker.on('halfOpen', () =>
	pinoLog.info('🟡 Circuit breaker HALF-OPEN - Retrying Redis...')
);
redisBreaker.on('close', () =>
	pinoLog.info('🟢 Circuit breaker CLOSED - Redis recovered.')
);
redisBreaker.on('failure', (error: Error) =>
	pinoLog.error('❌ Redis operation failed', error)
);

// Function Wrappers using Circuit Breaker
const redisSetWithTimeout = async (
	key: string,
	value: string,
	expire?: number,
	timeoutMs = 500
) => {
	try {
		return await Promise.race([
			(async () => {
				try {
					return expire
						? await redisBreaker.fire('set', key, value, 'EX', expire)
						: await redisBreaker.fire('set', key, value);
				} catch (err) {
					throw new Error(`Redis error: ${err}`);
				}
			})(),
			new Promise((_, reject) =>
				setTimeout(() => reject(new Error('Redis timeout')), timeoutMs)
			)
		]);
	} catch (err) {
		pinoLog.error(`Redis failure on set: ${err}`);
		return false; // Fail gracefully instead of hanging
	}
};

export const setCache = async (key: string, value: string, expire?: number) => {
	try {
		const result = await redisSetWithTimeout(key, value, expire, 500);
		if (!result) {
			throw new UE_ERROR(
				'Failed to set cache',
				StatusCodeEnum.UE_INTERNAL_SERVER
			);
		}
	} catch (err) {
		pinoLog.error(`Error setting cache for key: ${key}`);
		// Do NOT throw again to avoid retry issues
	}
};

export const getCacheKeys = async () => {
	try {
		return await redisBreaker.fire('keys', '*');
	} catch (err) {
		pinoLog.error('Error getting cache list', err);
		return [];
	}
};

const redisGetWithTimeout = async (key: string, timeoutMs = 500) => {
	try {
		return await Promise.race([
			redisBreaker.fire('get', key).catch(err => {
				throw new Error(`Redis error: ${err.message}`);
			}),
			new Promise((_, reject) =>
				setTimeout(() => reject(new Error('Redis timeout')), timeoutMs)
			)
		]);
	} catch (err) {
		pinoLog.error(`Redis failure: ${err}`);
		throw err; // Ensure API does NOT hang and error is properly thrown
	}
};

export const getCache = async (key: string): Promise<string | null> => {
	try {
		return await redisGetWithTimeout(key, 500);
	} catch (err) {
		pinoLog.error(`Redis timeout or failure for key: ${key}`, err);
		return null; // Return null instead of throwing to avoid breaking API calls
	}
};

export const deleteCache = async (key: string): Promise<number> => {
	try {
		const result = await redisBreaker.fire('del', key);
		if (result === 0) {
			pinoLog.warn(`Cache key "${key}" not found or already deleted.`);
		}
		return result;
	} catch (err) {
		pinoLog.error(`Redis error deleting key "${key}": ${err}`);
		return 0; // Return 0 to indicate failure
	}
};
