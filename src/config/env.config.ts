import { type Static, t } from 'elysia';
import config from '../../env.config.json';

const envSchema = t.Object({
	SERVER_PREFIX_PATH: t.String(),
	WSO2_BASIC_AUTH: t.String(),
	WSO2_APIM_HOURLY_TOKEN: t.String(),
	WSO2_MMAG_TOKEN_URL: t.String(),
	WSO2_CHECK_STOCK: t.String(),
	WSO2_BOOK_APPOINTMENT: t.String(),
	WSO2_SWIFT_APPOINTMENT_CREATE: t.String(),
	WSO2_NOVA_ID_RESET: t.String(),
	WSO2_RESERVE_IPTV_ID: t.String(),
	WSO2_ORDER_SUBMIT: t.String(),
	WSO2_RETRIEVE_CUSTOMER_ACCOUNTS: t.String(),
	WSO2_RETRIEVE_SERVICE_ACCOUNTS: t.String(),
	WSO2_ORDER_REVIEW: t.String(),
	WSO2_ORDER_TRACKING: t.String(),
	WSO2_ORDER_MONITORING: t.String(),
	WSO2_SR_RETRIEVE: t.String(),
	WSO2_UPDATE_APPOINTMENT: t.String(),
	WSO2_RETRIEVE_APPOINTMENT_SLOT: t.String(),
	WSO2_TMFORCE_ORDER_DETAILS: t.String(),
	WSO2_TMFORCE_ORDER_PROGRESS_UPDATE: t.String(),
	WSO2_TMFORCE_TT_PROGRESS_UPDATE: t.String(),
	WSO2_TMFORCE_TECHNICIAN_DETAILS: t.String(),
	WSO2_TMFORCE_POSTPONE_APPOINTMENT: t.String(),
	WSO2_TMFORCE_ACCEPTANCE_FORM: t.String(),
	WSO2_SIEBEL_UPDATE_CONFIRM_FLAG: t.String(),
	WSO2_SWIFT_UPDATE_CUSTOMER_RESPONSE: t.String(),
	WSO2_RETRIEVE_BILLING_DETAILS: t.String(),
	WSO2_RETRIEVE_CONCISE_ACCOUNT_DETAILS: t.String(),
	WSO2_PDF_ANNUAL_BILL_STATEMENT: t.String(),
	WSO2_RETRIEVE_BILL_LINK: t.String(),
	WSO2_EXP_DISCOUNT: t.String(),
	WSO2_HARD_SOFT_BUNDLE: t.String(),
	WSO2_CREATE_SR: t.String(),
	WSO2_NOVA_BILLING_PROFILE: t.String(),
	WSO2_OUTSTANDING_AMOUNT: t.String(),
	WSO2_UPDATE_BILLING_PROFILE: t.String(),
	WSO2_CONCISE_CUST_INFO: t.String(),
	WSO2_IBILL_BILLING_DETAILS: t.String(),
	WSO2_AUTOPAY_SETTING: t.String(),
	WSO2_SEND_EMAIL: t.String(),
	WSO2_SEND_EMAIL_WITH_ATTACHMENT: t.String(),
	WSO2_SEND_SMS: t.String(),
	WSO2_AUTOPAY_CHECK: t.String(),
	WSO2_SSM_INFO: t.String(),
	WSO2_CUSTOMER_PROFILE_CHECK: t.String(),
	WSO2_DMS_CREDIT_SCORE: t.String(),
	WSO2_EDWH_MOANA: t.String(),
	WSO2_NOVA_TRANSFER_REQUEST_STATUS: t.String(),
	WSO2_CREATE_SR_NOVA: t.String(),
	WSO2_CREATE_SR_ICP: t.String(),
	WSO2_CREATE_CUSTOMER_NOVA: t.String(),
	WSO2_CREATE_CUSTOMER_ICP: t.String(),
	WSO2_RETRIEVE_CUSTOMER_NOVA: t.String(),
	WSO2_RETRIEVE_CUSTOMER_ICP: t.String(),
	WSO2_CREATE_TT_NOVA: t.String(),
	WSO2_CREATE_TT_ICP: t.String(),
	WSO2_RETRIEVE_NTT: t.String(),
	WSO2_CTT_CHECK_ELIGIBILITY: t.String(),
	WSO2_REBATE_SUBMIT_TICKET: t.String(),
	WSO2_SUBMIT_NES_SURVEY: t.String(),
	WSO2_ADDRESS_BY_ID: t.String(),
	WSO2_ADDRESS_BY_COORDINATE: t.String(),
	WSO2_ADDRESS_BY_KEYWORD_STATE: t.String(),
	WSO2_EASYFIX_TNPS_SURVEY: t.String(),
	WSO2_CREATE_TROIKA_DEMAND: t.String(),
	WSO2_QUERY_TROIKA_DEMAND: t.String(),
	WSO2_RETRIEVE_BUNDLE_DEVICE: t.String(),
	OMG_X_API_KEY: t.String(),
	OMG_GET_OTT_SUBSCRIPTION: t.String(),
	OMG_NEW_OTT_ORDER: t.String(),
	OMG_NEW_OTT_SWAP_ORDER: t.String(),
	OMG_VERIFY_OTT_USER_ID: t.String(),
	OMG_GET_OTT_ENTITLEMENT: t.String(),
	OMG_NETFLIX_GET_TOKEN: t.String(),
	OMG_NETFLIX_GET_PLAN: t.String(),
	OMG_NETFLIX_CHANGE_PLAN_ORDER: t.String(),
	OMG_NETFLIX_CANCEL: t.String(),
	OMG_HBO_GET_TOKEN: t.String(),
	OMG_HBO_GET_BUNDLE: t.String(),
	OMG_DISNEY_CHANGE_MOBILE_NO: t.String(),
	OMG_HBO_GET_ACTIVATION_ALA_CARTE_URL: t.String(),
	OMG_HBO_GET_ACTIVATION_BUNDLE_URL: t.String(),
	AUTOPAY_REGISTRATION_TEMPLATE: t.String(),
	AUTOPAY_MODIFICATION_TEMPLATE: t.String(),
	AUTOPAY_TERMINATION_TEMPLATE: t.String(),
	OSES_PAYMENT_RECEIPT_TEMPLATE: t.String(),
	CLOUD_CONNECT_ECOMMERCE_TEMPLATE: t.String(),
	SR_CREATE_REVAMP_TEMPLATE: t.String(),
	OSES_PORTAL_BILL_MERCHANT_ID: t.String(),
	OSES_PORTAL_BILL_TXN_PASS: t.String(),
	OSES_CMC_SMS_MERCHANT_ID: t.String(),
	OSES_CMC_SMS_TXN_PASS: t.String(),
	OSES_SMS_MERCHANT_ID: t.String(),
	OSES_SMS_TXN_PASS: t.String(),
	OSES_APP_MERCHANT_ID: t.String(),
	OSES_APP_TXN_PASS: t.String(),
	OSES_DEFAULT_TXN_PASS: t.String(),
	OSES_RETURN_URL: t.String(),
	OSES_URL: t.String(),
	OSES_REVENUE_CODE_ICP: t.String(),
	OSES_REVENUE_CODE_NOVA: t.String(),
	CLOUD_CONNECT_CLIENT_LOGIN: t.String(),
	CLOUD_CONNECT_CLIENT_ID: t.String(),
	CLOUD_CONNECT_CLIENT_SECRET: t.String(),
	CLOUD_CONNECT_TENANT_REGISTER: t.String(),
	ECOMMERCE_LOGIN: t.String(),
	ECOMMERCE_USERNAME: t.String(),
	ECOMMERCE_REGISTER: t.String(),
	ECOMMERCE_PASSWORD: t.String(),
	PRIVATE_PDF_DOMAIN: t.String(),
	PUBLIC_PDF_DOMAIN: t.String(),
	SENDGRID_SEND_EMAIL: t.String(),
	WERAS_BASE_URL: t.String(),
	WERAS_CLIENT_ID: t.String(),
	WERAS_CLIENT_SECRET: t.String(),
	WERAS_USERNAME: t.String(),
	WERAS_PASSWORD: t.String(),
	WERAS_TOKEN: t.String(),
	WERAS_GET_ITEMS: t.String(),
	WERAS_GET_REDEEM_ITEMS: t.String(),
	WERAS_GET_MEMBERSHIP: t.String(),
	WERAS_GET_CUSTOMER_BILLS: t.String(),
	WERAS_GET_PROMOTION_LIST: t.String(),
	WERAS_GET_MY_REWARDS: t.String(),
	WERAS_GET_TRANSACTION: t.String(),
	WERAS_GET_ONLINE_CATALOGUE: t.String(),
	WERAS_UPDATE_REWARDS_FLAG: t.String(),
	WERAS_PERSONALISED_REPORTING: t.String(),
	OTT_VAR_NOTIFICATION_EMAIL_URL: t.String(),
	OTT_ULTIMATE_NOTIFICATION_EMAIL_URL: t.String(),
	OTT_SWAPPING_NOTIFICATION_EMAIL_URL: t.String(),
	OTT_SWAPPING_FAILED_NOTIFICATION_EMAIL_URL: t.String(),
	OTT_ALACARTE_NOTIFICATION_EMAIL_URL: t.String(),
	OTT_ULTIMATE_PLUS_NOTIFICATION_EMAIL_URL: t.String(),
	OTT_ULTIMATE_MAX_NOTIFICATION_EMAIL_URL: t.String(),
	TEMPORAL_TRIGGER_USER_TASK_SIGNAL_URL: t.String(),
	TEMPORAL_TRIGGER_WORKFLOW_URL: t.String(),
	DISNEY_CHANGE_MOBILE_EMAIL_URL: t.String(),
	CX_EMAIL_ADDRESS: t.String(),
	CONFIRMATION_MESH_NOTIFICATION_EMAIL_URL: t.String(),
	CONFIRMATION_CLOUD_GAMING_NOTIFICATION_EMAIL_URL: t.String(),
	CONFIRMATION_VARNAM_NOTIFICATION_EMAIL_URL: t.String(),
	CONFIRMATION_ANEKA_NOTIFICATION_EMAIL_URL: t.String(),
	CONFIRMATION_RUBY_NOTIFICATION_EMAIL_URL: t.String(),
	CONFIRMATION_ULTIMATE_NOTIFICATION_EMAIL_URL: t.String(),
	CONFIRMATION_UPB_NOTIFICATION_EMAIL_URL: t.String(),
	CONFIRMATION_SME_ADDON_NOTIFICATION_EMAIL_URL: t.String(),
	WSO2_RESERVE_ORDER_URL: t.String()
});

export type Env = Static<typeof envSchema>;

export const envConfig = (): Env => {
	const environment = `${process.env.ENVIRONMENT}`;
	const appConfig =
		config[
			environment.toLowerCase() as
				| 'dev'
				| 'sit'
				| 'uat'
				| 'preprod'
				| 'staging'
				| 'production'
		];
	return appConfig;
};
