import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import Elysia from 'elysia';
import { getIdTokenInfo } from '../../../../middleware/uaid/util/utils';
import { baseHeaderSchema } from '../../../../shared/schemas/api/headers.schema';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import * as rewardsSchema from '../schemas/api/rewards.schema';
import Rewards from '../services/rewards.service';

const rewardsV1Routes = new Elysia()
	.use(bearer())
	.resolve(async ctx => {
		const idTokenInfo = await getIdTokenInfo(ctx.bearer);
		return {
			Rewards: new Rewards(randomUUID(), idTokenInfo)
		};
	})
	.get(
		'/items',
		async (ctx): Promise<rewardsSchema.GetItemsRes> => {
			return await ctx.Rewards.getItemsService(ctx.query);
		},
		{
			detail: {
				tags: ['Reward'],
				description:
					'This API retrieves a paginated list of available reward items that users can redeem through the Unifi Rewards system. Each reward item includes details such as the name, description, category, brand, inventory channel, redemption flow, validity period, and status.'
			},
			query: rewardsSchema.getItemsReqBodySchema,
			response: {
				200: rewardsSchema.getItemsResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.post(
		'/redeem-item',
		async (ctx): Promise<rewardsSchema.GetRedeemItemRes> => {
			return await ctx.Rewards.redeemItemService(ctx.body);
		},
		{
			detail: {
				tags: ['Reward'],
				description:
					'This API processes the redemption of a reward item. Users must provide the reward ID and relevant details to complete the redemption process. The response includes the redeemed item details such as reward name, item ID, points redeemed, remaining balance, quantity, and a unique redemption code with an expiry date.'
			},
			body: rewardsSchema.getRedeemItemReqBodySchema,
			response: {
				200: rewardsSchema.getRedeemItemResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.get(
		'/membership',
		async (ctx): Promise<rewardsSchema.GetMembershipRes> => {
			return await ctx.Rewards.getMembershipService(ctx.headers, ctx.query);
		},
		{
			detail: {
				tags: ['Reward'],
				description:
					'This API retrieves membership details for a customer based on their authentication token. The response includes personal details, membership status, available points, loyalty card information, and tier benefits.'
			},
			headers: baseHeaderSchema,
			query: rewardsSchema.getMembershipReqBodySchema,
			response: {
				200: rewardsSchema.getMembershipResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.get(
		'/customer-bill',
		async (ctx): Promise<rewardsSchema.GetCustomerBillsRes> => {
			return await ctx.Rewards.getCustomerBillService(ctx.query);
		},
		{
			detail: {
				tags: ['Reward'],
				description:
					'This API retrieves customer billing details, including account number, bill number, outstanding balance, total due, and payment due date. The response provides a breakdown of the current month’s charges and processed payments.'
			},
			query: rewardsSchema.getCustomerBillsReqBodySchema,
			response: {
				200: rewardsSchema.getCustomerBillsResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.get(
		'/promotions',
		async (ctx): Promise<rewardsSchema.GetPromotionListRes> => {
			return await ctx.Rewards.getPromotionService(ctx.query);
		},
		{
			detail: {
				tags: ['Reward'],
				description:
					'This API retrieves a list of promotions available to a customer. The response includes promotion name, description, category, brand, inventory channel, redemption method, start and expiry dates, and applicable terms and conditions.'
			},
			query: rewardsSchema.getPromotionListReqBodySchema,
			response: {
				200: rewardsSchema.getPromotionListResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.get(
		'/my-rewards',
		async (ctx): Promise<rewardsSchema.GetMyRewardsRes> => {
			return await ctx.Rewards.getMyRewardsService(ctx.query);
		},
		{
			detail: {
				tags: ['Reward'],
				description:
					'This API retrieves a list of rewards a customer has redeemed. The response includes reward item details, redemption status, voucher code, expiry date, and fulfillment history.'
			},
			query: rewardsSchema.getMyRewardsReqBodySchema,
			response: {
				200: rewardsSchema.getMyRewardsResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.get(
		'/point-transactions',
		async (ctx): Promise<rewardsSchema.GetTransactionsRes> => {
			return await ctx.Rewards.getTransactionsService(ctx.query);
		},
		{
			detail: {
				tags: ['Reward'],
				description:
					'This API retrieves a list of point transactions for a customer, including earned, redeemed, and expired points. The response includes transaction type, amount, status, and timestamps.'
			},
			query: rewardsSchema.getTransactionsReqBodySchema,
			response: {
				200: rewardsSchema.getTransactionsResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.get(
		'/online-catalogues',
		async (ctx): Promise<rewardsSchema.GetOnlineCatalogueRes> => {
			return await ctx.Rewards.getOnlineCataloguesService();
		},
		{
			detail: {
				tags: ['Reward'],
				description:
					'This API retrieves a list of online catalogue categories available in the Unifi Rewards system. The response includes category IDs and their corresponding names.'
			},
			response: {
				200: rewardsSchema.getOnlineCatalogueResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.patch(
		'/reward-flag',
		async (ctx): Promise<rewardsSchema.UpdateRewardsFlagRes> => {
			return await ctx.Rewards.updateRewardFlagService(ctx.body);
		},
		{
			detail: {
				tags: ['Reward'],
				description:
					'This API updates the status of a redeemed voucher. Users can mark a voucher as used or archive it from the "My Rewards" section. The response confirms whether the update was successful.'
			},
			body: rewardsSchema.updateRewardsFlagReqBodySchema,
			response: {
				200: rewardsSchema.updateRewardsFlagResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.get(
		'/report-eligibility',
		async (ctx): Promise<rewardsSchema.GetReportEligibilityRes> => {
			return await ctx.Rewards.getReportEligibilityService(ctx.query);
		},
		{
			detail: {
				tags: ['Reward'],
				description:
					'This API checks a customer’s eligibility for a rewards report. The response includes details such as available points, total earned points, redeemed points, and billing rebate eligibility.'
			},
			query: rewardsSchema.getReportEligibilityReqBodySchema,
			response: {
				200: rewardsSchema.getReportEligibilityResSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export default rewardsV1Routes;
