import { integer, json, pgTable, text, timestamp } from 'drizzle-orm/pg-core';

export const rewardsTemplateTableSchema = pgTable('rewards_template', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	ImageUrl: text('image_url').notNull(),
	CopyText: json('copy_text').notNull(),
	Hyperlink: json('hyperlink').notNull(),
	Platform: text('platform').notNull(),
	Category: text('category').notNull(),
	Type: text('type').notNull(),
	DateRange: text('date_range').notNull(),
	CreatedAt: timestamp('created_at').defaultNow().notNull(),
	ExpiredAt: timestamp('expired_at').notNull()
});
