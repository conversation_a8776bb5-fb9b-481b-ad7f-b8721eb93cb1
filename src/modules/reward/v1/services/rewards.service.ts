import { differenceInCalendarDays, isValid, parse } from 'date-fns';
import { and, eq, gte } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { envConfig } from '../../../../config/env.config';
import { SourceEnum } from '../../../../enum/header.enum';
import { DisplayEnum } from '../../../../enum/lov.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { IdTypeEnum } from '../../../../enum/user.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type { WerasGetCustomerBillsReq } from '../../../../integration/weras/schemas/api/werasGetCustomerBills.schema';
import type {
	TierInfo,
	WerasGetMembershipReq
} from '../../../../integration/weras/schemas/api/werasGetMembership.schema';
import type { WerasGetMyRewardsRes } from '../../../../integration/weras/schemas/api/werasGetMyRewards.schema';
import type { WerasGetPersonalisedReportingReq } from '../../../../integration/weras/schemas/api/werasGetPersonalisedReporting.schema';
import type { WerasGetRedeemItemReq } from '../../../../integration/weras/schemas/api/werasGetRedeemItem.schema';
import type { WerasUpdateRewardsFlagReq } from '../../../../integration/weras/schemas/api/werasUpdateRewardsFlag.schema';
import { UE_ERROR } from '../../../../middleware/error';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import { encrypt } from '../../../../shared/encryption/aesGcm';
import type { BaseHeader } from '../../../../shared/schemas/api/headers.schema';
import type * as moduleSchema from '../schemas/api/rewards.schema';
import { rewardsTemplateTableSchema } from '../schemas/db/rewardsTemplate.schema';

class Rewards {
	private db: NodePgDatabase;
	private mwIntegration: MwIntegration;
	private integrationId: string;
	private idTokenInfo: IdTokenInfo;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo) {
		this.db = getDbInstance();
		this.mwIntegration = new MwIntegration(integrationId);
		this.integrationId = integrationId;
		this.idTokenInfo = idTokenInfo;
	}

	getItemsService = async (
		payload: moduleSchema.GetItemsReqBody
	): Promise<moduleSchema.GetItemsRes> => {
		// Call integration layer to fetch data from WERAS
		const werasRes =
			await this.mwIntegration.WerasIntegration.getItemsWeras(payload);

		// Filter items for display 'both' or 'app_only'
		const filteredItems = werasRes.data.items.filter(
			item =>
				item.display === DisplayEnum.BOTH ||
				item.display === DisplayEnum.APP_ONLY
		);

		// Sort items by start_date descending
		const sortedItems = filteredItems.sort((a, b) => {
			const dateA = new Date(a.start_date);
			const dateB = new Date(b.start_date);
			return dateB.getTime() - dateA.getTime();
		});

		// Transform each item from integration to module schema format
		const transformedItems = sortedItems.map(item => ({
			Id: item.id,
			Name: item.name,
			Description: item.description,
			Category: item.category,
			Brand: item.brand,
			InventoryChannel: item.inventory_channel,
			RedemptionFlow: item.redemption_flow,
			RedemptionScope: item.redemption_scope,
			GameFlag: item.game_flag,
			Status: item.status,
			Segment: item.segment,
			AdditionalDescription: item.additional_description ?? '',
			RmValue: item.rm_value ?? 0,
			PointValue: item.point_value ?? 0,
			CodeValue: '', // Always blanked out
			StartDate: item.start_date,
			EndDate: item.end_date,
			Image: item.image,
			Csvfile: item.csvfile ?? '',
			FastTrack: item.fast_track,
			Tnc: item.tnc,
			DownloadUrl: item.download_url ?? '',
			Highlighted: item.highlighted,
			Exclude: item.exclude,
			Barcode: item.barcode,
			Qrcode: item.qrcode,
			Display: item.display,
			CreatedBy: item.created_by,
			ExpiryDate: item.expiry_date,
			ModifiedBy: item.modified_by,
			DeletedBy: item.deleted_by ?? 0,
			CreatedAt: item.created_at,
			UpdatedAt: item.updated_at,
			DeletedAt: item.deleted_at ?? '',
			Campaign: item.campaign ?? '',
			Stocks: item.stocks.map(stock => ({
				Id: stock.id,
				BranchId: stock.branch_id,
				ItemId: stock.item_id,
				Type: stock.type,
				Quantity: stock.quantity,
				QuantityThreshold: stock.quantity_threshold,
				TypeThreshold: stock.type_threshold,
				QuantityUsed: stock.quantity_used,
				Infinite: stock.infinite,
				CreatedBy: stock.created_by ?? 0,
				ModifiedBy: stock.modified_by ?? 0, // fallback to 0 if Weras gave null
				DeletedBy: stock.deleted_by ?? 0,
				CreatedAt: stock.created_at,
				UpdatedAt: stock.updated_at,
				DeletedAt: stock.deleted_at ?? ''
			})),
			RedemptionLimit: {
				Quantity: item.redemption_limit.quantity,
				QuantityPeriod: item.redemption_limit.quantity_period,
				QuantityUsed: item.redemption_limit.quantity_used,
				QuantityAvailable: item.redemption_limit.quantity_available
			}
		}));

		// Format pagination values to string
		const pagination = werasRes.data.paginationDetails;
		const mappedPagination = {
			PerPage: String(pagination.per_page),
			CurrentPage: String(pagination.current_page),
			Total: String(pagination.total),
			LastPage: String(pagination.last_page)
		};

		// Final transformed structure
		const mappedResponse = {
			Items: transformedItems,
			PaginationDetails: mappedPagination
		};

		// Return in standardize baseResponseSchema structure
		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: mappedResponse
		};
	};

	redeemItemService = async (
		payload: moduleSchema.GetRedeemItemReqBody
	): Promise<moduleSchema.GetRedeemItemRes> => {
		const werasReq: WerasGetRedeemItemReq = {
			itemId: payload.ItemId,
			customerId: payload.CustomerId,
			quantity: payload.Quantity,
			accountId: payload.AccountId || null
		};
		const werasRes =
			await this.mwIntegration.WerasIntegration.redeemItemWeras(werasReq);

		if (!werasRes.data) {
			throw new UE_ERROR('Empty WERAS Response', StatusCodeEnum.NO_CONTENT, {
				integrationId: this.integrationId,
				response: 'No content from WERAS redeem-item API'
			});
		}

		const mappedResponse = {
			RewardName: werasRes.data.reward_name ?? '',
			ItemId: werasRes.data.itemId ?? 0,
			PointsRedeemed: werasRes.data.points_redeemed ?? 0,
			PointsBalance: werasRes.data.points_balance ?? 0,
			Quantity: werasRes.data.quantity ?? 0,
			Code: werasRes.data.code ?? '',
			ExpiryDate: werasRes.data.expiry_date ?? '',
			ItemType: werasRes.data.item_type ?? ''
		};

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: mappedResponse
		};
	};

	getMembershipService = async (
		header: BaseHeader,
		payload: moduleSchema.GetMembershipReqBody
	): Promise<moduleSchema.GetMembershipRes> => {
		const scope = 'WERAS';
		const encryptedIdType = await encrypt(
			this.getIdType(this.idTokenInfo.IdType),
			scope
		);
		const encryptedIdValue = await encrypt(this.idTokenInfo.IdValue, scope);
		const source =
			header.source === SourceEnum.UNIFI_APP ? 'MYUNIFI-APP' : 'MYUNIFI-PORTAL';

		const reqBody: WerasGetMembershipReq = {
			login_scope: header.segment,
			login_source: source,
			action_flag: payload.ActionFlag,
			id_type: encryptedIdType,
			id_number: encryptedIdValue
		};

		const res =
			await this.mwIntegration.WerasIntegration.getMembershipWeras(reqBody);

		const customer = res.data?.customer;

		if (!customer) {
			throw new UE_ERROR('WERAS Membership Error', StatusCodeEnum.ACCEPTED, {
				integrationId: this.integrationId,
				response: 'No customer membership record found in WERAS'
			});
		}

		const transformedResponse = {
			Id: customer.id ?? 0,
			Name: customer.name ?? '',
			Email: customer.email ?? '',
			MemberNo: customer.member_no ?? '',
			MemberCategory: customer.member_category ?? '',
			LoyaltyCardNo: customer.loyalty_card_no ?? '',
			RegistrationDate: customer.registration_date ?? '',
			EarnedPoints: customer.earned_points ?? 0,
			RedeemedPoints: customer.redeemed_points ?? 0,
			ExpiredPoints: customer.expired_points ?? 0,
			MemberStatus: customer.member_status ?? '',
			RedirectUrl: customer.redirect_url ?? '',
			PointsExpiredThisMonth: customer.points_expired_thismonth ?? 0,
			AvailablePoints: customer.available_points ?? 0,
			GameUrl: customer.game_url ?? '',
			BannerUrl: customer.banner_url ?? '',
			Game: customer.game ?? '',
			Tier: customer.tier ?? '',
			TierPrevious: customer.tier_previous ?? '',
			TierInfo: (res.data?.tier_info || []).map((t: TierInfo) => ({
				Name: t.name ?? '',
				Benefit: t.benefit ?? [],
				BannerUrl: `${envConfig().WERAS_BASE_URL}${t.banner_url ?? ''}`
			})),
			PromoInfo: (res.data?.promo_info || []).map(p => ({
				Name: p.name ?? '',
				BannerUrl: p.banner_url ?? '',
				RedirectUrl: p.redirect_url ?? ''
			}))
		};

		return {
			Success: true,
			Code: 200,
			IntegrationId: this.integrationId,
			Response: transformedResponse
		};
	};

	private getIdType = (idType: string): string => {
		switch (this.idTokenInfo.IdType) {
			case IdTypeEnum.NEW_NRIC: {
				return 'MY_NRIC';
			}
			case IdTypeEnum.PASSPORT:
				return 'PASSPORT';
			case IdTypeEnum.MYPR:
				return 'MY_PR';
			case IdTypeEnum.MYKAS:
				return 'MY_KAS';
			case IdTypeEnum.NON_BRN:
			case IdTypeEnum.BRN:
				return 'BRN';
			default:
				return idType;
		}
	};

	getCustomerBillService = async (
		payload: moduleSchema.GetCustomerBillsReqBody
	): Promise<moduleSchema.GetCustomerBillsRes> => {
		const werasReq: WerasGetCustomerBillsReq = {
			customerId: payload.CustomerId,
			redemptionScope: payload.RedemptionScope
		};
		const werasRes =
			await this.mwIntegration.WerasIntegration.getCustomerBillWeras(werasReq);

		const transformedBills = werasRes.data.bill.map(bill => ({
			Id: bill.id,
			Source: bill.source,
			CustomerCpcId: bill.customer_cpc_id,
			IdNumber: bill.id_number,
			IdType: bill.id_type,
			AccountNumber: bill.account_number,
			BillPeriod: bill.bill_period,
			BillNumber: bill.bill_number,
			BillDate: bill.bill_date,
			OutstandingBalance: bill.outstanding_balance,
			CurrentMonthCharge: bill.current_month_charge,
			TotalDue: bill.total_due,
			ProcessedAmount: bill.processed_amount,
			PaymentDueDate: bill.payment_due_date,
			CreatedBy: bill.created_by,
			ModifiedBy: bill.modified_by,
			CreatedAt: bill.created_at,
			UpdatedAt: bill.updated_at,
			SyncedAt: bill.synced_at,
			AccountId: bill.account_id
		}));

		return {
			Success: true,
			Code: 200,
			IntegrationId: this.integrationId,
			Response: transformedBills
		};
	};

	getPromotionService = async (
		payload: moduleSchema.GetPromotionListReqBody
	): Promise<moduleSchema.GetPromotionListRes> => {
		const werasRes =
			await this.mwIntegration.WerasIntegration.getPromotionWeras(payload);

		const rawPromotions = werasRes.data?.response || [];

		const filteredPromos = rawPromotions.filter(
			promo =>
				promo.display === DisplayEnum.BOTH ||
				promo.display === DisplayEnum.APP_ONLY
		);

		const mapped = filteredPromos.map(promo => ({
			Id: promo.id,
			Name: promo.name,
			Description: promo.description,
			Category: promo.category,
			Brand: promo.brand,
			InventoryChannel: promo.inventory_channel,
			RedemptionFlow: promo.redemption_flow,
			RedemptionScope: promo.redemption_scope,
			Flag: promo.game_flag,
			Status: promo.status,
			Segment: promo.segment,
			AdditionalDescription: promo.additional_description ?? '',
			RmValue: promo.rm_value ?? 0,
			PointValue: promo.point_value ?? 0,
			CodeValue: promo.code_value ?? '',
			StartDate: promo.start_date,
			EndDate: promo.end_date,
			Image: promo.image,
			Csvfile: promo.csvfile ?? '',
			FastTrack: promo.fast_track,
			Tnc: promo.tnc,
			DownloadUrl: promo.download_url ?? '',
			Highlighted: promo.highlighted,
			Exclude: promo.exclude,
			Barcode: promo.barcode,
			Qrcode: promo.qrcode,
			Display: promo.display,
			CreatedBy: promo.created_by,
			ModifiedBy: promo.modified_by,
			DeletedBy: promo.deleted_by ?? 0,
			CreatedAt: promo.created_at,
			UpdatedAt: promo.updated_at,
			DeletedAt: promo.deleted_at ?? '',
			ExpiryDate: promo.expiry_date,
			Customer: promo.customer
		}));

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				Message: werasRes.data?.message ?? null,
				Response: mapped,
				Error: werasRes.data?.error ?? null
			}
		};
	};

	async getMyRewardsService(
		payload: moduleSchema.GetMyRewardsReqBody
	): Promise<moduleSchema.GetMyRewardsRes> {
		const werasRes: WerasGetMyRewardsRes =
			await this.mwIntegration.WerasIntegration.getMyRewardsWeras(payload);

		const rewardItems: moduleSchema.GetMyRewardsRes['Response']['Data'] = [];
		const expiringVouchers: moduleSchema.GetMyRewardsRes['Response']['ExpiringVouchers'] =
			[];

		const details = werasRes.data?.response;

		if (!details?.data?.length) {
			throw new UE_ERROR(
				'No reward data found',
				StatusCodeEnum.WERAS_SERVICE_ERROR,
				{
					integrationId: this.integrationId,
					response: 'Empty response from WERAS get-my-rewards.'
				}
			);
		}

		for (const reward of details.data) {
			const item = reward.item;
			if (!item) continue;

			const mappedItem = {
				Id: item.id,
				Name: item.name,
				Description: item.description,
				Category: item.category,
				Brand: item.brand,
				InventoryChannel: item.inventory_channel,
				RedemptionFlow: item.redemption_flow,
				Status: item.status,
				Segment: item.segment,
				AdditionalDescription: item.additional_description ?? '',
				RmValue: typeof item.rm_value === 'number' ? item.rm_value : 0,
				PointValue: typeof item.point_value === 'number' ? item.point_value : 0,
				CodeValue: reward.voucher_code_lists?.code ?? item.code_value ?? '',
				StartDate: item.start_date,
				EndDate: item.end_date,
				Image: item.image,
				Csvfile: item.csvfile ?? '',
				FastTrack: item.fast_track,
				Tnc: item.tnc,
				DownloadUrl: item.download_url ?? '',
				Highlighted: item.highlighted,
				Exclude: item.exclude,
				Barcode: item.barcode,
				Qrcode: item.qrcode,
				CreatedBy: item.created_by,
				ModifiedBy: item.modified_by,
				DeletedBy: typeof item.deleted_by === 'number' ? item.deleted_by : 0,
				CreatedAt: item.created_at,
				UpdatedAt: item.updated_at,
				DeletedAt: item.deleted_at ?? '',
				ExpiryDate: reward.redemption?.expire_date ?? item.expiry_date ?? '',
				RedemptionCreatedAt: reward.redemption?.created_at ?? '',
				RedemptionId: reward.redemption?.id ?? 0,
				UseDate: reward.redemption?.use_date ?? ''
			};

			rewardItems.push(mappedItem);

			const expiryDate = parse(
				mappedItem.ExpiryDate ?? '',
				'yyyy-MM-dd HH:mm:ss',
				new Date()
			);
			const now = new Date();

			if (
				isValid(expiryDate) &&
				differenceInCalendarDays(expiryDate, now) <= 3 &&
				differenceInCalendarDays(expiryDate, now) >= 0
			) {
				expiringVouchers.push({
					Id: mappedItem.Id,
					ItemId: mappedItem.Id,
					RedemptionId: Number(mappedItem.RedemptionId ?? 0),
					ExpiryDate: mappedItem.ExpiryDate ?? '',
					ItemName: mappedItem.Name ?? ''
				});
			}
		}

		return {
			Success: true,
			Code: 200,
			IntegrationId: this.integrationId,
			Response: {
				PaginationDetails: {
					CurrentPage: String(details.current_page ?? ''),
					FirstPageUrl: details.first_page_url ?? '',
					From: String(details.from ?? ''),
					LastPage: String(details.last_page ?? ''),
					LastPageUrl: details.last_page_url ?? '',
					NextPageUrl: details.next_page_url ?? '',
					Path: details.path ?? '',
					PerPage: String(details.per_page ?? ''),
					PrevPageUrl: details.prev_page_url ?? '',
					To: String(details.to ?? ''),
					Total: String(details.total ?? '')
				},
				Data: rewardItems,
				ExpiringVouchers: expiringVouchers
			}
		};
	}

	getTransactionsService = async (
		payload: moduleSchema.GetTransactionsReqBody
	): Promise<moduleSchema.GetTransactionsRes> => {
		const werasRes =
			await this.mwIntegration.WerasIntegration.getTransactionsWeras(payload);

		const transaction = werasRes.data.transaction;

		const mappedResponse = {
			CurrentPage: String(transaction.current_page),
			FirstPageUrl: transaction.first_page_url,
			From: String(transaction.from),
			LastPage: String(transaction.last_page),
			LastPageUrl: transaction.last_page_url,
			NextPageUrl: transaction.next_page_url ?? '',
			Path: transaction.path,
			PerPage: String(transaction.per_page),
			PrevPageUrl: transaction.prev_page_url ?? '',
			To: String(transaction.to),
			Total: String(transaction.total),
			Data: transaction.data.map(txn => ({
				Name: txn.name ?? '',
				Description: txn.description ?? '',
				FulfilmentId: txn.fulfilment_id ?? 0,
				RedemptionId: txn.redemption_id ?? 0,
				Status: txn.status ?? '',
				CreatedAt: txn.created_at ?? '',
				ExpiryAt: txn.expiry_at ?? '',

				Payment:
					typeof txn.payment === 'string'
						? txn.payment
						: txn.payment === null
							? null
							: '',

				Fulfilment: {
					Id: txn.fulfilment?.id ?? 0,
					RedemptionId: txn.fulfilment?.redemption_id ?? 0,
					EventId:
						typeof txn.fulfilment.event_id === 'number'
							? txn.fulfilment.event_id.toString()
							: (txn.fulfilment.event_id ?? null),
					ItemId: txn.fulfilment?.item_id ?? 0,
					ItemQuantity: txn.fulfilment?.item_quantity ?? 0,
					PointsRedeemed: txn.fulfilment?.points_redeemed ?? 0,
					VoucherCodeId: txn.fulfilment?.voucher_code_id ?? 0,
					Status: txn.fulfilment?.status ?? '',
					Address1: txn.fulfilment?.address_1 ?? '',
					Address2: txn.fulfilment?.address_2 ?? '',
					Address3: txn.fulfilment?.address_3 ?? '',
					City: txn.fulfilment?.city ?? '',
					State: txn.fulfilment?.state ?? '',
					Country: txn.fulfilment?.country ?? '',
					PostalCode: txn.fulfilment?.postal_code ?? '',
					ContactNumber: txn.fulfilment?.contact_number ?? '',
					ShippedAt: txn.fulfilment?.shipped_at ?? '',
					ShippedBy: txn.fulfilment?.shipped_by ?? '',
					TrackingNumber: txn.fulfilment?.tracking_number ?? '',
					TrackingUrl: txn.fulfilment?.tracking_url ?? '',
					CreatedBy: txn.fulfilment?.created_by ?? 0,
					ModifiedBy: txn.fulfilment?.modified_by ?? 0,
					CreatedAt: txn.fulfilment?.created_at ?? '',
					UpdatedAt: txn.fulfilment?.updated_at ?? '',
					Item: {
						Id: txn.fulfilment?.item?.id ?? 0,
						Name: txn.fulfilment?.item?.name ?? '',
						Description: txn.fulfilment?.item?.description ?? '',
						Category: txn.fulfilment?.item?.category ?? 0,
						Brand: txn.fulfilment?.item?.brand ?? 0,
						InventoryChannel: txn.fulfilment?.item?.inventory_channel ?? '',
						RedemptionFlow: txn.fulfilment?.item?.redemption_flow ?? '',
						RedemptionScope: txn.fulfilment?.item?.redemption_scope ?? '',
						GameFlag: txn.fulfilment?.item?.game_flag ?? 0,
						Status: txn.fulfilment?.item?.status ?? '',
						Segment: txn.fulfilment?.item?.segment ?? '',
						AdditionalDescription:
							txn.fulfilment?.item?.additional_description ?? '',
						RmValue: txn.fulfilment?.item?.rm_value ?? 0,
						PointValue: txn.fulfilment?.item?.point_value ?? 0,
						CodeValue: txn.fulfilment?.item?.code_value ?? '',
						StartDate: txn.fulfilment?.item?.start_date ?? '',
						EndDate: txn.fulfilment?.item?.end_date ?? '',
						ExpiryDate: txn.fulfilment?.item?.expiry_date ?? '',
						Image: txn.fulfilment?.item?.image ?? '',
						Csvfile: txn.fulfilment?.item?.csvfile ?? '',
						FastTrack: txn.fulfilment?.item?.fast_track ?? 0,
						Tnc: txn.fulfilment?.item?.tnc ?? '',
						DownloadUrl: txn.fulfilment?.item?.download_url ?? '',
						Highlighted: txn.fulfilment?.item?.highlighted ?? 0,
						Exclude: txn.fulfilment?.item?.exclude ?? 0,
						Barcode: txn.fulfilment?.item?.barcode ?? '',
						Qrcode: txn.fulfilment?.item?.qrcode ?? '',
						Display: txn.fulfilment?.item?.display ?? '',
						CreatedBy: txn.fulfilment?.item?.created_by ?? 0,
						ModifiedBy: txn.fulfilment?.item?.modified_by ?? 0,
						DeletedBy: txn.fulfilment?.item?.deleted_by ?? 0,
						CreatedAt: txn.fulfilment?.item?.created_at ?? '',
						UpdatedAt: txn.fulfilment?.item?.updated_at ?? '',
						DeletedAt: txn.fulfilment?.item?.deleted_at ?? '',
						BarcodeLink: txn.fulfilment?.item?.barcode_link ?? '',
						QrCodeLink: txn.fulfilment?.item?.qr_code_link ?? ''
					}
				}
			}))
		};

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: mappedResponse
		};
	};

	getOnlineCataloguesService =
		async (): Promise<moduleSchema.GetOnlineCatalogueRes> => {
			const werasRes =
				await this.mwIntegration.WerasIntegration.getOnlineCataloguesWeras();

			const mappedResponse = {
				Category: werasRes.data.category.map(item => ({
					Id: item.id,
					Name: item.name
				}))
			};

			return {
				Success: true,
				Code: werasRes.code,
				IntegrationId: this.integrationId,
				Response: mappedResponse
			};
		};

	updateRewardFlagService = async (
		payload: moduleSchema.UpdateRewardsFlagReqBody
	): Promise<moduleSchema.UpdateRewardsFlagRes> => {
		const werasReq: WerasUpdateRewardsFlagReq = {
			voucherFlag: payload.VoucherFlag,
			redemptionId: payload.RedemptionId
		};
		const werasRes =
			await this.mwIntegration.WerasIntegration.updateRewardFlagWeras(werasReq);

		const mappedResponse = {
			Message: werasRes.data.message
		};

		return {
			Success: true,
			Code: werasRes.code,
			IntegrationId: this.integrationId,
			Response: mappedResponse
		};
	};

	getReportEligibilityService = async (
		payload: moduleSchema.GetReportEligibilityReqBody
	): Promise<moduleSchema.GetReportEligibilityRes> => {
		// Step 1: Fetch available templates
		const availableTemplate = await this.db
			.select()
			.from(rewardsTemplateTableSchema)
			.where(
				and(
					eq(rewardsTemplateTableSchema.Category, 'Personalized Rewards'),
					gte(rewardsTemplateTableSchema.ExpiredAt, new Date())
				)
			);

		// Step 2: Find home banner
		const banner = availableTemplate.find(
			image => image.Type === 'banner/home'
		);

		if (!banner) {
			throw new UE_ERROR('No Active Banner', StatusCodeEnum.NOT_FOUND_ERROR, {
				integrationId: this.integrationId,
				response: 'No active banner found from table image_details'
			});
		}

		const dateParts = banner.DateRange.split('-');

		if (dateParts.length !== 2) {
			throw new UE_ERROR(
				'Invalid Date Range Format',
				StatusCodeEnum.UNPROCESSABLE_ENTITY,
				{
					integrationId: this.integrationId,
					response: `Date range format is incorrect: ${banner.DateRange}`
				}
			);
		}

		const [start, end] = dateParts.map(date => date.trim());

		const startDate = new Date(Date.parse(`01 ${start}`)); // Add day for parsing
		const endDate = new Date(Date.parse(`01 ${end}`)); // Add day for parsing

		if (Number.isNaN(startDate.getTime()) || Number.isNaN(endDate.getTime())) {
			throw new UE_ERROR(
				'Invalid Parsed Dates',
				StatusCodeEnum.UNPROCESSABLE_ENTITY,
				{
					integrationId: this.integrationId,
					response: `Parsed dates are invalid: start=${start}, end=${end}`
				}
			);
		}

		const formattedStartDate = startDate.toISOString().split('T')[0];
		const formattedEndDate = endDate.toISOString().split('T')[0];

		// Step 4: Prepare request payload
		const werasReq: WerasGetPersonalisedReportingReq = {
			customer_id: payload.CustomerId,
			from_date: formattedStartDate,
			to_date: formattedEndDate
		};

		const werasRes =
			await this.mwIntegration.WerasIntegration.getPersonalisedReportingWeras(
				werasReq
			);

		if (!werasRes.status) {
			const werasError = werasRes || 'Unknown error from Weras service';
			throw new UE_ERROR(
				'Weras Service Error',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{
					integrationId: this.integrationId,
					response: werasError
				}
			);
		}

		const mappedResponse = werasRes.data.map(item => ({
			Points: {
				TotalAvailable: item.points.total_available,
				TotalEarned: item.points.total_earned,
				TotalUsed: item.points.total_used,
				RedeemedInDateRange: item.points.redeemed_in_date_range
			},
			RedeemedPointsInDateRangeRM: item.redeemed_points_in_date_range_RM,
			BillRebatePoints: item.bill_rebate_points,
			BillRebateRM: item.bill_rebate_RM,
			VoucherRedeemedCount: item.voucher_redeemed_count,
			VoucherSaveAmountRM: item.voucher_save_amount_RM,
			DonationRedeemedPoints: item.donation_redeemed_points,
			DonationRedeemedRM: item.donation_redeemed_RM
		}));

		// Step 6: Return the response
		return {
			Success: true,
			Code: werasRes.code,
			IntegrationId: this.integrationId,
			Response: mappedResponse
		};
	};
}

export default Rewards;
