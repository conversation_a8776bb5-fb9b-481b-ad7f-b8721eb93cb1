import { and, eq, ilike, or } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { pinoLog } from '../../../../config/pinoLog.config';
import {
	OttMerchantIdEnum,
	SubscribedAddOnsTagsEnum,
	TvPackPlanTypeEnum
} from '../../../../enum/addOns.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	OmgGetOttSubscriptionReq,
	OmgGetOttSubscriptionRes,
	OmgOttSubscribedRes,
	OmgPlanSubscribedRes
} from '../../../../integration/omg/schemas/api/omgOttSubscription.schema';
import {
	type SelectAddonsCatalogue,
	addonsCatalogueTableSchema
} from '../../../catalogue/v1/schemas/db/addOnsCatalogue.schema';
import {
	type SelectAddonsMetadata,
	addonsMetadataTableSchema
} from '../../../catalogue/v1/schemas/db/addOnsMetadata.schema';
import {
	type NetflixPlanObj,
	type OttDetails,
	type OttDetailsList,
	type SelectOttPlanCatalogue,
	ottPlanCatalogueTableSchema
} from '../../../catalogue/v1/schemas/db/ottPlanCatalogue.schema';
import {
	type SelectTvPackCatalogue,
	tvPackCatalogueTableSchema
} from '../../../catalogue/v1/schemas/db/tvPackCatalogue.schema';
import type {
	SubscribedAddonsObj,
	SubscribedOttList,
	SubscribedOttListRes
} from '../schemas/api/billingAccount.schema';

class SubscribedAddOns {
	private db: NodePgDatabase;
	private integrationId: string;

	constructor(db: NodePgDatabase, integrationId: string) {
		this.db = db;
		this.integrationId = integrationId;
	}

	async generateDeviceAddonsResObj(
		name: string,
		quantity: string,
		addOnDate: string
	): Promise<SubscribedAddonsObj> {
		const [addonsCatalogue]: SelectAddonsCatalogue[] = await this.db
			.select()
			.from(addonsCatalogueTableSchema)
			.where(ilike(addonsCatalogueTableSchema.Name, name))
			.execute()
			.catch((err: Error) => {
				pinoLog.error(err);
				return [];
			});

		if (addonsCatalogue) {
			const addOnsMetadata: SelectAddonsMetadata[] = await this.db
				.select()
				.from(addonsMetadataTableSchema)
				.where(eq(addonsMetadataTableSchema.Category, addonsCatalogue.Category))
				.execute()
				.catch((err: Error) => {
					pinoLog.error(err);
					return [];
				});

			return {
				Name: addonsCatalogue.Name ?? name,
				DisplayName: addonsCatalogue.DisplayName ?? name,
				Quantity: quantity,
				MonthlyCharge: `${addonsCatalogue.MonthlyCommitment}`,
				ProductDetails: addonsCatalogue.Description ?? [],
				Category: addonsCatalogue.Category,
				Tag: addonsCatalogue.Tag ?? SubscribedAddOnsTagsEnum.DEFAULT,
				AddonDate: addOnDate,
				ContractTerm: `${addonsCatalogue.ContractTerm}`,
				Summary: addonsCatalogue.Summary,
				Image: addonsCatalogue.ImageUrl,
				WarrantyPolicy: addOnsMetadata[0]?.WarrantyPolicyUrl ?? null,
				FaqUrl: addOnsMetadata[0]?.FaqUrl ?? null,
				TncUrl: addOnsMetadata[0]?.TncUrl ?? null,
				Specification: addonsCatalogue.Specification,
				UserGuideUrl: addOnsMetadata[0]?.UserGuideUrl ?? null
			};
		}
		return {
			Name: name,
			DisplayName: name,
			Quantity: quantity,
			MonthlyCharge: null,
			ProductDetails: [],
			Category: null,
			Tag: SubscribedAddOnsTagsEnum.DEFAULT,
			AddonDate: addOnDate,
			ContractTerm: null,
			Summary: null,
			Image: null,
			WarrantyPolicy: null,
			FaqUrl: null,
			TncUrl: null,
			Specification: null,
			UserGuideUrl: null
		};
	}

	async getOttListByTvPack(
		accountId: string,
		siebelTvPackName: string | undefined,
		internetSpeed: string,
		netflixProductName: string | null,
		enableErrorException = false
	): Promise<SubscribedOttListRes> {
		const omgReq: OmgGetOttSubscriptionReq = {
			accountType: 'Broadband',
			accountId: accountId
		};

		const omgRes: OmgGetOttSubscriptionRes = await new MwIntegration(
			this.integrationId
		).OmgIntegration.getOmgGetOttSubscription(omgReq, enableErrorException);

		if (!omgRes) {
			return {
				IsErrorFromOmg: true,
				OttSelectionCustChoice: [],
				OttSelectionFixed: [],
				OttAlaCarte: [],
				OttSelectionNetflix: []
			};
		}

		const omgActivePlanSubscribed: OmgPlanSubscribedRes =
			await this.getActiveOttSubscribed(omgRes);

		const ottList: SubscribedOttListRes = await this.getActivatedOttList(
			internetSpeed,
			omgActivePlanSubscribed
		);

		const nonActivatedOttList: SubscribedOttList =
			siebelTvPackName && siebelTvPackName !== ''
				? await this.getNonActivatedOttList(
						siebelTvPackName,
						internetSpeed,
						omgActivePlanSubscribed
					)
				: [];

		if (nonActivatedOttList.length > 0) {
			ottList.OttSelectionFixed.push(...nonActivatedOttList);
		}

		if (netflixProductName) {
			const netflixComplementary: SubscribedOttList =
				await this.getNetflixComplementary(
					omgActivePlanSubscribed,
					internetSpeed,
					netflixProductName
				);

			ottList.OttSelectionNetflix.push(...netflixComplementary);
		}

		return ottList;
	}

	private async getActivatedOttList(
		internetSpeed: string,
		omgPlanSubscribed: OmgPlanSubscribedRes
	): Promise<SubscribedOttListRes> {
		const ottSelectionCustChoiceArr: SubscribedOttList = [];
		const ottSelectionFixedArr: SubscribedOttList = [];
		const ottAlaCarteArr: SubscribedOttList = [];

		for (const planSubscribed of omgPlanSubscribed) {
			const findTvPackByPlanId = await this.db
				.select({
					tvPack: tvPackCatalogueTableSchema,
					ottPlan: ottPlanCatalogueTableSchema
				})
				.from(tvPackCatalogueTableSchema)
				.innerJoin(
					ottPlanCatalogueTableSchema,
					eq(
						tvPackCatalogueTableSchema.PlanId,
						ottPlanCatalogueTableSchema.PlanId
					)
				)
				.where(
					and(
						eq(tvPackCatalogueTableSchema.PlanId, planSubscribed.ottPlanId),
						or(
							eq(tvPackCatalogueTableSchema.PlanSpeed, 'All'),
							eq(tvPackCatalogueTableSchema.PlanSpeed, internetSpeed)
						)
					)
				)
				.limit(1)
				.execute()
				.catch((err: Error) => {
					pinoLog.error(err);
					return [];
				});

			const ottSubscribed: OmgOttSubscribedRes[] =
				planSubscribed.ottSubscribed.filter(ottSubscribed => {
					return !ottSubscribed.ottName.toLowerCase().includes('netflix');
				});

			for (const ottSub of ottSubscribed) {
				for (const tvPackObj of findTvPackByPlanId) {
					// retrieve ala carte (if any)
					if (
						planSubscribed.ottPlanId === 'P7' ||
						planSubscribed.ottPlanId === 'P8'
					) {
						for (const ottAlaCarteObj of tvPackObj.ottPlan.OttAlaCarte) {
							if (
								this.isOttSubscribedMatchedOttCatalogue(
									ottSub.ottMerchantId,
									ottAlaCarteObj.OttMerchantId,
									ottSub.ottName,
									ottAlaCarteObj.OttName,
									ottSub.ottOmgId,
									ottAlaCarteObj.OttOmgId
								)
							) {
								ottAlaCarteArr.push(
									...this.generateOttListRes(
										tvPackObj.tvPack,
										tvPackObj.ottPlan,
										ottAlaCarteObj,
										ottSub,
										true,
										true
									)
								);
							}
						}
					} else {
						// retrieve customer selection choice (if any)
						for (const ottSelectionCustChoiceObj of tvPackObj.ottPlan
							.OttSelectionCustChoice) {
							if (
								this.isOttSubscribedMatchedOttCatalogue(
									ottSub.ottMerchantId,
									ottSelectionCustChoiceObj.OttMerchantId,
									ottSub.ottName,
									ottSelectionCustChoiceObj.OttName,
									ottSub.ottOmgId,
									ottSelectionCustChoiceObj.OttOmgId
								)
							) {
								ottSelectionCustChoiceArr.push(
									...this.generateOttListRes(
										tvPackObj.tvPack,
										tvPackObj.ottPlan,
										ottSelectionCustChoiceObj,
										ottSub,
										false,
										true
									)
								);
							}
						}
						// retrieve fixed ott
						for (const ottSelectionFixedObj of tvPackObj.ottPlan
							.OttSelectionFixed) {
							if (
								this.isOttSubscribedMatchedOttCatalogue(
									ottSub.ottMerchantId,
									ottSelectionFixedObj.OttMerchantId,
									ottSub.ottName,
									ottSelectionFixedObj.OttName,
									ottSub.ottOmgId,
									ottSelectionFixedObj.OttOmgId
								)
							) {
								ottSelectionFixedArr.push(
									...this.generateOttListRes(
										tvPackObj.tvPack,
										tvPackObj.ottPlan,
										ottSelectionFixedObj,
										ottSub,
										false,
										true
									)
								);
							}
						}
					}
				}
			}
		}

		const res: SubscribedOttListRes = {
			IsErrorFromOmg: false,
			OttSelectionCustChoice: ottSelectionCustChoiceArr,
			OttSelectionFixed: ottSelectionFixedArr,
			OttAlaCarte: ottAlaCarteArr,
			OttSelectionNetflix: []
		};

		return res;
	}

	private async getNonActivatedOttList(
		siebelTvPackName: string,
		internetSpeed: string,
		omgPlanSubscribed: OmgPlanSubscribedRes
	): Promise<SubscribedOttList> {
		const ottSelectionFixedArr: SubscribedOttList = [];
		const findTvPackBySiebelTvPackName = await this.db
			.select({
				tvPack: tvPackCatalogueTableSchema,
				ottPlan: ottPlanCatalogueTableSchema
			})
			.from(tvPackCatalogueTableSchema)
			.innerJoin(
				ottPlanCatalogueTableSchema,
				eq(
					tvPackCatalogueTableSchema.PlanId,
					ottPlanCatalogueTableSchema.PlanId
				)
			)
			.where(
				and(
					eq(tvPackCatalogueTableSchema.SiebelTvPackName, siebelTvPackName),
					eq(ottPlanCatalogueTableSchema.AllowActivation, true),
					or(
						eq(tvPackCatalogueTableSchema.PlanSpeed, 'All'),
						eq(tvPackCatalogueTableSchema.PlanSpeed, internetSpeed)
					)
				)
			)
			.execute()
			.catch((err: Error) => {
				pinoLog.error(err.message);
				return [];
			});

		for (const tvPackObj of findTvPackBySiebelTvPackName) {
			if (tvPackObj.ottPlan.OttSelectionFixed.length > 0) {
				const ottSelectionFixed: OttDetailsList =
					tvPackObj.ottPlan.OttSelectionFixed.filter(ottSelectionFixedObj => {
						return (
							ottSelectionFixedObj.OttIsActive &&
							!ottSelectionFixedObj.OttName.toLowerCase().includes('netflix')
						);
					});

				for (const ottSelectionFixedObj of ottSelectionFixed) {
					const hasBundlePlanActivated = omgPlanSubscribed.find(
						planSubscribed => {
							return (
								planSubscribed.ottPlanId === tvPackObj.tvPack.PlanId &&
								ottSelectionFixed.length === planSubscribed.ottSubscribed.length
							);
						}
					);

					const isOttActivatedInOmg = omgPlanSubscribed.find(planSubscribed => {
						return planSubscribed.ottSubscribed.find(ottSubscribed => {
							return this.isOttSubscribedMatchedOttCatalogue(
								ottSubscribed.ottMerchantId,
								ottSelectionFixedObj.OttMerchantId,
								ottSubscribed.ottName,
								ottSelectionFixedObj.OttName,
								ottSubscribed.ottOmgId,
								ottSelectionFixedObj.OttOmgId
							);
						});
					});

					if (!isOttActivatedInOmg && !hasBundlePlanActivated) {
						ottSelectionFixedArr.push(
							...this.generateOttListRes(
								tvPackObj.tvPack,
								tvPackObj.ottPlan,
								ottSelectionFixedObj,
								undefined,
								false,
								false
							)
						);
					}
				}
			}
		}

		return ottSelectionFixedArr;
	}

	private async getNetflixComplementary(
		omgPlanSubscribed: OmgPlanSubscribedRes,
		internetSpeed: string,
		netflixProductName: string
	): Promise<SubscribedOttList> {
		const ottSelectionNetflixArr: SubscribedOttList = [];

		for (const planSubscribed of omgPlanSubscribed) {
			const [findTvPackByPlanId] = await this.db
				.select({
					tvPack: tvPackCatalogueTableSchema,
					ottPlan: ottPlanCatalogueTableSchema
				})
				.from(tvPackCatalogueTableSchema)
				.innerJoin(
					ottPlanCatalogueTableSchema,
					eq(
						tvPackCatalogueTableSchema.PlanId,
						ottPlanCatalogueTableSchema.PlanId
					)
				)
				.where(
					and(
						eq(tvPackCatalogueTableSchema.PlanId, planSubscribed.ottPlanId),
						or(
							eq(tvPackCatalogueTableSchema.PlanSpeed, 'All'),
							eq(tvPackCatalogueTableSchema.PlanSpeed, internetSpeed)
						)
					)
				)
				.execute()
				.catch((err: Error) => {
					pinoLog.error(err);
					return [];
				});

			const ottSubscribed: OmgOttSubscribedRes[] =
				planSubscribed.ottSubscribed.filter(ottSubscribed => {
					return ottSubscribed.ottName.toLowerCase().includes('netflix');
				});

			for (const ottSub of ottSubscribed) {
				for (const ottSelectionFixedObj of findTvPackByPlanId.ottPlan
					.OttSelectionFixed) {
					if (ottSelectionFixedObj.OttName.toLowerCase().includes('netflix')) {
						ottSelectionNetflixArr.push(
							...this.generateOttListRes(
								findTvPackByPlanId.tvPack,
								findTvPackByPlanId.ottPlan,
								ottSelectionFixedObj,
								ottSub,
								false,
								true
							)
						);
					}
				}
			}
		}

		const [findTvPackBySiebelTvPackName] = await this.db
			.select({
				tvPack: tvPackCatalogueTableSchema,
				ottPlan: ottPlanCatalogueTableSchema
			})
			.from(tvPackCatalogueTableSchema)
			.innerJoin(
				ottPlanCatalogueTableSchema,
				eq(
					tvPackCatalogueTableSchema.PlanId,
					ottPlanCatalogueTableSchema.PlanId
				)
			)
			.where(
				and(
					eq(tvPackCatalogueTableSchema.SiebelTvPackName, netflixProductName),
					eq(tvPackCatalogueTableSchema.PlanSpeed, internetSpeed)
				)
			)
			.execute()
			.catch((err: Error) => {
				pinoLog.error(err);
				return [];
			});

		if (findTvPackBySiebelTvPackName?.ottPlan?.OttSelectionNetflix.length > 0) {
			for (const ottSelectionFixedObj of findTvPackBySiebelTvPackName?.ottPlan
				?.OttSelectionFixed ?? []) {
				if (
					ottSelectionNetflixArr.filter(ottSelectionNetflixArrObj => {
						return (
							ottSelectionNetflixArrObj.OttMerchantId ===
								ottSelectionFixedObj.OttMerchantId &&
							ottSelectionFixedObj.OttName.toLowerCase().includes('netflix')
						);
					}).length === 0
				) {
					ottSelectionNetflixArr.push(
						...this.generateOttListRes(
							findTvPackBySiebelTvPackName.tvPack,
							findTvPackBySiebelTvPackName.ottPlan,
							ottSelectionFixedObj,
							undefined,
							false,
							false
						)
					);
				}
			}
		}

		return ottSelectionNetflixArr;
	}

	generateOttListRes = (
		tvPackCatalogueTableSchema: SelectTvPackCatalogue,
		ottPlanCatalogueTableSchema: SelectOttPlanCatalogue,
		ottDetails: OttDetails | undefined,
		ottSubscribed: OmgOttSubscribedRes | undefined,
		isAlaCarte: boolean,
		isActivated: boolean,
		netflixPlanObj?: NetflixPlanObj
	): SubscribedOttList => {
		return [
			{
				OttPlanType: isAlaCarte
					? TvPackPlanTypeEnum.ALA_CARTE
					: tvPackCatalogueTableSchema.PlanType,
				OttPlanId: isAlaCarte
					? tvPackCatalogueTableSchema.PlanId === 'P8'
						? tvPackCatalogueTableSchema.PlanId
						: 'P7'
					: tvPackCatalogueTableSchema.PlanId,
				OttPlanName: ottPlanCatalogueTableSchema.OttPlanName,
				OttIsActive: ottDetails?.OttIsActive ?? true,
				OttTxnId: ottSubscribed ? ottSubscribed.ottTxnId : 0,
				OttStartDate: ottSubscribed ? ottSubscribed.ottStartDate : '',
				OttExpiryDate: ottSubscribed ? ottSubscribed.ottExpiryDate : '',
				OttStatus: ottSubscribed ? ottSubscribed.ottStatus : '',
				OttUserId: ottSubscribed ? ottSubscribed.ottUserId : '',
				SwapAvailable: ottSubscribed ? ottSubscribed.swapAvailable : '',
				Swapped: ottSubscribed ? ottSubscribed.swapped : '',
				SwappedOttTxnId: ottSubscribed ? ottSubscribed.swappedOttTxnId : 0,
				SwapMode: ottSubscribed ? ottSubscribed.swapMode : '',
				SwapOttExpiryDate: ottSubscribed ? ottSubscribed.swapOttExpiryDate : '',
				SwapOttStartDate: ottSubscribed ? ottSubscribed.swapOttStartDate : '',
				OttName: ottDetails?.OttName ?? ottSubscribed?.ottName ?? '',
				OttMerchantId:
					ottDetails?.OttMerchantId ?? ottSubscribed?.ottMerchantId ?? 0,
				OttProductId: ottDetails?.OttProductId ?? '',
				OttOmgId: ottDetails?.OttOmgId ?? ottSubscribed?.ottOmgId ?? 0,
				OttUniversalLink:
					ottDetails?.OttUniversalLink ?? ottSubscribed?.ottUniversalLink ?? '',
				OttIconPath:
					ottDetails?.OttIconPath ?? ottSubscribed?.ottIconPath ?? '',
				OttLoginType:
					ottDetails?.OttLoginType ?? ottSubscribed?.ottLoginType ?? '',
				OttLoginInstruction: ottDetails?.OttLoginInstruction ?? '',
				OttVerificationInstruction:
					ottDetails?.OttVerificationInstruction ?? '',
				OttActivationLink: ottDetails?.OttActivationLink ?? '',
				OttSequence: ottDetails?.OttSequence ?? 0,
				OttPrice: ottDetails?.OttPrice ?? netflixPlanObj?.TmProductPrice ?? -1,
				OttPlanSwapGroup: ottPlanCatalogueTableSchema.OttSwapGroup,
				OttActivateStatus:
					isActivated &&
					ottSubscribed &&
					ottSubscribed.ottStatus !== 'Processing_Active'
						? 'Watch'
						: 'Activate',
				OttActivateType:
					isAlaCarte ||
					tvPackCatalogueTableSchema.PlanType ===
						TvPackPlanTypeEnum.SOFT_BUNDLE ||
					this.isSelfActivateMerchant(ottSubscribed?.ottMerchantId) ||
					this.isSelfActivateMerchant(ottDetails?.OttMerchantId)
						? 'Self'
						: 'Group',
				ShowChangePlan:
					!isAlaCarte &&
					(ottDetails?.OttMerchantId !== OttMerchantIdEnum.NETFLIX ||
						ottSubscribed?.ottMerchantId !== OttMerchantIdEnum.NETFLIX),
				AllowDisneyUpgrade: ottSubscribed?.allowDisneyUpgrade ?? '',
				AllowMaxUpgrade: ottSubscribed?.allowMaxUpgrade ?? '',
				NetflixTxnId: ottSubscribed?.netflixTxnId ?? '',
				AllowNetflixCancel: ottSubscribed?.allowNetflixCancel ?? '',
				OttDescription: ottDetails?.OttDescription ?? '',
				OttPackageDetails: ottDetails?.OttPackageDetails ?? [],
				OttPackageDuration: ottDetails?.OttPackageDuration ?? '',
				OttPackageType: ottDetails?.OttPackageType ?? ''
			}
		];
	};

	private isSelfActivateMerchant = (
		merchantId: number | undefined
	): boolean => {
		return (
			merchantId === OttMerchantIdEnum.HBO ||
			merchantId === OttMerchantIdEnum.NETFLIX ||
			merchantId === OttMerchantIdEnum.CMGO
		);
	};

	private isOttSubscribedMatchedOttCatalogue = (
		ottSubscribedMerchantId: number,
		ottCatalogueMerchantId: number,
		ottSubscribedName: string,
		ottCatalogueName: string,
		ottSubscribedOmgId: number,
		ottCatalogueOmgId: number
	): boolean => {
		return (
			ottSubscribedMerchantId === ottCatalogueMerchantId &&
			ottSubscribedOmgId === ottCatalogueOmgId &&
			(ottSubscribedName
				.trim()
				.toLowerCase()
				.includes(ottCatalogueName.trim().toLowerCase()) ||
				ottCatalogueName
					.trim()
					.toLowerCase()
					.includes(ottSubscribedName.trim().toLowerCase()))
		);
	};

	private isOttSubscribedActive = (ottSubscribedStatus: string): boolean => {
		return (
			ottSubscribedStatus === 'Active' ||
			ottSubscribedStatus === 'Processing_Active' ||
			ottSubscribedStatus === 'Swap_Retry' ||
			ottSubscribedStatus === 'Swapped'
		);
	};

	private getActiveOttSubscribed = async (
		omgRes: OmgGetOttSubscriptionRes
	): Promise<OmgPlanSubscribedRes> => {
		const activeOttList: OmgPlanSubscribedRes = [];
		for (const planSubscribed of omgRes?.planSubscribed ?? []) {
			activeOttList.push({
				ottPlanId: planSubscribed.ottPlanId,
				ottPlanName: planSubscribed.ottPlanName,
				ottSubscribed: planSubscribed.ottSubscribed.filter(ottSubscribed => {
					return this.isOttSubscribedActive(ottSubscribed.ottStatus);
				})
			});
		}

		return activeOttList;
	};
}

export default SubscribedAddOns;
