import { KciBillingCategoryEnum } from '../../../../enum/billing.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { LightweightFlagEnum } from '../../../../enum/wso2.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type { Wso2AccountValidationRes } from '../../../../integration/wso2/user/schemas/api/wso2AccountValidation.schema';
import type {
	Wso2ConciseCustInfoReq,
	Wso2ConciseCustInfoRes,
	Wso2KCIResponseData
} from '../../../../integration/wso2/user/schemas/api/wso2ConciseCustInfo.schema';
import type {
	Wso2CustomerAccountReq,
	Wso2CustomerAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2CustomerAccount.schema';
import { UE_ERROR } from '../../../../middleware/error';
import { getMyTimeZoneDate } from '../../../../shared/common';
import { encryptAesEcb } from '../../../../shared/encryption/aesEcb';
import { encrypt } from '../../../../shared/encryption/aesGcm';
import type {
	KCIBillingInfoReq,
	KCIBillingInfoRes
} from '../schemas/api/kci.schema';

class KCI {
	private integrationId: string;
	private mwIntegration: MwIntegration;

	constructor(integrationId: string) {
		this.mwIntegration = new MwIntegration(integrationId);
		this.integrationId = integrationId;
	}

	async getKCIBillingInfo(req: KCIBillingInfoReq): Promise<KCIBillingInfoRes> {
		// check bill month
		const year: number = Number.parseInt(req.BillMonth.slice(0, 2), 10) + 2000;
		const month: number = Number.parseInt(req.BillMonth.slice(2, 4), 10) - 1;
		const billDate: Date = new Date(year, month);
		const currentDate: Date = getMyTimeZoneDate();
		const twoMonthsAgo: Date = new Date(
			currentDate.setMonth(currentDate.getMonth() - 3)
		);
		if (billDate < twoMonthsAgo) {
			throw new UE_ERROR('Link expired', StatusCodeEnum.FORBIDDEN_ERROR);
		}

		// validate billing account number
		const regex = /^(3\d{9}|17\d{8})$/;

		if (regex.test(req.BillingAccountNo)) {
			throw new UE_ERROR('Link expired', StatusCodeEnum.FORBIDDEN_ERROR);
		}

		// validate BillHash
		let stringToEncrypt = null;
		if (req.BillHash.length < 5) {
			throw new UE_ERROR('Invalid Bill Hash', StatusCodeEnum.FORBIDDEN_ERROR);
		}

		if (req.Category.includes(KciBillingCategoryEnum.OTHERS)) {
			stringToEncrypt = req.BillingAccountNo + req.BillMonth;
		} else {
			stringToEncrypt = req.BillingAccountNo;
		}

		const hexStr = encryptAesEcb(
			stringToEncrypt,
			process.env.WSO2_KCI_API_KEY ?? ''
		);
		if (!hexStr.startsWith(req.BillHash)) {
			throw new UE_ERROR('Link expired', StatusCodeEnum.FORBIDDEN_ERROR);
		}

		if (
			req.Category.includes(KciBillingCategoryEnum.FINAL_ACCOUNT) ||
			req.Category.includes(KciBillingCategoryEnum.PROMISE_TO_PAY)
		) {
			const wso2Req: Wso2ConciseCustInfoReq = {
				requestHeader: {
					requestId: `XE-${Math.random().toString(36).slice(2)}`,
					eventName: 'evOXECONCISECustInfo'
				},
				kciRequest: {
					customerId: null,
					serviceNo: null,
					serviceId: null,
					account: req.BillingAccountNo
				}
			};
			const wso2Res: Wso2ConciseCustInfoRes =
				await this.mwIntegration.Wso2UserIntegration.getWso2ConciseCustInfo(
					wso2Req
				);

			if (
				wso2Res.kciResponse === null ||
				wso2Res.kciResponse.kciresponseData === null ||
				wso2Res.kciResponse.kciresponseData.length === 0
			) {
				throw new UE_ERROR('No records found', StatusCodeEnum.NO_CONTENT);
			}
			const wso2KCIResponseData: Wso2KCIResponseData =
				wso2Res.kciResponse.kciresponseData[0];

			return {
				Success: true,
				Code: StatusCodeEnum.OK,
				IntegrationId: this.integrationId,
				Response: {
					BillingAccountNo: String(wso2KCIResponseData.accountno),
					BillingAccountName: wso2KCIResponseData.accountname ?? '',
					IdType: '',
					IdValue: '',
					SystemName: '',
					AmountToBePaid: String(wso2KCIResponseData.p2pamount ?? ''),
					BillNo: '',
					BillDate: wso2KCIResponseData.billingdate ?? '',
					BillDueDate: '',
					TotalCurrent: '',
					TotalPrevious: '',
					TotalPreviousPayment: '',
					TotalPrevAdj: '',
					TotalOutstanding: String(wso2KCIResponseData.outstandingamount ?? 0),
					TotalDue: '',
					OverduePayment: '',
					PromiseToPayDate: wso2KCIResponseData.p2pdate ?? ''
				}
			};
		}

		let accountName = '';
		const wso2Res: Wso2AccountValidationRes =
			await this.mwIntegration.Wso2UserIntegration.getWso2BAVerificationByMonth(
				req.BillingAccountNo,
				req.BillMonth
			);

		if (wso2Res.Response.CustomerIdType && wso2Res.Response.CustomerIdNumber) {
			const wso2CAReq: Wso2CustomerAccountReq = {
				idType: wso2Res.Response.CustomerIdType,
				idValue: wso2Res.Response.CustomerIdNumber
			};
			const wso2CARes: Wso2CustomerAccountRes =
				await this.mwIntegration.Wso2UserIntegration.getWso2CustomerAccount(
					wso2CAReq,
					LightweightFlagEnum.YES
				);

			for (const ca of wso2CARes.Response?.CustomerAccounts ?? []) {
				if (ca?.SystemName === wso2Res.Response.SystemName) {
					accountName = ca?.Name ?? '';
				}
			}
		}

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				BillingAccountNo: req.BillingAccountNo,
				BillingAccountName: accountName,
				IdType: wso2Res.Response.CustomerIdType ?? '',
				IdValue: wso2Res.Response.CustomerIdNumber
					? await encrypt(wso2Res.Response.CustomerIdNumber)
					: '',
				SystemName: wso2Res.Response.SystemName ?? '',
				AmountToBePaid: wso2Res.Response.AmountToBePaid ?? '',
				BillNo: wso2Res.Response.BillNo ?? '',
				BillDate: wso2Res.Response.BillMonth ?? '',
				BillDueDate: wso2Res.Response.BillDueDate ?? '',
				TotalCurrent: wso2Res.Response.TotalCurrent ?? '',
				TotalPrevious: wso2Res.Response.TotalPrevious ?? '',
				TotalPreviousPayment: wso2Res.Response.TotalPreviousPayment ?? '',
				TotalPrevAdj: wso2Res.Response.TotalPrevAdj ?? '',
				TotalOutstanding: wso2Res.Response.TotalOutstanding ?? '',
				TotalDue: wso2Res.Response.TotalDue ?? '',
				OverduePayment: Number.parseFloat(
					wso2Res.Response.OverduePayment ?? ''
				).toFixed(2),
				PromiseToPayDate: ''
			}
		};
	}
}

export default KCI;
