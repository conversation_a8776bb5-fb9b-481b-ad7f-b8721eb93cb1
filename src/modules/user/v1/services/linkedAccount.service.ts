import { and, count, desc, eq, inArray } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getCache, setCache } from '../../../../config/cache.config';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	Wso2CustomerAccountReq,
	Wso2CustomerAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2CustomerAccount.schema';
import type {
	Wso2ServiceAccountReq,
	Wso2ServiceAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2ServiceAccount.schema';
import { UE_ERROR } from '../../../../middleware/error';
import {
	findPlanSpeedByInternetPlanName,
	getMyTimeZoneDate,
	icpBillingProfile,
	novaBillingProfile
} from '../../../../shared/common';
import { decrypt, encrypt } from '../../../../shared/encryption/aesGcm';
import type {
	DeleteLinkedAccountReq,
	DeleteNonOwnerReq,
	DeleteNonOwnerRes,
	LinkAccountReq,
	LinkedAccountRes,
	LinkedAccountsNonOwnersReq,
	LinkedAccountsNonOwnersRes,
	LinkedAccountsProfileReq,
	LinkedAccountsProfileRes,
	LinkedAddress,
	LinkedBillingAccount,
	LinkedCustomerAccounts,
	LinkedProductsObj,
	UpdateLinkedAccountReq
} from '../schemas/api/linkedAccount.schema';
import {
	type InsertLinkedAccounts,
	type SelectLinkedAccounts,
	linkedAccountsTableSchema
} from '../schemas/db/linkedAccounts.schema';

import { getDbInstance } from '../../../../config/db.config';
import { CacheKeyEnum } from '../../../../enum/cacheKey.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import {
	LightweightFlagEnum,
	SystemNameEnum
} from '../../../../enum/wso2.enum';
import type { Wso2AccountValidationRes } from '../../../../integration/wso2/user/schemas/api/wso2AccountValidation.schema';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import { deleteLinkedAccountProfileCache } from '../../../../shared/cache';
import type { NovaIcpBillingProfile } from '../../../../shared/schemas/api/novaBillingProfile.schema';
import AccountService from '../helpers/accountService.helper';
import type {
	ConfirmAccountLinkingReq,
	ConfirmAccountLinkingRes
} from '../schemas/api/linkedAccount.schema';

class LinkedAccount {
	private integrationId: string;
	private db: NodePgDatabase;
	private accountService: AccountService;
	private idTokenInfo: IdTokenInfo;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo) {
		this.db = getDbInstance();
		this.idTokenInfo = idTokenInfo;
		this.integrationId = integrationId;
		this.accountService = new AccountService(this.integrationId);
	}

	async getLinkedAccountProfileByPage(
		query: LinkedAccountsProfileReq
	): Promise<LinkedAccountsProfileRes> {
		const pageNumber: number = !Number.isNaN(Number(query.Page))
			? Number(query.Page)
			: 1;
		const limitNumber: number = !Number.isNaN(Number(query.Limit))
			? Number(query.Limit)
			: 5;

		const [countLinkedAccounts] = await this.db
			.select({ count: count() })
			.from(linkedAccountsTableSchema)
			.where(
				and(
					eq(linkedAccountsTableSchema.NonOwnerIdType, this.idTokenInfo.IdType),
					eq(
						linkedAccountsTableSchema.NonOwnerIdValue,
						this.idTokenInfo.IdValue
					)
				)
			)
			.execute();

		const totalRecords: number = countLinkedAccounts.count ?? 0;
		const offset: number = (pageNumber - 1) * limitNumber;

		const linkedAccounts: SelectLinkedAccounts[] = await this.db
			.select()
			.from(linkedAccountsTableSchema)
			.where(
				and(
					eq(linkedAccountsTableSchema.NonOwnerIdType, this.idTokenInfo.IdType),
					eq(
						linkedAccountsTableSchema.NonOwnerIdValue,
						this.idTokenInfo.IdValue
					)
				)
			)
			.orderBy(desc(linkedAccountsTableSchema.UpdatedAt))
			.offset(offset)
			.limit(limitNumber)
			.execute();

		const res: LinkedAccountsProfileRes = {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				Page: pageNumber,
				Limit: limitNumber,
				TotalRecords: totalRecords,
				TotalPages: Math.ceil(totalRecords / limitNumber),
				CustomerAccounts: []
			}
		};

		const getWso2UserIntegration = new MwIntegration(this.integrationId)
			.Wso2UserIntegration;

		for (const linkedAccount of linkedAccounts) {
			const wso2CustomerAccReq: Wso2CustomerAccountReq = {
				idType: linkedAccount.OwnerIdType,
				idValue: linkedAccount.OwnerIdValue
			};
			const wso2CustomerAccRes: Wso2CustomerAccountRes =
				await getWso2UserIntegration.getWso2CustomerAccount(
					wso2CustomerAccReq,
					LightweightFlagEnum.NO
				);

			for (const ca of wso2CustomerAccRes.Response?.CustomerAccounts ?? []) {
				const ba = ca?.BillingAccounts?.find(
					value => value?.AccountNumber === linkedAccount.OwnerBillAccNo
				);
				if (ba) {
					const cacheName = `${CacheKeyEnum.LINKED_ACCOUNT_PROFILE}-${linkedAccount.OwnerBillAccNo}`;
					const cache = await getCache(cacheName);
					if (cache) {
						res.Response.CustomerAccounts.push(
							JSON.parse(cache) as LinkedCustomerAccounts
						);
						continue;
					}
					const systemName: string = ca?.SystemName || '';
					const billingAccounts: LinkedBillingAccount[] = [];
					const billingAccountNo: string = ba?.AccountNumber || '';
					const encryptedBillAccNo: string = await encrypt(billingAccountNo);
					let prodPromName = '';
					let prodPlanSpeed = '';
					const saProducts: LinkedProductsObj = [];
					let serviceAddress: LinkedAddress = null;
					let contractStartDate = '';
					let contractEndDate = '';
					let contractTenure = '';
					let startDate = '';

					const wso2ServiceAccountReq: Wso2ServiceAccountReq = {
						idType: linkedAccount.OwnerIdType,
						idValue: linkedAccount.OwnerIdValue,
						SystemName: systemName,
						BillingAccountNo: billingAccountNo
					};

					const wso2LightweightServiceAccountRes: Wso2ServiceAccountRes =
						await getWso2UserIntegration.getWso2ServiceAccount(
							wso2ServiceAccountReq,
							LightweightFlagEnum.YES,
							false
						);

					if (
						wso2LightweightServiceAccountRes?.Response?.ServiceAccount &&
						wso2LightweightServiceAccountRes?.Response.ServiceAccount.length > 0
					) {
						for (const sa of wso2LightweightServiceAccountRes.Response
							.ServiceAccount) {
							prodPromName = sa?.ProdPromName ?? '';
							contractEndDate = sa?.ContractEndDate ?? '';
							contractStartDate = sa?.ContractStartDate ?? '';
							contractTenure = sa?.ContractTenure ?? '';
							startDate = sa?.StartDate ?? '';
							prodPlanSpeed = findPlanSpeedByInternetPlanName(prodPromName);

							if (sa?.Products) {
								for (const saProduct of sa.Products) {
									let serialNumber = saProduct.SerialNumber;
									if (
										saProduct.ProductName === 'Internet' ||
										saProduct.ProductName === 'unifi TV'
									) {
										const [beforeAt, afterAt] =
											saProduct.SerialNumber.split('@');
										serialNumber = `${'•'.repeat(beforeAt.length)}@${afterAt}`;
									} else if (saProduct.ProductName === 'Voice') {
										serialNumber = saProduct.SerialNumber.replace(
											/.(?=.{4})/g,
											'•'
										);
									}
									saProducts.push({
										ProductName: saProduct?.ProductName,
										SerialNumber: serialNumber
									});
								}
							}

							serviceAddress = {
								UnitLot: '••••••',
								FloorNo: '••••••',
								BuildingName: '••••••',
								StreetType: '••••••',
								StreetName: '••••••',
								Section: '••••••',
								Postcode: '••••••',
								City: '••••••',
								State: sa?.State ?? '',
								Country: sa?.Country ?? ''
							};
						}
					}

					const { LatestOutstandingAmount, LatestBillDueDate } =
						await this.accountService.getBillingDetails(wso2ServiceAccountReq);

					const billingProfile: NovaIcpBillingProfile =
						systemName === SystemNameEnum.ICP
							? await icpBillingProfile(
									this.integrationId,
									this.idTokenInfo.IdType,
									this.idTokenInfo.IdValue,
									billingAccountNo,
									wso2CustomerAccRes
								)
							: await novaBillingProfile(this.integrationId, billingAccountNo);

					const lightweightBillingAccount: LinkedBillingAccount = {
						Id: linkedAccount.Id,
						AccountStatus: ba?.AccountStatus || '',
						AccountEmail: '••••••',
						AccountName: '••••••',
						AccountLabel: linkedAccount.AccountLabel,
						AccountContactNo:
							billingProfile.AccountContactNo.replace(/.(?=.{4})/g, '•') ?? '',
						BillingAccountNo: billingAccountNo,
						EncryptedBillAccNo: encryptedBillAccNo,
						LatestOutstandingAmount,
						LatestBillDueDate,
						ProdPromName: prodPromName,
						ProdPlanSpeed: prodPlanSpeed,
						ContractStartDate: contractStartDate,
						ContractEndDate: contractEndDate,
						ContractTenure: contractTenure,
						StartDate: startDate,
						Products: saProducts,
						ServiceAddress: serviceAddress
					};

					billingAccounts.push(lightweightBillingAccount);

					const lightweightCustomerAccount: LinkedCustomerAccounts = {
						SystemName: systemName,
						AccountStatus: `${ca?.Status}`,
						AccountNo: '••••••',
						AccountContactNo: `${ca.PreferredCustomerContactDetails?.TMContactCellPhone?.replace(
							/.(?=.{4})/g,
							'•'
						)}`,
						Relationship: linkedAccount.Relationship,
						BillingAccounts: billingAccounts
					};

					await setCache(
						cacheName,
						JSON.stringify(lightweightCustomerAccount),
						900
					); // 15 minutes

					res.Response.CustomerAccounts.push(lightweightCustomerAccount);
				}
			}
		}

		return res;
	}

	async add(req: LinkAccountReq): Promise<LinkedAccountRes> {
		const getWso2UserIntegration = new MwIntegration(this.integrationId)
			.Wso2UserIntegration;

		const result: SelectLinkedAccounts[] = await this.db
			.select()
			.from(linkedAccountsTableSchema)
			.where(
				and(
					eq(linkedAccountsTableSchema.NonOwnerIdType, this.idTokenInfo.IdType),
					eq(
						linkedAccountsTableSchema.NonOwnerIdValue,
						this.idTokenInfo.IdValue
					)
				)
			);

		if (result.length < 15) {
			const duplicate = result.find(
				a => a.OwnerBillAccNo === req.OwnerBillAccNo
			);
			if (duplicate !== undefined) {
				throw new UE_ERROR(
					'It looks like you already have added this account with us. Please check your existing added account details or contact support for assistance.',
					StatusCodeEnum.FORBIDDEN_ERROR
				);
			}

			const verifyResult: Wso2AccountValidationRes =
				await getWso2UserIntegration.getWso2BAVerification(req.OwnerBillAccNo);

			if (
				verifyResult.Status.Type === 'OK' &&
				verifyResult.Response.CustomerIdType &&
				verifyResult.Response.CustomerIdNumber
			) {
				if (
					verifyResult.Response.RetrieveBillURLResponse.AccountStatus ===
					'Terminated'
				) {
					throw new UE_ERROR(
						'The account you are trying to add is already terminated. Please check your account details or contact support for assistance.',
						StatusCodeEnum.FORBIDDEN_ERROR
					);
				}

				const result: InsertLinkedAccounts[] = await this.db
					.insert(linkedAccountsTableSchema)
					.values({
						OwnerBillAccNo: req.OwnerBillAccNo,
						AccountLabel: req.AccountLabel,
						ProductType: 'HOME',
						NonOwnerIdType: this.idTokenInfo.IdType,
						NonOwnerIdValue: this.idTokenInfo.IdValue,
						NonOwnerCredentialValue: req.NonOwnerCredentialValue,
						OwnerIdType: verifyResult.Response.CustomerIdType,
						OwnerIdValue: verifyResult.Response.CustomerIdNumber,
						SystemName: verifyResult.Response.SystemName ?? '',
						Relationship: req.Relationship,
						OwnerEmail: req.OwnerEmail
					})
					.returning();

				if (result.length > 0) {
					return {
						Success: true,
						Code: StatusCodeEnum.CREATED,
						IntegrationId: this.integrationId,
						Response: {
							Action: 'ADDED',
							Message: 'This account is successfully added to your profile'
						}
					};
				}

				throw new UE_ERROR(
					"We couldn't add this account this time. Please try again or reach out to our support team for help.",
					StatusCodeEnum.UE_INTERNAL_SERVER
				);
			}

			throw new UE_ERROR(
				"We couldn't verify the added account at the moment. Please ensure your information is correct or contact our support team for assistance.",
				StatusCodeEnum.UNPROCESSABLE_ENTITY
			);
		}

		throw new UE_ERROR(
			'You have reached the maximum limit of 15 added accounts. If you need further assistance, please contact our support team.',
			StatusCodeEnum.LIMIT_EXCEEDED_ERROR
		);
	}

	async update(req: UpdateLinkedAccountReq): Promise<LinkedAccountRes> {
		await this.db
			.update(linkedAccountsTableSchema)
			.set({ AccountLabel: req.AccountLabel, UpdatedAt: getMyTimeZoneDate() })
			.where(
				and(
					eq(linkedAccountsTableSchema.OwnerBillAccNo, req.OwnerBillAccNo),
					eq(linkedAccountsTableSchema.NonOwnerIdType, this.idTokenInfo.IdType),
					eq(
						linkedAccountsTableSchema.NonOwnerIdValue,
						this.idTokenInfo.IdValue
					)
				)
			)
			.returning()
			.catch(err => {
				throw new UE_ERROR(
					"We couldn't update your added account details at this moment. Please try again later or get in touch with our support team.",
					StatusCodeEnum.UE_INTERNAL_SERVER,
					{
						integrationId: this.integrationId,
						response: err
					}
				);
			});

		// remove cached data
		await deleteLinkedAccountProfileCache(req.OwnerBillAccNo);

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				Action: 'UPDATED',
				Message: 'The non owner account label is successfully updated'
			}
		};
	}

	async unlink(req: DeleteLinkedAccountReq): Promise<LinkedAccountRes> {
		const result: SelectLinkedAccounts[] = await this.db
			.delete(linkedAccountsTableSchema)
			.where(
				and(
					eq(linkedAccountsTableSchema.OwnerBillAccNo, req.OwnerBillAccNo),
					eq(linkedAccountsTableSchema.NonOwnerIdType, this.idTokenInfo.IdType),
					eq(
						linkedAccountsTableSchema.NonOwnerIdValue,
						this.idTokenInfo.IdValue
					)
				)
			)
			.returning();

		await deleteLinkedAccountProfileCache(req.OwnerBillAccNo);

		if (result.length > 0) {
			return {
				Success: true,
				Code: StatusCodeEnum.OK,
				IntegrationId: this.integrationId,
				Response: {
					Action: 'DELETED',
					Message: 'The account is successfully unlinked'
				}
			};
		}

		throw new UE_ERROR(
			"We couldn't remove your added account right now. Please try again later or contact our support team for assistance.",
			StatusCodeEnum.UE_INTERNAL_SERVER
		);
	}

	async confirmAccountLinking(
		req: ConfirmAccountLinkingReq
	): Promise<ConfirmAccountLinkingRes> {
		const getWso2UserIntegration = new MwIntegration(this.integrationId)
			.Wso2UserIntegration;

		const wso2verified: Wso2AccountValidationRes =
			await getWso2UserIntegration.getWso2BAVerification(req.OwnerBillAccNo);

		if (
			wso2verified.Response.CustomerIdType &&
			wso2verified.Response.CustomerIdNumber &&
			wso2verified.Status.Type === 'OK'
		) {
			if (
				wso2verified.Response.RetrieveBillURLResponse.AccountStatus ===
				'Terminated'
			) {
				throw new UE_ERROR(
					'The account you are trying to add is already terminated. Please check your account details or contact support for assistance.',
					StatusCodeEnum.FORBIDDEN_ERROR
				);
			}

			if (
				wso2verified.Response.CustomerIdType === this.idTokenInfo.IdType &&
				wso2verified.Response.CustomerIdNumber === this.idTokenInfo.IdValue
			) {
				throw new UE_ERROR(
					'You are not allowed to link your own account. Please check your account details or contact support for assistance.',
					StatusCodeEnum.FORBIDDEN_ERROR
				);
			}

			// Get the service account details
			const wso2SARes: Wso2ServiceAccountRes =
				(await getWso2UserIntegration.getWso2ServiceAccount(
					{
						idType: wso2verified.Response.CustomerIdType,
						idValue: wso2verified.Response.CustomerIdNumber,
						SystemName: wso2verified.Response.SystemName ?? '',
						BillingAccountNo: req.OwnerBillAccNo
					},
					LightweightFlagEnum.YES
				)) as Wso2ServiceAccountRes;

			const sa =
				wso2SARes?.Response?.ServiceAccount &&
				wso2SARes.Response.ServiceAccount.length > 0
					? wso2SARes.Response.ServiceAccount.find(sa => sa.Status === 'Active')
					: null;

			const prodPlanSpeed: string = findPlanSpeedByInternetPlanName(
				sa?.ProdPromName ?? ''
			);

			const serviceAddress = {
				UnitLot: sa?.UnitLot ?? '',
				FloorNo: sa?.FloorNo ?? '',
				BuildingName: sa?.BuildingName ?? '',
				StreetType: sa?.StreetType ?? '',
				StreetName: sa?.StreetName ?? '',
				Section: sa?.Section ?? '',
				Postcode: sa?.Postcode ?? '',
				City: sa?.City ?? '',
				State: sa?.State ?? '',
				Country: sa?.Country ?? ''
			};

			// Get the latest billing details
			const { LatestOutstandingAmount, LatestBillDueDate } =
				await this.accountService.getBillingDetails({
					idType: wso2verified.Response.CustomerIdType,
					idValue: wso2verified.Response.CustomerIdNumber,
					SystemName: wso2verified.Response.SystemName ?? '',
					BillingAccountNo: req.OwnerBillAccNo
				});
			const billingProfile: NovaIcpBillingProfile =
				wso2verified.Response.SystemName === SystemNameEnum.ICP
					? await icpBillingProfile(
							this.integrationId,
							wso2verified.Response.CustomerIdType,
							wso2verified.Response.CustomerIdNumber,
							req.OwnerBillAccNo
						)
					: await novaBillingProfile(this.integrationId, req.OwnerBillAccNo);

			return {
				Success: true,
				Code: StatusCodeEnum.OK,
				IntegrationId: this.integrationId,
				Response: {
					Verification: true,
					Details: {
						AccountStatus:
							wso2verified.Response.RetrieveBillURLResponse.AccountStatus ??
							'N/A',
						AccountLabel: req.AccountLabel,
						OwnerBillAccNo: req.OwnerBillAccNo,
						OwnerName: billingProfile.AccountName,
						OwnerEmail: billingProfile.AccountEmail,
						Relationship: req.Relationship,
						SystemName: wso2verified.Response.SystemName ?? '',
						LatestBillDueDate,
						LatestOutstandingAmount,
						ProdPlanSpeed: prodPlanSpeed,
						ServiceAddress: serviceAddress
					}
				}
			};
		}
		throw new UE_ERROR(
			"We couldn't verify the added billing account at this time. Please check your billing details or reach out to our support team for assistance.",
			StatusCodeEnum.UNPROCESSABLE_ENTITY
		);
	}

	async getNonOwnersLinkedAccountsByPage(
		query: LinkedAccountsNonOwnersReq
	): Promise<LinkedAccountsNonOwnersRes> {
		const pageNumber: number = !Number.isNaN(Number(query.Page))
			? Number(query.Page)
			: 1;
		const limitNumber: number = !Number.isNaN(Number(query.Limit))
			? Number(query.Limit)
			: 5;

		const getWso2UserIntegration = new MwIntegration(this.integrationId)
			.Wso2UserIntegration;

		const wso2CustomerAccRes: Wso2CustomerAccountRes =
			await getWso2UserIntegration.getWso2CustomerAccount(
				{
					idType: this.idTokenInfo.IdType,
					idValue: this.idTokenInfo.IdValue
				},
				LightweightFlagEnum.YES
			);

		const billingAccounts =
			wso2CustomerAccRes.Response?.CustomerAccounts?.flatMap(
				customer =>
					customer.BillingAccounts?.map(billing => billing.AccountNumber) ?? []
			)?.filter((account): account is string => account !== undefined) ?? [];

		const [countSharedAccounts] = await this.db
			.select({ count: count() })
			.from(linkedAccountsTableSchema)
			.where(inArray(linkedAccountsTableSchema.OwnerBillAccNo, billingAccounts))
			.execute();

		const totalRecords: number = countSharedAccounts.count ?? 0;
		const offset: number = (pageNumber - 1) * limitNumber;

		const sharedAccounts: SelectLinkedAccounts[] = await this.db
			.select()
			.from(linkedAccountsTableSchema)
			.where(inArray(linkedAccountsTableSchema.OwnerBillAccNo, billingAccounts))
			.offset(offset)
			.limit(limitNumber)
			.execute();

		const res: LinkedAccountsNonOwnersRes = {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				Page: pageNumber,
				Limit: limitNumber,
				TotalRecords: totalRecords,
				TotalPages: Math.ceil(totalRecords / limitNumber),
				NonOwners: []
			}
		};

		for (const sharedAccount of sharedAccounts) {
			res.Response.NonOwners.push({
				Id: sharedAccount.Id,
				SystemName: sharedAccount.SystemName,
				CredentialValue: sharedAccount.NonOwnerCredentialValue,
				EncryptedCredentialValue: await encrypt(
					sharedAccount.NonOwnerCredentialValue
				),
				Relationship: sharedAccount.Relationship,
				BillingAccountNo: sharedAccount.OwnerBillAccNo,
				EncryptedBillAccNo: await encrypt(sharedAccount.OwnerBillAccNo)
			});
		}

		return res;
	}

	async revokeAccess(req: DeleteNonOwnerReq): Promise<DeleteNonOwnerRes> {
		try {
			const decryptedCredentialValue: string = await decrypt(
				req.EncryptedCredentialValue
			);
			const decryptedBillAccNo: string = await decrypt(req.EncryptedBillAccNo);

			await this.db
				.delete(linkedAccountsTableSchema)
				.where(
					and(
						eq(linkedAccountsTableSchema.OwnerBillAccNo, decryptedBillAccNo),
						eq(
							linkedAccountsTableSchema.NonOwnerCredentialValue,
							decryptedCredentialValue
						),
						eq(linkedAccountsTableSchema.Id, req.Id)
					)
				)
				.execute();

			return {
				Success: true,
				Code: StatusCodeEnum.OK,
				IntegrationId: this.integrationId,
				Response: {
					Action: 'DELETED',
					Message: 'The billing account is successfully unlinked'
				}
			};
		} catch (error) {
			throw new UE_ERROR(
				"We couldn't remove your billing account right now. Please try again later or contact our support team for assistance.",
				StatusCodeEnum.UE_INTERNAL_SERVER
			);
		}
	}
}

export default LinkedAccount;
