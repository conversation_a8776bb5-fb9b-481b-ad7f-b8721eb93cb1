import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import {
	LightweightFlagEnum,
	SystemNameEnum
} from '../../../../enum/wso2.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	Wso2CustomerAccountReq,
	Wso2CustomerAccountRes,
	Wso2CustomerAccountsObj,
	Wso2PreferredCustomerContactDetails
} from '../../../../integration/wso2/user/schemas/api/wso2CustomerAccount.schema';
import type {
	Wso2ServiceAccount,
	Wso2ServiceAccountReq,
	Wso2ServiceAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2ServiceAccount.schema';
import type Wso2UserIntegration from '../../../../integration/wso2/user/wso2User.integration';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import {
	findPlanSpeedByInternetPlanName,
	icpBillingProfile,
	novaBillingProfile,
	paginateArray
} from '../../../../shared/common';
import { encrypt } from '../../../../shared/encryption/aesGcm';
import type {
	NovaAccountBillingAddress,
	NovaIcpBillingProfile
} from '../../../../shared/schemas/api/novaBillingProfile.schema';
import AccountService from '../helpers/accountService.helper';
import type {
	AddressDetails,
	AddressDetailsRes,
	BillingAddress,
	CustomerAccounts,
	LightweightAddress,
	LightweightBillingAccount,
	ProfileAccountsRes,
	SAProductsObj
} from '../schemas/api/profileAccount.schema';

class ProfileAccount {
	private integrationId: string;
	private getWso2UserIntegration: Wso2UserIntegration;
	private accountService: AccountService;
	private idTokenInfo: IdTokenInfo;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo) {
		this.integrationId = integrationId;
		this.idTokenInfo = idTokenInfo;
		this.getWso2UserIntegration = new MwIntegration(
			this.integrationId
		).Wso2UserIntegration;
		this.accountService = new AccountService(this.integrationId);
	}

	async getProfileByPage(
		page: number,
		limit: number
	): Promise<ProfileAccountsRes> {
		const wso2CustomerAccReq: Wso2CustomerAccountReq = {
			idType: this.idTokenInfo.IdType,
			idValue: this.idTokenInfo.IdValue
		};
		const wso2CustomerAccRes: Wso2CustomerAccountRes =
			await this.getWso2UserIntegration.getWso2CustomerAccount(
				wso2CustomerAccReq,
				LightweightFlagEnum.NO
			);
		const caWithBillingAccounts =
			wso2CustomerAccRes.Response?.CustomerAccounts?.filter(
				ca => ca?.BillingAccounts && ca?.BillingAccounts?.length > 0
			);
		const totalRecords: number = caWithBillingAccounts?.length ?? 0;
		const customerAccountsObj: Wso2CustomerAccountsObj = paginateArray(
			caWithBillingAccounts ?? [],
			page,
			limit
		);
		const caProfile: CustomerAccounts[] = await this.getProfileAccounts(
			wso2CustomerAccRes,
			customerAccountsObj
		);

		const offset: number = (Number(page === 0 ? 1 : page) - 1) * limit;
		const hasNextPage: boolean = offset + limit < totalRecords;

		// retrieve the next page of accounts asynchronously
		if (hasNextPage) {
			this.getProfileAccounts(wso2CustomerAccRes, caWithBillingAccounts ?? []);
		}

		const res: ProfileAccountsRes = {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				Page: page,
				Limit: limit,
				TotalRecords: totalRecords,
				TotalPages: Math.ceil(totalRecords / limit),
				CustomerAccounts: caProfile
			}
		};

		return res;
	}

	private async getProfileAccounts(
		wso2CustomerAccRes: Wso2CustomerAccountRes,
		wso2CustomerAccountsObj: Wso2CustomerAccountsObj
	): Promise<CustomerAccounts[]> {
		const res: CustomerAccounts[] = [];
		for (const ca of wso2CustomerAccountsObj ?? []) {
			const systemName: string = ca?.SystemName || '';
			const billingAccounts: LightweightBillingAccount[] = [];
			for (const ba of ca?.BillingAccounts ?? []) {
				const billingAccountNo: string = ba?.AccountNumber || '';
				const encryptedBillAccNo: string = await encrypt(billingAccountNo);
				const products: SAProductsObj = [];
				let prodPromName = '';
				let prodPlanSpeed = '';
				let productType = '';
				let serviceAddress: LightweightAddress = null;

				const wso2ServiceAccountReq: Wso2ServiceAccountReq = {
					idType: this.idTokenInfo.IdType,
					idValue: this.idTokenInfo.IdValue,
					SystemName: systemName,
					BillingAccountNo: billingAccountNo
				};

				const wso2LightweightServiceAccountRes: Wso2ServiceAccountRes =
					await this.getWso2UserIntegration.getWso2ServiceAccount(
						wso2ServiceAccountReq,
						LightweightFlagEnum.YES,
						false
					);

				if (
					wso2LightweightServiceAccountRes?.Response?.ServiceAccount &&
					wso2LightweightServiceAccountRes?.Response?.ServiceAccount.length > 0
				) {
					for (const sa of wso2LightweightServiceAccountRes.Response
						.ServiceAccount) {
						prodPromName = sa?.ProdPromName ?? '';
						prodPlanSpeed = findPlanSpeedByInternetPlanName(prodPromName);
						serviceAddress = {
							UnitLot: sa?.UnitLot,
							FloorNo: sa?.FloorNo,
							BuildingName: sa?.BuildingName,
							StreetType: sa?.StreetType ?? '',
							StreetName: sa?.StreetName ?? '',
							Section: sa?.Section ?? '',
							Postcode: sa?.Postcode ?? '',
							City: sa?.City ?? '',
							State: sa?.State ?? '',
							Country: sa?.Country ?? ''
						};

						sa?.Products && products.push(...(sa?.Products ?? []));
						productType = sa.ProductType ?? '';
					}
				}

				const { LatestOutstandingAmount, LatestBillDueDate } =
					await this.accountService.getBillingDetails(wso2ServiceAccountReq);

				const accountLabel: string =
					await this.accountService.getBillingAccountLabel(
						this.idTokenInfo.IdValue,
						billingAccountNo
					);

				const { AccountEmail, AccountContactNo }: NovaIcpBillingProfile =
					systemName === SystemNameEnum.ICP
						? await icpBillingProfile(
								this.integrationId,
								this.idTokenInfo.IdType,
								wso2ServiceAccountReq.idValue,
								billingAccountNo,
								wso2CustomerAccRes
							)
						: await novaBillingProfile(this.integrationId, billingAccountNo);

				const lightweightBillingAccount: LightweightBillingAccount = {
					AccountStatus: ba?.AccountStatus || '',
					AccountEmail: AccountEmail,
					AccountName: ba?.BillingName || '',
					AccountLabel: accountLabel,
					AccountContactNo: AccountContactNo,
					BillingAccountNo: billingAccountNo,
					EncryptedBillAccNo: encryptedBillAccNo,
					LatestOutstandingAmount,
					LatestBillDueDate,
					PaymentMethod:
						ba.ListOfTmComInvoiceProfileIntegration?.at(0)?.PaymentMethod,
					ProdPromName: prodPromName,
					ProdPlanSpeed: prodPlanSpeed,
					Products: products,
					ProductType: productType,
					ServiceAddress: serviceAddress
				};

				billingAccounts.push(lightweightBillingAccount);
			}

			res.push({
				SystemName: systemName,
				AccountStatus: `${ca?.Status}`,
				AccountNo: `${ca?.AccountNo}`,
				AccountContactNo: `${ca.PreferredCustomerContactDetails?.TMContactCellPhone}`,
				AccountEmail: `${ca.PreferredCustomerContactDetails?.TMContactEmailAddress}`,
				ContactId: `${ca.PreferredCustomerContactDetails?.ContactId}`,
				BillingAccounts: billingAccounts
			});
		}

		return res;
	}

	getAccountsAddressDetails = async (): Promise<AddressDetailsRes> => {
		let billingAddress: BillingAddress | null = null;
		let addressDetailsList: AddressDetails[] = [];
		const getWso2UserIntegration = new MwIntegration(this.integrationId)
			.Wso2UserIntegration;

		const wso2CARequest: Wso2CustomerAccountReq = {
			idType: this.idTokenInfo.IdType,
			idValue: this.idTokenInfo.IdValue
		};
		const wso2CARes = await getWso2UserIntegration.getWso2CustomerAccount(
			wso2CARequest,
			LightweightFlagEnum.NO
		);

		for (const ca of wso2CARes.Response?.CustomerAccounts ?? []) {
			if (ca?.SystemName === SystemNameEnum.NOVA && ca?.BillingAccounts) {
				for (const ba of ca?.BillingAccounts ?? []) {
					const billingProfile: NovaIcpBillingProfile =
						await novaBillingProfile(
							this.integrationId,
							ba?.AccountNumber ?? ''
						);
					billingAddress = this.setBillingAddressFromProfile(
						billingProfile.AccountAddress
					);
					const wso2SAReq: Wso2ServiceAccountReq = {
						SystemName: ca?.SystemName,
						idType: this.idTokenInfo.IdType,
						idValue: this.idTokenInfo.IdValue,
						BillingAccountNo: ba?.AccountNumber ?? ''
					};
					const wso2SARes: Wso2ServiceAccountRes =
						(await getWso2UserIntegration.getWso2ServiceAccount(
							wso2SAReq,
							LightweightFlagEnum.NO
						)) as Wso2ServiceAccountRes;
					const sa = wso2SARes?.Response?.ServiceAccount ?? [];
					addressDetailsList = addressDetailsList.concat(
						await this.setAddressDetailsFromServiceAccount(
							sa,
							ca?.PreferredCustomerContactDetails ?? {},
							billingAddress,
							ba?.AccountNumber ?? '',
							ca?.Name ?? ''
						)
					);
				}
			}
		}

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: addressDetailsList
		};
	};

	private setBillingAddressFromProfile(
		profile: NovaAccountBillingAddress
	): BillingAddress | null {
		if (profile) {
			let address1: string;
			let address2: string;

			if (profile.AddressType === 'High Rise') {
				address1 = `${profile.UnitLot}, ${profile.FloorNo}`;
				address2 = `${profile.BuildingName}, ${profile.StreetType}, ${profile.StreetName}, ${profile.Section}`;
			} else {
				address1 = `${profile.UnitLot}, ${profile.StreetType} ${profile.StreetName}`;
				address2 = profile.Section ?? '';
			}

			const billingAddress: BillingAddress = {
				BillingAddress1: address1,
				BillingAddress2: address2,
				City: profile.City ?? '',
				Country: profile.Country ?? '',
				Postcode: profile.Postcode ?? '',
				State: profile.State ?? ''
			};
			return billingAddress;
		}

		return null;
	}

	private async setAddressDetailsFromServiceAccount(
		sa: Wso2ServiceAccount,
		preferredCustomerContactDetails: Wso2PreferredCustomerContactDetails,
		billingAddress: BillingAddress,
		billingAccountNo: string,
		name: string
	): Promise<AddressDetails[]> {
		const addressDetailsList: AddressDetails[] = [];
		const encryptedBillingAccountNo: string = await encrypt(billingAccountNo);

		let contactId: string | null = null;
		if (preferredCustomerContactDetails?.ContactId) {
			contactId = preferredCustomerContactDetails?.ContactId;
		}

		for (const account of sa ?? []) {
			if (
				account?.Status !== 'Suspended' &&
				account?.BillingAccountNumber === billingAccountNo
			) {
				for (const moli of account?.ServiceAccountMoli ?? []) {
					let planName: string | null = null;
					if (moli?.ProdPromName) {
						planName = moli?.ProdPromName;
					}

					for (const oli of moli?.ServiceAccountOli ?? []) {
						if (oli?.Type === 'Speed' || oli?.SerialNumber?.endsWith('@iptv')) {
							let speed = 0;
							const speedObject = oli?.[
								'ListOfTmAssetMgmt-AssetXaIntegration'
							]?.['TmAssetMgmt-AssetXaIntegration']?.find(s => {
								if (s?.Name === 'Download Speed') {
									return s?.Value;
								}
							});
							if (speedObject) {
								speed =
									Number.parseInt(
										speedObject?.Value?.replaceAll(' Kbps', '') ?? '0'
									) / 1000;
							}

							const billingProfile: NovaIcpBillingProfile =
								await novaBillingProfile(this.integrationId, billingAccountNo);

							const addressDetails: AddressDetails = {
								PlanName: planName,
								EncryptedCustomerAccountNo: account?.AccountNo
									? await encrypt(account?.AccountNo)
									: '',
								ContactId: contactId,
								ServicePointId: account?.ServicePointId ?? '',
								TmDPLocation:
									moli?.['TmCutAssetMgmt-ServiceMeterIntegration']
										?.TMDPLocation ?? '',
								TmPremiseType:
									moli?.['TmCutAssetMgmt-ServiceMeterIntegration']
										?.TMPremiseType ?? '',
								TmExchangeName:
									moli?.['TmCutAssetMgmt-ServiceMeterIntegration']
										?.TMExchangeName ?? '',
								ProductName: account?.ProductName ?? '',
								ProductPartNumber: account?.ProductPartNumber ?? '',
								EncryptedBillingAccountNo: encryptedBillingAccountNo,
								Name: name,
								ContactNo: billingProfile.AccountContactNo,
								Email: billingProfile.AccountEmail,
								ServiceId: oli?.SerialNumber ?? '',
								DeliveryAddress: {
									AddressId:
										account?.['TmCutAssetMgmt-ServiceMeterIntegration']
											?.TmCutAddressIntegration?.Id ?? '',
									UnitNo:
										account?.['TmCutAssetMgmt-ServiceMeterIntegration']
											?.TmCutAddressIntegration?.ApartmentNumber ?? '',
									FloorNo:
										account?.['TmCutAssetMgmt-ServiceMeterIntegration']
											?.TmCutAddressIntegration?.TMFloorNo ?? '',
									BuildingName:
										account?.['TmCutAssetMgmt-ServiceMeterIntegration']
											?.TmCutAddressIntegration?.TMBuildingName ?? '',
									StreetType:
										account?.['TmCutAssetMgmt-ServiceMeterIntegration']
											?.TmCutAddressIntegration?.StreetType ?? '',
									StreetName:
										account?.['TmCutAssetMgmt-ServiceMeterIntegration']
											?.TmCutAddressIntegration?.StreetName ?? '',
									Section:
										account?.['TmCutAssetMgmt-ServiceMeterIntegration']
											?.TmCutAddressIntegration?.Section ?? '',
									City:
										account?.['TmCutAssetMgmt-ServiceMeterIntegration']
											?.TmCutAddressIntegration?.City ?? '',
									Postcode:
										account?.['TmCutAssetMgmt-ServiceMeterIntegration']
											?.TmCutAddressIntegration?.PostalCode ?? '',
									State:
										account?.['TmCutAssetMgmt-ServiceMeterIntegration']
											?.TmCutAddressIntegration?.State ?? '',
									Country:
										account?.['TmCutAssetMgmt-ServiceMeterIntegration']
											?.TmCutAddressIntegration?.Country ?? ''
								},
								BillingAddress: billingAddress,
								TmAccessTechnology:
									moli?.['TmCutAssetMgmt-ServiceMeterIntegration']
										?.TMAccessTechnology ?? '',
								DownloadSpeedInMbps: speed
							};
							addressDetailsList.push(addressDetails);
						}
					}
				}
			}
		}
		return addressDetailsList;
	}
}

export default ProfileAccount;
