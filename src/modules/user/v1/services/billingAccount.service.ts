import { and, eq, or, sql } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getCache, setCache } from '../../../../config/cache.config';
import { getDbInstance } from '../../../../config/db.config';
import { envConfig } from '../../../../config/env.config';
import { pinoLog } from '../../../../config/pinoLog.config';
import {
	AddOnsRequestCategoryEnum,
	SubscribedAddOnsTagsEnum,
	TvPackPlanTypeEnum
} from '../../../../enum/addOns.enum';
import { BillingProfileUpdateEnum } from '../../../../enum/billing.enum';
import { CacheKeyEnum } from '../../../../enum/cacheKey.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import {
	LightweightFlagEnum,
	SystemNameEnum
} from '../../../../enum/wso2.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	Wso2AnnualBillStatementReq,
	Wso2AnnualBillStatementRes
} from '../../../../integration/wso2/user/schemas/api/wso2AnnualBillStatement.schema';
import type {
	Wso2CustomerAccountReq,
	Wso2CustomerAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2CustomerAccount.schema';
import type {
	Wso2ExpDiscountReq,
	Wso2ExpDiscountRes
} from '../../../../integration/wso2/user/schemas/api/wso2ExpDiscount.schema';
import type { Wso2LightWeightBillingDetailsRes } from '../../../../integration/wso2/user/schemas/api/wso2LightweightBillingDetails.schema';
import type {
	Wso2NovaBillingProfileReq,
	Wso2NovaBillingProfileRes
} from '../../../../integration/wso2/user/schemas/api/wso2NovaBillingProfile.schema';
import type {
	Wso2ServiceAccountObj,
	Wso2ServiceAccountOli,
	Wso2ServiceAccountReq,
	Wso2ServiceAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2ServiceAccount.schema';
import type { Wso2UpdateBillingProfileReq } from '../../../../integration/wso2/user/schemas/api/wso2UpdateBillingProfile.schema';
import type {
	Wso2GetIbillBillingDetailsReq,
	Wso2GetIbillBillingDetailsRes
} from '../../../../integration/wso2/user/schemas/api/wso2iBillBillingDetails.schema';
import { UE_ERROR } from '../../../../middleware/error';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import { deleteWso2NovaBillingProfileCache } from '../../../../shared/cache';
import {
	findPlanSpeedByDownloadSpeed,
	findPlanSpeedByInternetPlanName,
	getUltimatePackSiebelTvPackName,
	icpBillingProfile,
	novaBillingProfile
} from '../../../../shared/common';
import { decrypt, encrypt } from '../../../../shared/encryption/aesGcm';
import type { NovaIcpBillingProfile } from '../../../../shared/schemas/api/novaBillingProfile.schema';
import type { BaseResponse } from '../../../../shared/schemas/api/responses.schema';
import {
	type SelectAddonsMetadata,
	addonsMetadataTableSchema
} from '../../../catalogue/v1/schemas/db/addOnsMetadata.schema';
import {
	type SelectTvPackCatalogue,
	tvPackCatalogueTableSchema
} from '../../../catalogue/v1/schemas/db/tvPackCatalogue.schema';
import AccountService from '../helpers/accountService.helper';
import SubscribedAddOns from '../helpers/subscribedAddOns.helper';
import type {
	AccountSettingsReq,
	AccountSettingsRes,
	AnnualBillStatementReq,
	AnnualBillStatementRes,
	AnnualBillStatements,
	BillingHistoryReq,
	BillingProfileObj,
	BillingProfileReq,
	BillingProfileRes,
	BillingTrend,
	ExpDiscountRes,
	ExpDiscountResObj,
	IbillBillingDetailsReq,
	IbillBillingDetailsRes,
	LightweightBillHistoryRes,
	NovaBillingProfileRes,
	QuickLinks,
	SAProductsObj,
	ServiceAccountObj,
	ServiceAccountProfile,
	SubscribedOttListRes,
	UpdateAccountLabelReq,
	UpdateBillingProfileReq,
	UpdateBillingProfileRes,
	UpdateQuickLinksReq
} from '../schemas/api/billingAccount.schema';
import {
	type SelectAccountSettings,
	accountSettingsTableSchema
} from '../schemas/db/accountSettings.schema';

class BillingAccount {
	private db: NodePgDatabase;
	private integrationId: string;
	private idTokenInfo: IdTokenInfo;
	private mwIntegration: MwIntegration;
	private accountService: AccountService;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo) {
		this.db = getDbInstance();
		this.mwIntegration = new MwIntegration(integrationId);
		this.integrationId = integrationId;
		this.accountService = new AccountService(this.integrationId);
		this.idTokenInfo = idTokenInfo;
	}

	async getBillingAccountProfile(
		req: BillingProfileReq
	): Promise<BillingProfileRes> {
		const decryptedBillAccNo: string = await decrypt(req.EncryptedBillAccNo);

		let billingProfileObj: BillingProfileObj = null;
		const wso2CustomerAccReq: Wso2CustomerAccountReq = {
			idType: this.idTokenInfo.IdType,
			idValue: this.idTokenInfo.IdValue
		};

		const wso2CustomerAccRes: Wso2CustomerAccountRes =
			await this.mwIntegration.Wso2UserIntegration.getWso2CustomerAccount(
				wso2CustomerAccReq,
				LightweightFlagEnum.NO
			);

		for (const ca of wso2CustomerAccRes.Response?.CustomerAccounts ?? []) {
			const ba = ca?.BillingAccounts?.find(
				value => value?.AccountNumber === decryptedBillAccNo
			);
			if (ba) {
				const systemName: string = ca?.SystemName || '';
				const serviceAccountObj: ServiceAccountObj =
					await this.getServiceAccountProfile(systemName, decryptedBillAccNo);

				const billingDetails = await this.accountService.getBillingDetails({
					idType: this.idTokenInfo.IdType,
					idValue: this.idTokenInfo.IdValue,
					SystemName: systemName,
					BillingAccountNo: decryptedBillAccNo
				});

				const annualBillStatements: AnnualBillStatements =
					await this.getAnnualBillStatementsLast5Years(
						systemName,
						decryptedBillAccNo
					);

				const accountLabel: string =
					await this.accountService.getBillingAccountLabel(
						this.idTokenInfo.IdValue,
						decryptedBillAccNo
					);

				const {
					AccountName,
					AccountEmail,
					AccountContactNo,
					AccountAddress
				}: NovaIcpBillingProfile =
					systemName === SystemNameEnum.ICP
						? await icpBillingProfile(
								this.integrationId,
								this.idTokenInfo.IdType,
								this.idTokenInfo.IdValue,
								decryptedBillAccNo,
								wso2CustomerAccRes
							)
						: await novaBillingProfile(this.integrationId, decryptedBillAccNo);

				billingProfileObj = {
					AccountStatus: ba?.AccountStatus || '',
					AccountNo: decryptedBillAccNo,
					AccountEmail: AccountEmail,
					AccountContactNo: AccountContactNo,
					AccountName: AccountName,
					AccountLabel: accountLabel,
					AccountAddress: AccountAddress,
					LatestOutstandingAmount: billingDetails.LatestOutstandingAmount,
					LatestBillDueDate: billingDetails.LatestBillDueDate,
					PaymentMethod:
						ba.ListOfTmComInvoiceProfileIntegration?.at(0)?.PaymentMethod ||
						'N/A',
					ServiceAccount: serviceAccountObj,
					BillingDetails: billingDetails.BillingDetails,
					AnnualBillStatements: annualBillStatements
				};
			}
		}

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				BillingAccount: billingProfileObj
			}
		};
	}

	async getAccountSettings(
		req: AccountSettingsReq
	): Promise<AccountSettingsRes> {
		const decryptedBillAccNo: string = await decrypt(req.EncryptedBillAccNo);

		const result: SelectAccountSettings[] =
			await this.findAccountSettingsByBillingAccNoAndIdValue(
				decryptedBillAccNo
			);

		const res: AccountSettingsRes = {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: result
		};
		return res;
	}

	async updateQuickLinks(body: UpdateQuickLinksReq): Promise<BaseResponse> {
		const decryptedBillAccNo: string = await decrypt(body.EncryptedBillAccNo);
		const existence: SelectAccountSettings[] =
			await this.findAccountSettingsByBillingAccNoAndIdValue(
				decryptedBillAccNo
			);

		let links = '';
		if (body.QuickLinks) {
			links = body.QuickLinks.filter((el: QuickLinks) => el.Active)
				.map((el: QuickLinks) => el.Title)
				.join(':');
		}

		if (existence.length === 0) {
			const result: SelectAccountSettings[] = await this.db
				.insert(accountSettingsTableSchema)
				.values({
					Email: body.Email,
					IdType: this.idTokenInfo.IdType,
					IdValue: this.idTokenInfo.IdValue,
					BillingAccountNo: decryptedBillAccNo,
					SystemName: body.SystemName,
					QuickLinks: links
				})
				.returning();

			if (result.length > 0) {
				return {
					Success: true,
					Code: StatusCodeEnum.OK,
					IntegrationId: this.integrationId,
					Message: 'Quick links successfully inserted!'
				};
			}
			throw new UE_ERROR(
				'Failed to add quicklinks!',
				StatusCodeEnum.UE_INTERNAL_SERVER
			);
		}

		const result: SelectAccountSettings[] = await this.db
			.update(accountSettingsTableSchema)
			.set({ QuickLinks: links, UpdatedAt: sql`now()` })
			.where(
				and(
					eq(accountSettingsTableSchema.BillingAccountNo, decryptedBillAccNo),
					eq(accountSettingsTableSchema.IdValue, this.idTokenInfo.IdValue)
				)
			)
			.returning();

		if (result.length > 0) {
			return {
				Success: true,
				Code: StatusCodeEnum.CREATED,
				IntegrationId: this.integrationId,
				Message: 'Quick links successfully updated!'
			};
		}

		throw new UE_ERROR(
			'Failed to update quicklinks!',
			StatusCodeEnum.UE_INTERNAL_SERVER,
			{ integrationId: this.integrationId, response: null }
		);
	}

	async updateAccountLabel(body: UpdateAccountLabelReq): Promise<BaseResponse> {
		const decryptedBillAccNo: string = await decrypt(body.EncryptedBillAccNo);
		const existence: SelectAccountSettings[] =
			await this.findAccountSettingsByBillingAccNoAndIdValue(
				decryptedBillAccNo
			);

		if (existence.length === 0) {
			await this.db
				.insert(accountSettingsTableSchema)
				.values({
					Email: body.Email,
					IdType: this.idTokenInfo.IdType,
					IdValue: this.idTokenInfo.IdValue,
					BillingAccountNo: decryptedBillAccNo,
					SystemName: body.SystemName,
					AccountLabel: body.AccountLabel
				})
				.catch(err => {
					throw new UE_ERROR(
						'Failed to create user!',
						StatusCodeEnum.UE_INTERNAL_SERVER,
						{
							integrationId: this.integrationId,
							response: err
						}
					);
				});

			return {
				Success: true,
				Code: StatusCodeEnum.CREATED,
				IntegrationId: this.integrationId,
				Message: 'Account label successfully inserted!'
			};
		}

		await this.db
			.update(accountSettingsTableSchema)
			.set({ AccountLabel: body.AccountLabel, UpdatedAt: sql`now()` })
			.where(
				and(
					eq(accountSettingsTableSchema.BillingAccountNo, decryptedBillAccNo),
					eq(accountSettingsTableSchema.IdValue, this.idTokenInfo.IdValue)
				)
			)
			.catch(err => {
				throw new UE_ERROR(
					'Failed to update account label!',
					StatusCodeEnum.UE_INTERNAL_SERVER,
					{
						integrationId: this.integrationId,
						response: err
					}
				);
			});

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Message: 'Account label successfully updated!'
		};
	}

	async getExpiryDiscountDetails(
		encryptedBillAccNo: string,
		products: SAProductsObj
	): Promise<ExpDiscountRes> {
		const decryptedBillAccNo: string = await decrypt(encryptedBillAccNo);
		const logins: string[] = products.map(obj => obj.SerialNumber ?? '');
		const wso2Req: Wso2ExpDiscountReq = {
			AccountNo: decryptedBillAccNo,
			Logins: logins
		};
		const wso2Res: Wso2ExpDiscountRes =
			await this.mwIntegration.Wso2UserIntegration.getWso2ExpiryDiscount(
				wso2Req
			);
		const expDiscountResObj: ExpDiscountResObj = [];

		for (const wso2ResObj of wso2Res.Response) {
			expDiscountResObj.push({
				EncryptedBillAccNo: await encrypt(wso2ResObj.Account_No),
				Login: wso2ResObj.Login,
				ServiceId: wso2ResObj.Service_Id,
				BundleName: wso2ResObj.Bundle_Name,
				DiscountName: wso2ResObj.Descr,
				DiscountAmount: wso2ResObj.Disc_Amount,
				CreatedDate: wso2ResObj.CreateD_T,
				EffectiveDate: wso2ResObj.Effective_T,
				EndDate: wso2ResObj.End_T,
				DiscountValidity: wso2ResObj.Disc_Validity,
				DayToExp: wso2ResObj.Day_To_Exp
			});
		}

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: expDiscountResObj
		};
	}

	async getBillingHistory(
		query: BillingHistoryReq
	): Promise<LightweightBillHistoryRes> {
		const decryptedBillAccNo: string = await decrypt(query.EncryptedBillAccNo);
		const wso2Req: Wso2ServiceAccountReq = {
			idType: this.idTokenInfo.IdType,
			idValue: this.idTokenInfo.IdValue,
			BillingAccountNo: decryptedBillAccNo,
			SystemName: query.SystemName
		};

		const wso2Res: Wso2LightWeightBillingDetailsRes =
			(await this.mwIntegration.Wso2UserIntegration.getWso2LightWeightBillingDetails(
				wso2Req
			)) as Wso2LightWeightBillingDetailsRes;

		if (!wso2Res?.Response) {
			throw new UE_ERROR(
				'No WSO2 response',
				StatusCodeEnum.UNPROCESSABLE_ENTITY,
				{
					integrationId: this.integrationId,
					response: wso2Res
				}
			);
		}

		// Set Billing Trends - For FE use in graphs
		const response = wso2Res.Response;
		const billingTrends: BillingTrend[] = [];
		const billingHistoryLength: number = response.BillingHistory?.length ?? 0;
		for (let i = billingHistoryLength - 1; i >= 0; i--) {
			let billingTrend: BillingTrend = null;
			if (i === 0) {
				if (
					response.PaymentHistory &&
					response.BillingHistory &&
					response.PaymentHistory[i]?.TransactionDatetime &&
					response.BillingHistory[i]?.BillDueDate
				) {
					let totalPaid = 0.0;
					const transactionDateTime: string =
						response.PaymentHistory[i]?.TransactionDatetime ?? '';
					const billDueDate: string =
						response.BillingHistory[i]?.BillDueDate ?? '';
					if (
						transactionDateTime !== '' &&
						billDueDate !== '' &&
						new Date(transactionDateTime) <= new Date(billDueDate)
					) {
						totalPaid = Number.parseFloat(
							response.PaymentHistory[i]?.PaymentAmount ?? '0'
						);
					}

					billingTrend = {
						BillDate: response.BillingHistory[i]?.BillDueDate,
						TotalDue: Number.parseFloat(
							response.BillingHistory[i]?.TotalDue ?? '0'
						),
						TotalPaid: totalPaid
					};
				}
			} else {
				billingTrend = {
					BillDate: response.BillingHistory?.[i]?.BillDate ?? '',
					TotalDue: Number.parseFloat(
						response.BillingHistory?.[i - 1]?.TotalPrevious ?? '0'
					),
					TotalPaid: Math.abs(
						Number.parseFloat(
							response.BillingHistory?.[i - 1]?.TotalPreviousPayment ?? '0'
						)
					)
				};
			}
			billingTrends.push(billingTrend);
		}

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				BillingTrend: billingTrends,
				Wso2Response: response
			}
		};
	}

	async getAnnualBillStatementByYear(
		query: AnnualBillStatementReq
	): Promise<AnnualBillStatementRes> {
		const cacheName = `${CacheKeyEnum.ANNUAL_BILL_STATEMENT_BY_YEAR}-${query.SystemName}-${query.EncryptedBillAccNo}-${query.Year}`;
		const cache = await getCache(cacheName);
		if (cache) {
			return JSON.parse(cache) as AnnualBillStatementRes;
		}
		const decryptedBillAccNo: string = await decrypt(query.EncryptedBillAccNo);
		const wso2Req: Wso2AnnualBillStatementReq = {
			requestHeader: {
				requestId: `${Math.random().toString(36).slice(2)}`,
				eventName:
					query.SystemName === SystemNameEnum.NOVA
						? 'evOUnifiPortal12MthsBillNOVA'
						: 'evOUnifiPortal12MthsBillICP'
			},
			accountNo: decryptedBillAccNo,
			year: query.Year
		};

		const wso2Res: Wso2AnnualBillStatementRes =
			(await this.mwIntegration.Wso2UserIntegration.getWso2AnnualBillStatement(
				wso2Req
			)) as Wso2AnnualBillStatementRes;

		await setCache(cacheName, JSON.stringify(wso2Res), 1800); // 30 minutes

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				Year: wso2Res?.tmArchivalResponse.statement.year,
				URL: wso2Res?.tmArchivalResponse.statement.url?.replaceAll(
					envConfig().PRIVATE_PDF_DOMAIN,
					envConfig().PUBLIC_PDF_DOMAIN
				),
				BillDate: wso2Res?.tmArchivalResponse.statement.billDate
			}
		};
	}

	async getNovaBillingProfile(
		encryptedBillAccNo: string
	): Promise<NovaBillingProfileRes> {
		const decryptedBillAccNo: string = await decrypt(encryptedBillAccNo);
		const wso2Req: Wso2NovaBillingProfileReq = {
			RetrieveBillingProfileRequest: {
				BillingAccountNo: decryptedBillAccNo
			}
		};
		const wso2Res =
			(await this.mwIntegration.Wso2UserIntegration.getWso2NovaBillingProfile(
				wso2Req
			)) as Wso2NovaBillingProfileRes;

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response:
				wso2Res.RetrieveBillingProfileResponse.BillingProfileDetails
					.BillingAccounts
		};
	}

	async updateBillingProfile(
		body: UpdateBillingProfileReq
	): Promise<UpdateBillingProfileRes> {
		const decryptedBillAccNo: string = await decrypt(body.EncryptedBillAccNo);
		const wso2Req: Wso2UpdateBillingProfileReq =
			body.SystemName === SystemNameEnum.ICP
				? await this.getWso2UpdateICPBillingProfileReqBody(
						body,
						decryptedBillAccNo
					)
				: await this.getWso2UpdateNovaBillingProfileReqBody(
						body,
						decryptedBillAccNo
					);

		await this.mwIntegration.Wso2UserIntegration.getWso2UpdateBillingProfile(
			wso2Req
		);

		// remove cached data
		await deleteWso2NovaBillingProfileCache(decryptedBillAccNo);

		return {
			Success: true,
			Code: StatusCodeEnum.CREATED,
			IntegrationId: this.integrationId,
			Message: `Successfully updated Billing ${body.UpdateType}`
		};
	}

	async getIbillBillingDetails(
		req: IbillBillingDetailsReq
	): Promise<IbillBillingDetailsRes> {
		const decryptedBillAccNo: string = await decrypt(req.EncryptedBillAccNo);
		const wso2Req: Wso2GetIbillBillingDetailsReq = {
			AccountNo: decryptedBillAccNo,
			BillDate: req.BillDate
		};
		const wso2Res: Wso2GetIbillBillingDetailsRes =
			await this.mwIntegration.Wso2UserIntegration.getWso2IbillBillingDetails(
				wso2Req
			);

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			...wso2Res
		};
	}

	private async getWso2UpdateICPBillingProfileReqBody(
		req: UpdateBillingProfileReq,
		decryptedBillAccNo: string
	): Promise<Wso2UpdateBillingProfileReq> {
		if (
			req.UpdateType === BillingProfileUpdateEnum.MOBILE_NO &&
			req.UpdateProfile.MobileNo
		) {
			return {
				UpdateBillingProfileRequest: {
					SystemName: req.SystemName,
					PerformAction: 'MODIFY',
					IdType: this.idTokenInfo.IdType,
					IdValue: this.idTokenInfo.IdValue,
					BillingAccountNo: decryptedBillAccNo,
					MobileNoFlag: 'Y',
					BillingProfileICP: {
						ICPSRCreate: {
							TmbEaiServiceRequest: {
								BillingAccountNo: decryptedBillAccNo,
								TMBNewMobileNumber: req.UpdateProfile.MobileNo
							}
						}
					}
				}
			};
		}
		if (
			req.UpdateType === BillingProfileUpdateEnum.EMAIL &&
			req.UpdateProfile.Email
		) {
			return {
				UpdateBillingProfileRequest: {
					SystemName: req.SystemName,
					PerformAction: 'MODIFY',
					IdType: this.idTokenInfo.IdType,
					IdValue: this.idTokenInfo.IdValue,
					BillingAccountNo: decryptedBillAccNo,
					BillMediaFlag: 'Y',
					BillingProfileICP: {
						ICPSRCreate: {
							TmbEaiServiceRequest: {
								BillingAccountNo: decryptedBillAccNo,
								TMBNewBillMedia: 'Email',
								TMBBillingMedia: 'Email',
								TMBEmailAddress: req.UpdateProfile.Email,
								TMBOldAddressId: ''
							}
						}
					}
				}
			};
		}
		if (
			req.UpdateType === BillingProfileUpdateEnum.ADDRESS &&
			req.UpdateProfile.Address
		) {
			return {
				UpdateBillingProfileRequest: {
					SystemName: req.SystemName,
					PerformAction: 'MODIFY',
					IdType: this.idTokenInfo.IdType,
					IdValue: this.idTokenInfo.IdValue,
					BillingAccountNo: decryptedBillAccNo,
					AddressFlag: 'Y',
					BillingProfileICP: {
						ICPSRCreate: {
							TmbEaiServiceRequest: {
								BillingAccountNo: decryptedBillAccNo,
								TMBEmailAddress: '',
								TMBNewMobileNumber: '',
								TMBOldAddressId: '1'
							}
						},
						ICPCreateCUTAddress: {
							TmbSrCutAddress: [
								{
									City: req.UpdateProfile.Address.City,
									Country: req.UpdateProfile.Address.Country,
									PostalCode: req.UpdateProfile.Address.PostalCode,
									Province: req.UpdateProfile.Address.Section ?? '',
									State: req.UpdateProfile.Address.State,
									StreetAddress: req.UpdateProfile.Address.StreetName,
									TMBAddressNum: req.UpdateProfile.Address.UnitNumber,
									TMBAddressType: req.UpdateProfile.Address.TMAddressType,
									TMBBuildingName: req.UpdateProfile.Address.BuildingName ?? '',
									TMBFloor: req.UpdateProfile.Address.FloorNo ?? '',
									TMBForeignCity: '',
									TMBForeignPostalCode: '',
									TMBNewPostalRegionCode: '',
									TMBStreetType: req.UpdateProfile.Address.StreetType,
									Section: req.UpdateProfile.Address.Section ?? ''
								}
							]
						}
					}
				}
			};
		}
		throw new UE_ERROR(
			'There is nothing to update!',
			StatusCodeEnum.BAD_REQUEST_ERROR
		);
	}

	private async getWso2UpdateNovaBillingProfileReqBody(
		req: UpdateBillingProfileReq,
		decryptedBillAccNo: string
	): Promise<Wso2UpdateBillingProfileReq> {
		if (
			req.UpdateType === BillingProfileUpdateEnum.MOBILE_NO &&
			req.UpdateProfile.MobileNo
		) {
			return {
				UpdateBillingProfileRequest: {
					SystemName: req.SystemName,
					PerformAction: 'MODIFY',
					IdType: this.idTokenInfo.IdType,
					IdValue: this.idTokenInfo.IdValue,
					BillingAccountNo: decryptedBillAccNo,
					MobileNoFlag: 'Y',
					BillingProfileNOVA: {
						BillingProfileSRCreate: {
							TmServiceRequestIntegration: {
								TMBillingAccount: decryptedBillAccNo,
								AccountId: '',
								TMBillingProfileId: req.BillingProfileId,
								ListOfTmEaiSrXmIntegration: {
									TmEaiSrXmIntegration: [
										{
											TMOldNew: 'OLD',
											BillingEmailTO: '',
											BillMedia: '',
											MobileNo: '',
											TMAddressId: ''
										},
										{
											TMOldNew: 'NEW',
											BillingEmailTO: '',
											BillMedia: '',
											MobileNo: req.UpdateProfile.MobileNo
										}
									]
								}
							}
						}
					}
				}
			};
		}
		if (
			req.UpdateType === BillingProfileUpdateEnum.EMAIL &&
			req.UpdateProfile.Email
		) {
			return {
				UpdateBillingProfileRequest: {
					SystemName: req.SystemName,
					PerformAction: 'MODIFY',
					IdType: this.idTokenInfo.IdType,
					IdValue: this.idTokenInfo.IdValue,
					BillingAccountNo: decryptedBillAccNo,
					BillMediaFlag: 'Y',
					BillingProfileNOVA: {
						BillingProfileSRCreate: {
							TmServiceRequestIntegration: {
								TMBillingAccount: decryptedBillAccNo,
								AccountId: '',
								TMBillingProfileId: req.BillingProfileId,
								ListOfTmEaiSrXmIntegration: {
									TmEaiSrXmIntegration: [
										{
											TMOldNew: 'OLD',
											BillingEmailTO: '',
											BillMedia: 'Email',
											MobileNo: '',
											TMAddressId: ''
										},
										{
											TMOldNew: 'NEW',
											BillingEmailTO: req.UpdateProfile.Email,
											BillMedia: 'Email',
											MobileNo: ''
										}
									]
								}
							}
						}
					}
				}
			};
		}
		if (
			req.UpdateType === BillingProfileUpdateEnum.ADDRESS &&
			req.UpdateProfile.Address
		) {
			return {
				UpdateBillingProfileRequest: {
					SystemName: req.SystemName,
					PerformAction: 'MODIFY',
					IdType: this.idTokenInfo.IdType,
					IdValue: this.idTokenInfo.IdValue,
					BillingAccountNo: decryptedBillAccNo,
					AddressFlag: 'Y',
					BillingProfileNOVA: {
						BillingProfileSRCreate: {
							TmServiceRequestIntegration: {
								TMBillingAccount: decryptedBillAccNo,
								AccountId: '',
								TMBillingProfileId: req.BillingProfileId,
								ListOfTmEaiSrXmIntegration: {
									TmEaiSrXmIntegration: [
										{
											TMOldNew: 'OLD',
											BillingEmailTO: '',
											BillMedia: '',
											MobileNo: '',
											TMAddressId: ''
										},
										{
											TMOldNew: 'NEW',
											BillingEmailTO: ''
										}
									]
								}
							}
						},
						SBLAddressCreate: req.UpdateProfile.Address
					}
				}
			};
		}
		throw new UE_ERROR(
			'There is nothing to update!',
			StatusCodeEnum.BAD_REQUEST_ERROR
		);
	}

	private async findAccountSettingsByBillingAccNoAndIdValue(
		decryptedBillAccNo: string
	): Promise<SelectAccountSettings[]> {
		return await this.db
			.select()
			.from(accountSettingsTableSchema)
			.where(
				and(
					eq(accountSettingsTableSchema.BillingAccountNo, decryptedBillAccNo),
					eq(accountSettingsTableSchema.IdValue, this.idTokenInfo.IdValue)
				)
			)
			.execute();
	}

	private async getServiceAccountProfile(
		systemName: string,
		billingAccountNumber: string
	): Promise<ServiceAccountObj> {
		const wso2ServiceAccountReq: Wso2ServiceAccountReq = {
			idType: this.idTokenInfo.IdType,
			idValue: this.idTokenInfo.IdValue,
			SystemName: systemName,
			BillingAccountNo: billingAccountNumber
		};

		const [
			wso2LightweightServiceAccountRes,
			wso2NonLightweightServiceAccountRes
		] = await Promise.all([
			this.mwIntegration.Wso2UserIntegration.getWso2ServiceAccount(
				wso2ServiceAccountReq,
				LightweightFlagEnum.YES,
				false
			),
			this.mwIntegration.Wso2UserIntegration.getWso2ServiceAccount(
				wso2ServiceAccountReq,
				LightweightFlagEnum.NO,
				false
			)
		]);

		if (this.isValidServiceAccountResponse(wso2LightweightServiceAccountRes)) {
			const res: ServiceAccountProfile[] = [];
			const subscribedAddOns = new SubscribedAddOns(
				this.db,
				this.integrationId
			);

			for (const sa of wso2LightweightServiceAccountRes?.Response
				?.ServiceAccount ?? []) {
				const internetPlanName: string = sa?.ProdPromName ?? '';
				const internetSpeed: string =
					findPlanSpeedByInternetPlanName(internetPlanName);
				const resObj: ServiceAccountProfile = this.buildServiceAccountProfile(
					sa,
					internetSpeed
				);

				if (
					this.isValidServiceAccountResponse(
						wso2NonLightweightServiceAccountRes
					)
				) {
					await this.processNonLightweightServiceAccount(
						wso2NonLightweightServiceAccountRes,
						resObj,
						internetPlanName,
						internetSpeed,
						subscribedAddOns
					);
				}

				if (resObj.ServiceID && resObj.ServiceID !== '') {
					await this.processOttListByTvPack(
						resObj.ServiceID,
						resObj,
						subscribedAddOns
					);
				}

				res.push(resObj);
			}

			return { IsErrorFromWso2: false, Response: res };
		}

		return {
			IsErrorFromWso2: true,
			Response: []
		};
	}

	private isValidServiceAccountResponse(
		response: Wso2ServiceAccountRes
	): response is Wso2ServiceAccountRes {
		return (
			!!response &&
			!!response.Response &&
			!!response.Response.ServiceAccount &&
			response.Response.ServiceAccount.length > 0
		);
	}

	private buildServiceAccountProfile(
		sa: Wso2ServiceAccountObj,
		internetSpeed: string
	): ServiceAccountProfile {
		return {
			Status: sa?.Status,
			ExpDiscFlg: sa?.ExpDiscFlg,
			UnitLot: sa?.UnitLot,
			FloorNo: sa?.FloorNo,
			BuildingName: sa?.BuildingName,
			StreetType: sa?.StreetType,
			StreetName: sa?.StreetName,
			Section: sa?.Section,
			Postcode: sa?.Postcode,
			City: sa?.City,
			State: sa?.State,
			Country: sa?.Country,
			Products: sa?.Products ?? [],
			ProductType: sa?.ProductType,
			ContractStartDate: sa?.ContractStartDate,
			ContractEndDate: sa?.ContractEndDate,
			ContractTenure: sa?.ContractTenure,
			ServiceID: sa?.ServiceID ?? '',
			ProdPromName: sa?.ProdPromName,
			ProdPlanSpeed: internetSpeed,
			ProdPlanUploadSpeed: internetSpeed,
			StartDate: sa?.StartDate,
			NetflixPlanName: null,
			SubscribedAddOns: []
		};
	}

	private async processNonLightweightServiceAccount(
		response: Wso2ServiceAccountRes,
		resObj: ServiceAccountProfile,
		internetPlanName: string,
		internetSpeed: string,
		subscribedAddOns: SubscribedAddOns
	): Promise<void> {
		for (const sa of response?.Response?.ServiceAccount ?? []) {
			if (sa?.ServiceAccountMoli) {
				for (const moli of sa.ServiceAccountMoli) {
					if (this.isRelevantProduct(moli.ProductName ?? '')) {
						await this.processServiceAccountOli(
							moli.ServiceAccountOli ?? [],
							resObj,
							internetPlanName,
							internetSpeed,
							subscribedAddOns
						);
					}
				}
			}
		}
	}

	private isRelevantProduct(productName: string): boolean {
		const relevantProducts: string[] = [
			'unifi playTV',
			'Residential High Speed Internet',
			'Media Apps',
			'unifi TV Residential',
			'Business High Speed Internet'
		];
		return relevantProducts.some(product => productName.includes(product));
	}

	private async processServiceAccountOli(
		olis: Wso2ServiceAccountOli[],
		resObj: ServiceAccountProfile,
		internetPlanName: string,
		internetSpeed: string,
		subscribedAddOns: SubscribedAddOns
	): Promise<void> {
		for (const oli of olis) {
			if (oli?.Status === 'Active') {
				await this.processOli(
					oli,
					resObj,
					internetPlanName,
					internetSpeed,
					subscribedAddOns
				);
			}
		}
	}

	private async processOli(
		oli: Wso2ServiceAccountOli,
		resObj: ServiceAccountProfile,
		internetPlanName: string,
		internetSpeed: string,
		subscribedAddOns: SubscribedAddOns
	): Promise<void> {
		const productName: string = oli?.ProductName ?? 'N/A';
		const addonDate: string =
			oli?.StartDate && oli.StartDate !== '' ? oli.StartDate : 'N/A';
		const quantity = oli?.Quantity ?? 'N/A';

		switch (oli?.Type) {
			case 'Speed':
				this.processSpeedOli(oli, resObj);
				break;
			case 'OTT':
				this.processOttOli(oli, resObj);
				break;
			case 'HyppTV Package':
				await this.processTvPackOli(
					oli,
					resObj,
					internetPlanName,
					internetSpeed
				);
				break;
			case 'Equipment':
				await this.processEquipmentOli(
					resObj,
					subscribedAddOns,
					productName,
					quantity,
					addonDate
				);
				break;
			case 'Miscellaneous Charges':
				await this.processMiscellaneousChargesOli(
					resObj,
					subscribedAddOns,
					productName,
					quantity,
					addonDate
				);
				break;
			case 'Service':
				await this.processServiceOli(
					oli,
					resObj,
					subscribedAddOns,
					quantity,
					addonDate
				);
				break;
		}
	}

	private processSpeedOli(
		oli: Wso2ServiceAccountOli,
		resObj: ServiceAccountProfile
	): void {
		resObj.ServiceID = oli.SerialNumber ?? '';
		const downloadSpeed = oli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
			'TmAssetMgmt-AssetXaIntegration'
		]?.find(asset => asset.Name === 'Download Speed');
		resObj.ProdPlanSpeed = findPlanSpeedByDownloadSpeed(
			downloadSpeed?.Value ?? ''
		);

		const uploadSpeed = oli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
			'TmAssetMgmt-AssetXaIntegration'
		]?.find(asset => asset.Name === 'Upload Speed');
		resObj.ProdPlanUploadSpeed = findPlanSpeedByDownloadSpeed(
			uploadSpeed?.Value ?? ''
		);
	}

	private processOttOli(
		oli: Wso2ServiceAccountOli,
		resObj: ServiceAccountProfile
	): void {
		if (oli.ProductName?.toLowerCase().includes('netflix')) {
			resObj.NetflixPlanName = oli.ProductName;
		}
	}

	private async processTvPackOli(
		oli: Wso2ServiceAccountOli,
		resObj: ServiceAccountProfile,
		internetPlanName: string,
		internetSpeed: string
	): Promise<void> {
		const siebelTvPackName: string = getUltimatePackSiebelTvPackName(
			internetPlanName,
			oli.ProductName ?? ''
		);
		const addOnsMetadata: SelectAddonsMetadata[] = await this.getAddOnsMetadata(
			AddOnsRequestCategoryEnum.TV_PACK
		);
		const productPartNumber: string = oli?.ProductPartNumber ?? '';
		const [tvPackCatalogue]: SelectTvPackCatalogue[] =
			await this.getTvPackCatalogue(
				siebelTvPackName,
				internetSpeed,
				productPartNumber
			);

		if (tvPackCatalogue)
			resObj.SubscribedAddOns.push({
				Name: siebelTvPackName,
				DisplayName: tvPackCatalogue.TvPackName,
				Category: AddOnsRequestCategoryEnum.TV_PACK,
				Tag: SubscribedAddOnsTagsEnum.TV_PACKS,
				Quantity: oli?.Quantity ?? 'N/A',
				AddonDate: oli?.StartDate ?? 'N/A',
				MonthlyCharge: `${tvPackCatalogue.MonthlyCommitment}`,
				ProductDetails: [],
				ContractTerm: `${tvPackCatalogue.ContractTerm}`,
				Summary: tvPackCatalogue.Summary,
				Image: tvPackCatalogue.ImageUrl,
				WarrantyPolicy: addOnsMetadata[0]?.WarrantyPolicyUrl ?? null,
				FaqUrl: addOnsMetadata[0]?.FaqUrl ?? null,
				TncUrl: addOnsMetadata[0]?.TncUrl ?? null,
				Specification: null,
				UserGuideUrl: addOnsMetadata[0]?.UserGuideUrl ?? null
			});
	}

	private async processEquipmentOli(
		resObj: ServiceAccountProfile,
		subscribedAddOns: SubscribedAddOns,
		productName: string,
		quantity: string,
		addonDate: string
	): Promise<void> {
		if (productName.toLowerCase().includes('unifi plus box')) {
			const addonName = productName.toLowerCase().includes('add on')
				? 'unifi Plus Box'
				: 'Speed Upgrade unifi Plus Box';
			resObj.SubscribedAddOns.push(
				await subscribedAddOns.generateDeviceAddonsResObj(
					addonName,
					quantity,
					addonDate
				)
			);
		}
	}

	private async processMiscellaneousChargesOli(
		resObj: ServiceAccountProfile,
		subscribedAddOns: SubscribedAddOns,
		productName: string,
		quantity: string,
		addonDate: string
	): Promise<void> {
		if (
			productName
				.replaceAll('-', '')
				.toUpperCase()
				.includes(AddOnsRequestCategoryEnum.MESH_WIFI) ||
			productName.includes('MESH Wi-Fi 6 RC') ||
			productName.includes('MESH Wi-Fi 6 OTC')
		) {
			resObj.SubscribedAddOns.push(
				await subscribedAddOns.generateDeviceAddonsResObj(
					productName,
					quantity,
					addonDate
				)
			);
		}
	}

	private async processServiceOli(
		oli: Wso2ServiceAccountOli,
		resObj: ServiceAccountProfile,
		subscribedAddOns: SubscribedAddOns,
		quantity: string,
		addonDate: string
	): Promise<void> {
		const tmAssets =
			oli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
				'TmAssetMgmt-AssetXaIntegration'
			];
		if (tmAssets) {
			for (const tmAsset of tmAssets) {
				if (tmAsset?.Value && tmAsset?.Name === 'Device Name') {
					resObj.SubscribedAddOns.push(
						await subscribedAddOns.generateDeviceAddonsResObj(
							tmAsset.Value,
							quantity,
							addonDate
						)
					);
				}
			}
		}
	}

	private async processOttListByTvPack(
		accountId: string,
		resObj: ServiceAccountProfile,
		subscribedAddOn: SubscribedAddOns
	): Promise<void> {
		const siebelTvPackName: string =
			resObj.SubscribedAddOns.find(
				addons => addons.Category === AddOnsRequestCategoryEnum.TV_PACK
			)?.Name ?? '';
		const ottListByTvPack: SubscribedOttListRes =
			await subscribedAddOn.getOttListByTvPack(
				accountId,
				siebelTvPackName,
				resObj.ProdPlanSpeed,
				resObj.NetflixPlanName
			);
		const addOnsMetadata: SelectAddonsMetadata[] = await this.getAddOnsMetadata(
			AddOnsRequestCategoryEnum.OTT
		);

		resObj.SubscribedAddOns.push({
			Name: 'Media Apps',
			DisplayName: 'Media Apps',
			Category: AddOnsRequestCategoryEnum.OTT,
			Tag: SubscribedAddOnsTagsEnum.STREAMING_APPS,
			Quantity: null,
			AddonDate: null,
			MonthlyCharge: null,
			ProductDetails: [],
			ContractTerm: null,
			Summary: null,
			Image: null,
			Specification: null,
			WarrantyPolicy: addOnsMetadata[0]?.WarrantyPolicyUrl ?? null,
			FaqUrl: addOnsMetadata[0]?.FaqUrl ?? null,
			TncUrl: addOnsMetadata[0]?.TncUrl ?? null,
			UserGuideUrl: addOnsMetadata[0]?.UserGuideUrl ?? null,
			OttDetailsList: ottListByTvPack
		});
	}

	private async getAddOnsMetadata(
		category: AddOnsRequestCategoryEnum
	): Promise<SelectAddonsMetadata[]> {
		return await this.db
			.select()
			.from(addonsMetadataTableSchema)
			.where(eq(addonsMetadataTableSchema.Category, category))
			.execute()
			.catch(err => {
				pinoLog.error(err);
				return [];
			});
	}

	private async getTvPackCatalogue(
		siebelTvPackName: string,
		internetSpeed: string,
		productPartNumber: string
	): Promise<SelectTvPackCatalogue[]> {
		return await this.db
			.select()
			.from(tvPackCatalogueTableSchema)
			.where(
				and(
					eq(tvPackCatalogueTableSchema.SiebelTvPackName, siebelTvPackName),
					eq(tvPackCatalogueTableSchema.PlanType, TvPackPlanTypeEnum.BUNDLE),
					eq(tvPackCatalogueTableSchema.PartNumber, productPartNumber),
					or(
						eq(tvPackCatalogueTableSchema.PlanSpeed, internetSpeed),
						eq(tvPackCatalogueTableSchema.PlanSpeed, 'All')
					)
				)
			)
			.execute()
			.catch(err => {
				pinoLog.error(err);
				return [];
			});
	}

	private getAnnualBillStatementsLast5Years = async (
		systemName: string,
		billingAccountNumber: string
	): Promise<AnnualBillStatements> => {
		const cacheName = `${CacheKeyEnum.ANNUAL_BILL_STATEMENT_LAST_FIVE_YEARS}-${systemName}-${billingAccountNumber}`;
		const cache = await getCache(cacheName);
		if (cache) {
			return JSON.parse(cache) as AnnualBillStatements;
		}
		const currentYear: number = new Date().getUTCFullYear();
		const annualBillStatementRes: AnnualBillStatements = [];
		for (let i = 1; i < 5; i++) {
			const wso2AnnualBillStatementReq: Wso2AnnualBillStatementReq = {
				requestHeader: {
					requestId: `${Math.random().toString(36).slice(2)}`,
					eventName:
						systemName === SystemNameEnum.NOVA
							? 'evOUnifiPortal12MthsBillNOVA'
							: 'evOUnifiPortal12MthsBillICP'
				},
				accountNo: billingAccountNumber,
				year: `${currentYear - i}`
			};

			const wso2AnnualBillStatementRes: Wso2AnnualBillStatementRes =
				await this.mwIntegration.Wso2UserIntegration.getWso2AnnualBillStatement(
					wso2AnnualBillStatementReq,
					false
				);

			const publicUrl =
				wso2AnnualBillStatementRes?.tmArchivalResponse.statement.url?.replaceAll(
					envConfig().PRIVATE_PDF_DOMAIN,
					envConfig().PUBLIC_PDF_DOMAIN
				);
			annualBillStatementRes.push({
				IsErrorFromWso2: wso2AnnualBillStatementRes === null,
				Response: {
					Year:
						wso2AnnualBillStatementRes?.tmArchivalResponse.statement.year ??
						currentYear - i,
					URL: publicUrl ?? null,
					BillDate:
						wso2AnnualBillStatementRes?.tmArchivalResponse.statement.billDate ??
						null
				}
			});
		}

		await setCache(cacheName, JSON.stringify(annualBillStatementRes), 900); // 15 minutes
		return annualBillStatementRes;
	};
}

export default BillingAccount;
