import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { SystemNameEnum } from '../../../../enum/wso2.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import { UE_ERROR } from '../../../../middleware/error';
import {
	icpBillingProfile,
	novaBillingProfile
} from '../../../../shared/common';
import type { NovaIcpBillingProfile } from '../../../../shared/schemas/api/novaBillingProfile.schema';
import AccountService from '../helpers/accountService.helper';
import type { PfaBillingAccountDetailsRes } from '../schemas/api/payForAnyone.schema';

class PayForAnyone {
	private integrationId: string;
	private accountService: AccountService;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
		this.accountService = new AccountService(this.integrationId);
	}

	async getPfaBillingAccountDetails(
		billingAccountNo: string
	): Promise<PfaBillingAccountDetailsRes> {
		const getWso2UserIntegration = new MwIntegration(this.integrationId)
			.Wso2UserIntegration;

		const wso2verified =
			await getWso2UserIntegration.getWso2BAVerification(billingAccountNo);

		if (
			wso2verified.Response.CustomerIdType &&
			wso2verified.Response.CustomerIdNumber &&
			wso2verified.Status.Type === 'OK'
		) {
			// Get the latest billing details
			const { LatestOutstandingAmount, LatestBillDueDate } =
				await this.accountService.getBillingDetails({
					idType: wso2verified.Response.CustomerIdType,
					idValue: wso2verified.Response.CustomerIdNumber,
					SystemName: wso2verified.Response.SystemName ?? '',
					BillingAccountNo: billingAccountNo
				});

			// Get the billing profile details
			const {
				AccountName,
				AccountEmail,
				AccountContactNo
			}: NovaIcpBillingProfile =
				wso2verified.Response.SystemName === SystemNameEnum.ICP
					? await icpBillingProfile(
							this.integrationId,
							wso2verified.Response.CustomerIdType,
							wso2verified.Response.CustomerIdNumber,
							billingAccountNo
						)
					: await novaBillingProfile(this.integrationId, billingAccountNo);

			return {
				Success: true,
				Code: StatusCodeEnum.OK,
				IntegrationId: this.integrationId,
				Response: {
					BillingAccountStatus:
						wso2verified.Response.RetrieveBillURLResponse.AccountStatus ??
						'N/A',
					BillingAccountNo: billingAccountNo,
					BillingAccountName: AccountName,
					BillingAccountEmail: AccountEmail,
					BillingAccountContactNo: AccountContactNo,
					SystemName: wso2verified.Response.SystemName ?? '',
					LatestBillDueDate,
					LatestOutstandingAmount
				}
			};
		}
		throw new UE_ERROR(
			"We couldn't verify the added billing account at this time. Please check your billing details or reach out to our support team for assistance.",
			StatusCodeEnum.UNPROCESSABLE_ENTITY
		);
	}
}

export default PayForAnyone;
