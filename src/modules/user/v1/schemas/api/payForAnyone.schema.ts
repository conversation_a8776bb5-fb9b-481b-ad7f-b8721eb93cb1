import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const pfaBillingAccountDetailsResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			SystemName: t.String({ example: 'NOVA' }),
			BillingAccountStatus: t.String({ example: 'Active' }),
			BillingAccountName: t.String({ example: 'John Doe' }),
			BillingAccountNo: t.String({ example: '*********' }),
			BillingAccountEmail: t.String({ example: '<EMAIL>' }),
			BillingAccountContactNo: t.String({ example: '**********' }),
			LatestBillDueDate: t.String({ example: '2024-02-10' }),
			LatestOutstandingAmount: t.String({ example: '0.00' })
		})
	},
	{
		description:
			'Account verification for pay for anyone successfully retrieved.'
	}
);

export type PfaBillingAccountDetailsRes = Static<
	typeof pfaBillingAccountDetailsResSchema
>;
