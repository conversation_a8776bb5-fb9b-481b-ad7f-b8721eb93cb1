import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const linkedAccountsProfileReqSchema = t.Object({
	Page: t.String({ default: '1' }),
	Limit: t.String({ default: '5' })
});

export type LinkedAccountsProfileReq = Static<
	typeof linkedAccountsProfileReqSchema
>;

const linkedProductsObjSchema = t.Array(
	t.Object(
		{
			ProductName: t.Optional(t.String({ examples: ['Internet'] })),
			SerialNumber: t.Optional(t.String({ examples: ['sunjae@unifi'] }))
		},
		{ description: 'The serial number of the service account for each product' }
	)
);

export type LinkedProductsObj = Static<typeof linkedProductsObjSchema>;

export const linkedAddressSchema = t.Nullable(
	t.Object({
		AddressId: t.Optional(t.String({ examples: ['1'] })),
		AddressType: t.Optional(t.String({ examples: ['LANDED'] })),
		UnitLot: t.MaybeEmpty(t.String({ examples: ['A-9-38'] })),
		FloorNo: t.Optional(t.String({ examples: ['9'] })),
		BuildingName: t.Optional(
			t.String({ examples: ['FTTH BLOK A KONDO PETAL'] })
		),
		StreetType: t.String({ examples: ['JALAN'] }),
		StreetName: t.String({ examples: ['TANJONG RAKIT 2'] }),
		Section: t.String({ examples: ['TANJONG RAKIT'] }),
		City: t.String({ examples: ['SHAH ALAM'] }),
		Postcode: t.String({ examples: ['68000'] }),
		State: t.String({ examples: ['SELANGOR'] }),
		Country: t.String({ examples: ['MALAYSIA'] })
	})
);

export type LinkedAddress = Static<typeof linkedAddressSchema>;

export const linkedBillingAccountSchema = t.Optional(
	t.Object({
		Id: t.Number({ examples: [1] }),
		AccountStatus: t.String({ examples: ['Active'] }),
		AccountEmail: t.String({ examples: ['<EMAIL>'] }),
		AccountName: t.String({ examples: ['Ryu Sun Jae'] }),
		AccountLabel: t.String({ examples: ['Sun Jae'] }),
		AccountContactNo: t.String({ examples: ['**********'] }),
		BillingAccountNo: t.String({ examples: ['**********'] }),
		EncryptedBillAccNo: t.String({
			examples: ['AQXXNlX/6xEICGVek3neU1S2TyzXe7JVa2GdVjhw=']
		}),
		LatestOutstandingAmount: t.String({ examples: ['0.00'] }),
		LatestBillDueDate: t.String({ examples: ['2024-02-10'] }),
		PaymentMethod: t.Optional(t.String({ examples: ['Credit Card'] })),
		ProdPromName: t.Optional(
			t.String({ examples: ['unifi 800Mbps - TM Staff Plan'] })
		),
		ProdPlanSpeed: t.Optional(t.String({ examples: ['800Mbps'] })),
		Products: t.Optional(linkedProductsObjSchema),
		ProductType: t.Optional(t.String()),
		ContractStartDate: t.Optional(t.String({ examples: ['-'] })),
		ContractEndDate: t.Optional(
			t.String({ examples: ['2024-10-15 00:03:13+08:00'] })
		),
		ContractTenure: t.Optional(t.String({ examples: ['2-Years'] })),
		StartDate: t.Optional(
			t.String({ examples: ['2022-10-15T00:02:51+08:00'] })
		),
		ServiceAddress: linkedAddressSchema
	})
);

export type LinkedBillingAccount = Static<typeof linkedBillingAccountSchema>;

const linkedCustomerAccountSchema = t.Object({
	SystemName: t.String({ examples: ['NOVA'] }),
	AccountStatus: t.String({ examples: ['Active'] }),
	AccountNo: t.String({ examples: ['1-PIQKKY'] }),
	AccountContactNo: t.String({ examples: ['**********'] }),
	Relationship: t.String({ examples: ['Parent'] }),
	BillingAccounts: t.Array(linkedBillingAccountSchema)
});

export type LinkedCustomerAccounts = Static<typeof linkedCustomerAccountSchema>;

export const linkedAccountsProfileResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			Page: t.Optional(t.Number({ examples: [1] })),
			Limit: t.Optional(t.Number({ examples: [5] })),
			TotalRecords: t.Optional(t.Number({ examples: [20] })),
			TotalPages: t.Optional(t.Number({ examples: [4] })),
			CustomerAccounts: t.Array(linkedCustomerAccountSchema)
		})
	},
	{
		description: 'Customer profile successfully retrieved.'
	}
);

export type LinkedAccountsProfileRes = Static<
	typeof linkedAccountsProfileResSchema
>;

export const updateLinkedAccountReqSchema = t.Object({
	AccountLabel: t.String({ example: 'Test' }),
	OwnerBillAccNo: t.String({ example: '*********' })
});

export type UpdateLinkedAccountReq = Static<
	typeof updateLinkedAccountReqSchema
>;

export const confirmAccountLinkingReqSchema = t.Object({
	OwnerBillAccNo: t.String({ example: '*********' }),
	AccountLabel: t.String({ example: 'Test' }),
	Relationship: t.String({ example: 'Parent' })
});

export type ConfirmAccountLinkingReq = Static<
	typeof confirmAccountLinkingReqSchema
>;

export const confirmAccountLinkingResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			Verification: t.Boolean(),
			Details: t.Object({
				AccountStatus: t.String({ example: 'Active' }),
				AccountLabel: t.String({ example: 'Test' }),
				OwnerName: t.String({ example: 'John Doe' }),
				OwnerBillAccNo: t.String({ example: '*********' }),
				OwnerEmail: t.String({ example: '<EMAIL>' }),
				Relationship: t.String({ example: 'Parent' }),
				SystemName: t.String({ example: 'NOVA' }),
				LatestBillDueDate: t.String({ example: '2024-02-10' }),
				LatestOutstandingAmount: t.String({ example: '0.00' }),
				ProdPlanSpeed: t.String({ example: '800Mbps' }),
				ServiceAddress: linkedAddressSchema
			})
		})
	},
	{
		description: 'Account verification successfully retrieved.'
	}
);

export type ConfirmAccountLinkingRes = Static<
	typeof confirmAccountLinkingResSchema
>;

export const linkAccountReqSchema = t.Object({
	AccountLabel: t.String({ example: 'Test' }),
	Relationship: t.String({ example: 'Parent' }),
	NonOwnerCredentialValue: t.String({ examples: ['9**********2@y******.com'] }),
	OwnerBillAccNo: t.String({ example: '*********' }),
	OwnerEmail: t.String({ example: '<EMAIL>' })
});
export type LinkAccountReq = Static<typeof linkAccountReqSchema>;

export const deleteLinkedAccountReqSchema = t.Object({
	OwnerBillAccNo: t.String({ example: '*********', minLength: 1 })
});

export type DeleteLinkedAccountReq = Static<
	typeof deleteLinkedAccountReqSchema
>;

export const linkedAccountResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			Action: t.String({ example: 'CREATED' }),
			Message: t.String({ example: 'Successfully Created' })
		})
	},
	{
		description: 'Link account label successfully updated.'
	}
);

export type LinkedAccountRes = Static<typeof linkedAccountResSchema>;

export const linkedAccountsNonOwnersReqSchema = t.Object({
	Page: t.String({ default: '1' }),
	Limit: t.String({ default: '5' })
});

export type LinkedAccountsNonOwnersReq = Static<
	typeof linkedAccountsNonOwnersReqSchema
>;

const nonOwnersSchema = t.Object({
	Id: t.Number({ examples: ['1'] }),
	SystemName: t.String({ examples: ['NOVA'] }),
	CredentialValue: t.String({ examples: ['**********'] }),
	EncryptedCredentialValue: t.String({ examples: ['1-PIQKKY'] }),
	Relationship: t.String({ examples: ['Child'] }),
	BillingAccountNo: t.String({ examples: ['1-PIQKKY'] }),
	EncryptedBillAccNo: t.String({ examples: ['1-PIQKKY'] })
});

export const linkedAccountsNonOwnersResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			Page: t.Optional(t.Number({ examples: [1] })),
			Limit: t.Optional(t.Number({ examples: [5] })),
			TotalRecords: t.Optional(t.Number({ examples: [20] })),
			TotalPages: t.Optional(t.Number({ examples: [4] })),
			NonOwners: t.Array(nonOwnersSchema)
		})
	},
	{
		description: 'Non owner accounts successfully retrieved.'
	}
);

export type LinkedAccountsNonOwnersRes = Static<
	typeof linkedAccountsNonOwnersResSchema
>;

export const deleteNonOwnerReqSchema = t.Object({
	Id: t.Number({ example: 1 }),
	EncryptedBillAccNo: t.String({
		example: 'AQXXNlX/6xEICGVek3neU1S2TyzXe7JVa2GdVjhw='
	}),
	EncryptedCredentialValue: t.String({
		example: 'AQXXNlX/6xEICGVek3neU1S2TyzXe7JVa2GdVjhw='
	})
});

export type DeleteNonOwnerReq = Static<typeof deleteNonOwnerReqSchema>;

export const deleteNonOwnerResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			Action: t.String({ example: 'DELETED' }),
			Message: t.String({ example: 'Successfully deleted' })
		})
	},
	{
		description: 'Shared account successfully deleted.'
	}
);

export type DeleteNonOwnerRes = Static<typeof deleteNonOwnerResSchema>;
