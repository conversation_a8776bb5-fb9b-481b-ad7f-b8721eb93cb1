import { type Static, t } from 'elysia';
import { KciBillingCategoryEnum } from '../../../../../enum/billing.enum';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

/* 
############################################################################
#####KCI Billing Info###################################################
############################################################################
*/

export const kciBillingInfoReqSchema = t.Object({
	BillingAccountNo: t.String({ example: '*********', minLength: 1 }),
	BillMonth: t.String({ example: 'September', minLength: 1 }),
	BillHash: t.String({ example: '12345', minLength: 5 }),
	Category: t.Enum(KciBillingCategoryEnum, {
		example: KciBillingCategoryEnum.ADVANCE_PAYMENT
	})
});

export type KCIBillingInfoReq = Static<typeof kciBillingInfoReqSchema>;

export const kciBillingInfoResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			BillingAccountNo: t.String({ example: '*********' }),
			BillingAccountName: t.String({ example: 'John Doe' }),
			IdType: t.String({ example: 'New NRIC' }),
			IdValue: t.String({ example: '999999-99-9999' }),
			SystemName: t.String({ example: 'NOVA' }),
			AmountToBePaid: t.String({ example: '0.00' }),
			BillNo: t.String({ example: '1001' }),
			BillDate: t.String({ example: '2024-02-10' }),
			BillDueDate: t.String({ example: '2024-02-10' }),
			TotalCurrent: t.String({ example: '0.00' }),
			TotalPrevious: t.String({ example: '0.00' }),
			TotalPreviousPayment: t.String({ example: '0.00' }),
			TotalPrevAdj: t.String({ example: '0.00' }),
			TotalOutstanding: t.String({ example: '0.00' }),
			TotalDue: t.String({ example: '0.00' }),
			OverduePayment: t.String({ example: '0.00' }),
			PromiseToPayDate: t.String({ example: '0.00' })
		})
	},
	{
		description: 'KCI billing information successfully retrieved.'
	}
);

export type KCIBillingInfoRes = Static<typeof kciBillingInfoResSchema>;
