import { type Static, t } from 'elysia';
import { BillingProfileUpdateEnum } from '../../../../../enum/billing.enum';
import { SystemNameEnum } from '../../../../../enum/wso2.enum';
import {
	wso2BillingHistorySchema,
	wso2CreditUtilizationSchema,
	wso2LightweightBillingDetailsResObjSchema,
	wso2PaymentHistorySchema
} from '../../../../../integration/wso2/user/schemas/api/wso2LightweightBillingDetails.schema';
import { wso2BillingAccountsResObjSchema } from '../../../../../integration/wso2/user/schemas/api/wso2NovaBillingProfile.schema';
import { wso2NovaAddressObjSchema } from '../../../../../integration/wso2/user/schemas/api/wso2UpdateBillingProfile.schema';
import { wso2GetIbillBillingDetailsResSchema } from '../../../../../integration/wso2/user/schemas/api/wso2iBillBillingDetails.schema';
import { novaAccountBillingAddressSchema } from '../../../../../shared/schemas/api/novaBillingProfile.schema';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

/* 
############################################################################
#####Account Settings######################################################
############################################################################
*/

export const accountSettingsReqSchema = t.Object({
	EncryptedBillAccNo: t.String({ minLength: 1 })
});

export type AccountSettingsReq = Static<typeof accountSettingsReqSchema>;

export const accountSettingsResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Array(
			t.Object({
				AccountLabel: t.Nullable(t.String({ example: 'Test' })),
				BillingAccountNo: t.String({ example: '*********' }),
				SystemName: t.String({ example: 'NOVA' }),
				Email: t.String({ example: '<EMAIL>' }),
				IdType: t.String({ example: 'New NRIC' }),
				IdValue: t.String({ example: '999999-99-9999' }),
				QuickLinks: t.Nullable(t.String({ example: 'Favourites, Profile' })),
				CreatedAt: t.Date(),
				UpdatedAt: t.Date()
			})
		)
	},
	{
		description: 'Account settings successfully retrieved.'
	}
);

export type AccountSettingsRes = Static<typeof accountSettingsResSchema>;

export const quickLinksSchema = t.Object({
	Title: t.String({ example: 'Favourites' }),
	Active: t.Boolean()
});

export type QuickLinks = Static<typeof quickLinksSchema>;

export const updateQuickLinksReqSchema = t.Object({
	EncryptedBillAccNo: t.String({
		example: 'AQXXNlX/6xEICGVek3neU1S2TyzXe7JVa2GdVjhw='
	}),
	SystemName: t.Enum(SystemNameEnum, { example: SystemNameEnum.NOVA }),
	Email: t.String({ example: '<EMAIL>' }),
	QuickLinks: t.Array(quickLinksSchema)
});
export type UpdateQuickLinksReq = Static<typeof updateQuickLinksReqSchema>;

export const updateAccountLabelReqSchema = t.Object({
	AccountLabel: t.String({ example: 'Test' }),
	EncryptedBillAccNo: t.String({
		example: 'AQXXNlX/6xEICGVek3neU1S2TyzXe7JVa2GdVjhw='
	}),
	SystemName: t.Enum(SystemNameEnum, { example: SystemNameEnum.NOVA }),
	Email: t.String({ example: '<EMAIL>' })
});

export type UpdateAccountLabelReq = Static<typeof updateAccountLabelReqSchema>;

/* 
############################################################################
#####Bill History###########################################################
############################################################################
*/

export const billingHistoryReqSchema = t.Object({
	EncryptedBillAccNo: t.String({ minLength: 1 }),
	SystemName: t.Enum(SystemNameEnum)
});

export type BillingHistoryReq = Static<typeof billingHistoryReqSchema>;

const billingTrendSchema = t.Nullable(
	t.Object({
		BillDate: t.MaybeEmpty(t.String({ examples: ['2024-01-01'] })),
		TotalDue: t.MaybeEmpty(t.Number({ examples: [100.0] })),
		TotalPaid: t.MaybeEmpty(t.Number({ examples: [100.0] }))
	})
);

export type BillingTrend = Static<typeof billingTrendSchema>;

export const lightweightBillHistoryResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			BillingTrend: t.Array(billingTrendSchema),
			Wso2Response: wso2LightweightBillingDetailsResObjSchema
		})
	},
	{
		description:
			'Customer bill history retrieved successfully. The response of this API is directly from WSO2 and is cached for 15 minutes.'
	}
);

export type LightweightBillHistoryRes = Static<
	typeof lightweightBillHistoryResSchema
>;

/* 
############################################################################
#####Annual Bill Statements###############################################
############################################################################
*/

export const annualBillStatementReqSchema = t.Object({
	EncryptedBillAccNo: t.String({
		example: 'AQXXNlX/6xEICGVek3neU1S2TyzXe7JVa2GdVjhw=',
		description: 'The encrypted billing account number',
		minLength: 1
	}),
	Year: t.String({
		example: '2024',
		description: 'The year of the bill statement',
		minLength: 1
	}),
	SystemName: t.Enum(SystemNameEnum)
});

export type AnnualBillStatementReq = Static<
	typeof annualBillStatementReqSchema
>;

export const annualBillStatementResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			Year: t.MaybeEmpty(t.Number({ examples: ['2023'] })),
			BillDate: t.MaybeEmpty(t.String({ examples: ['2023-10-25+08:00'] })),
			URL: t.MaybeEmpty(
				t.String({
					description: 'The URL of the annual bill statement in PDF format',
					examples: ['https://www.example.com/billing/2023/01/18/**********']
				})
			)
		})
	},
	{
		description: 'Annual bill statement successfully retrieved.'
	}
);

export type AnnualBillStatementRes = Static<
	typeof annualBillStatementResSchema
>;

/* 
############################################################################
#####Nova Billing Profile for Autopay usage#################################
############################################################################
*/

export const novaBillingProfileReqSchema = t.Object({
	EncryptedBillAccNo: t.String({ minLength: 1 })
});

export type NovaBillingProfileReq = Static<typeof novaBillingProfileReqSchema>;

export const novaBillingProfileResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: wso2BillingAccountsResObjSchema
	},
	{
		description: 'Nova customer billing profile successfully retrieved.'
	}
);

export type NovaBillingProfileRes = Static<typeof novaBillingProfileResSchema>;

/* 
############################################################################
#####Update Billing Profile###############################################
############################################################################
*/

export const updateBillingProfileReqSchema = t.Object({
	EncryptedBillAccNo: t.String({
		example: 'AQXXNlX/6xEICGVek3neU1S2TyzXe7JVa2GdVjhw='
	}),
	BillingProfileId: t.String({
		example: '1-PIQKKY',
		description:
			'The billing profile ID can be found in the response of the /accounts/summary endpoint (CustomerAccounts -> AccountNo).'
	}),
	SystemName: t.Enum(SystemNameEnum, { example: SystemNameEnum.NOVA }),
	UpdateType: t.Enum(BillingProfileUpdateEnum, {
		example: BillingProfileUpdateEnum.ADDRESS
	}),
	UpdateProfile: t.Object({
		Email: t.Optional(
			t.String({
				examples: ['<EMAIL>'],
				description: 'The new billing email address'
			})
		),
		MobileNo: t.Optional(
			t.String({
				examples: ['+************'],
				description: 'The new billing mobile number'
			})
		),
		Address: t.Optional(wso2NovaAddressObjSchema)
	})
});

export type UpdateBillingProfileReq = Static<
	typeof updateBillingProfileReqSchema
>;

export const updateBillingProfileResSchema = baseResponseSchema;

export type UpdateBillingProfileRes = Static<
	typeof updateBillingProfileResSchema
>;

/* 
############################################################################
#####iBill Billing Details###############################################
############################################################################
*/

export const ibillDetailsReqSchema = t.Object({
	EncryptedBillAccNo: t.String({ minLength: 1 }),
	BillDate: t.String({
		minLength: 1,
		example: '2024-01-01',
		description:
			'The bill date must be from billing history which you can get from /user/billing/history or /user/billing/profile endpoint.'
	})
});

export type IbillBillingDetailsReq = Static<typeof ibillDetailsReqSchema>;

export const ibillDetailsResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		...wso2GetIbillBillingDetailsResSchema.properties
	},
	{
		description: 'iBill billing information successfully retrieved.'
	}
);

export type IbillBillingDetailsRes = Static<typeof ibillDetailsResSchema>;

/* 
############################################################################
#####Billing Profile#######################################################
############################################################################
*/

export const billingProfileReqSchema = t.Object({
	EncryptedBillAccNo: t.String({
		example: 'AQXXNlX/6xEICGVek3neU1S2TyzXe7JVa2GdVjhw=',
		minLength: 1
	})
});

export type BillingProfileReq = Static<typeof billingProfileReqSchema>;

const subscribedOttListSchema = t.Array(
	t.Object({
		OttPlanType: t.String({ examples: ['Bundle'] }),
		OttPlanId: t.String({ examples: ['P2'] }),
		OttPlanName: t.String({ examples: ['Ultimate Pack 2'] }),
		OttIsActive: t.Boolean(),
		OttTxnId: t.Number({ examples: [1331631] }),
		OttStartDate: t.Optional(
			t.String({
				examples: ['09/20/2023 03:12:16 PM'],
				description: 'The start date of the OTT plan'
			})
		),
		OttExpiryDate: t.Optional(
			t.String({
				examples: ['09/20/2023 03:12:16 PM'],
				description: 'The expiry date of the OTT plan'
			})
		),
		OttStatus: t.Optional(t.String({ examples: ['Active'] })),
		OttUserId: t.Optional(t.String({ examples: ['sunjae@unifi'] })),
		SwapAvailable: t.Optional(t.String({ examples: ['no'] })),
		Swapped: t.Optional(t.String({ examples: ['no'] })),
		SwappedOttTxnId: t.Optional(t.Number({ examples: [0] })),
		SwapMode: t.Optional(t.String({ examples: ['Switch'] })),
		SwapOttExpiryDate: t.Optional(
			t.String({
				examples: ['11/24/2022 12:46:48 PM'],
				description: 'The expiry date of the OTT plan'
			})
		),
		SwapOttStartDate: t.Optional(
			t.String({
				examples: ['11/24/2022 12:46:48 PM'],
				description: 'The start date of the OTT plan'
			})
		),
		OttName: t.String({ examples: ['MangoTV'] }),
		OttMerchantId: t.Number({ examples: [49] }),
		OttProductId: t.String({ examples: ['100128'] }),
		OttOmgId: t.Number({ examples: ['105'] }),
		OttUniversalLink: t.String({
			examples: ['https://mangotv.com'],
			description: 'The universal link of the OTT plan'
		}),
		OttIconPath: t.String({
			examples: ['https://activate.unifi.com.my/ott/lib/logo/mangotv.png'],
			description: 'The icon path of the OTT plan'
		}),
		OttLoginType: t.String({ examples: ['mobile'] }),
		OttLoginInstruction: t.String({
			examples: ['Click VIP, then click SMS icon and use credentials above.'],
			description: 'The login instruction of the OTT plan'
		}),
		OttVerificationInstruction: t.String({
			examples: ['Please enter the code sent to your mobile phone.'],
			description: 'The verification instruction of the OTT plan'
		}),
		OttActivationLink: t.String({
			examples: ['/v1/ott/mango/request-activation-url'],
			description: 'The activation link of the OTT plan'
		}),
		OttSequence: t.Number({ examples: ['1'] }),
		OttPrice: t.Number({ examples: [0] }),
		OttPlanSwapGroup: t.Nullable(t.String({ examples: ['Null'] })),
		OttActivateStatus: t.String({ examples: ['Watch'] }),
		OttActivateType: t.String({ examples: ['Group'] }),
		ShowChangePlan: t.Boolean({ examples: [true] }),
		AllowDisneyUpgrade: t.String({ examples: ['True'] }),
		AllowMaxUpgrade: t.String({ examples: ['True'] }),
		NetflixTxnId: t.String({ examples: ['**********'] }),
		AllowNetflixCancel: t.String({ examples: ['no'] }),
		OttDescription: t.Optional(t.String()),
		OttPackageType: t.Optional(t.String()),
		OttPackageDetails: t.Optional(t.Array(t.String())),
		OttPackageDuration: t.Optional(t.String())
	})
);

export type SubscribedOttList = Static<typeof subscribedOttListSchema>;

const subscribedOttListResSchema = t.Object({
	IsErrorFromOmg: t.Boolean(),
	OttSelectionCustChoice: subscribedOttListSchema,
	OttSelectionFixed: subscribedOttListSchema,
	OttAlaCarte: subscribedOttListSchema,
	OttSelectionNetflix: subscribedOttListSchema
});

export type SubscribedOttListRes = Static<typeof subscribedOttListResSchema>;

export const annualBillStatementsSchema = t.Array(
	t.Object({
		IsErrorFromWso2: t.Boolean(),
		Response: t.Object({
			Year: t.MaybeEmpty(t.Number({ examples: [2023] })),
			BillDate: t.MaybeEmpty(t.String({ examples: ['2024-01-18+08:00'] })),
			URL: t.MaybeEmpty(
				t.String({
					examples: ['https://www.example.com/billing/2023/01/18/**********']
				})
			)
		})
	})
);

export type AnnualBillStatements = Static<typeof annualBillStatementsSchema>;

const saProductsObjSchema = t.Array(
	t.Object(
		{
			ProductName: t.Optional(t.String({ examples: ['Internet'] })),
			SerialNumber: t.Optional(t.String({ examples: ['sunjae@unifi'] }))
		},
		{ description: 'The serial number of the service account for each product' }
	)
);

export type SAProductsObj = Static<typeof saProductsObjSchema>;

const subscribedAddonsObjSchema = t.Object({
	Name: t.Optional(t.String({ examples: ['Speed Upgrade unifi Plus Box'] })),
	DisplayName: t.String({ examples: ['Free Unifi TV Box (Promo)'] }),
	Category: t.Nullable(t.String({ examples: ['UPB RM0'] })),
	Tag: t.String({ examples: ['Devices'] }),
	Quantity: t.Nullable(t.String({ examples: ['1'] })),
	AddonDate: t.Nullable(t.String({ examples: ['2020-06-20 00:00:00'] })),
	MonthlyCharge: t.Nullable(t.String({ examples: ['0'] })),
	ProductDetails: t.Optional(
		t.Array(
			t.String({
				examples: [
					'4K Android TV Box',
					' Android 9 OS',
					' 2GB RAM',
					' Open access to Google Play Store'
				]
			})
		)
	),
	ContractTerm: t.Nullable(t.String({ examples: ['24'] })),
	Summary: t.Nullable(
		t.String({
			examples: ['Elevate your home entertainment experience with Unifi TV Box']
		})
	),
	Image: t.Nullable(
		t.String({
			examples: [
				'https://unifi.com.my/sites/default/files/page/assets/images/mobileunifi/addon/plus-box.jpg'
			]
		})
	),
	Specification: t.Nullable(
		t.String({
			examples: [
				'https://unifi.com.my/sites/default/files/page/assets/documents/mesh%20wifi/unifi%20Plus%20Box%20%E2%80%93%20Product%20Specification.pdf'
			]
		})
	),
	WarrantyPolicy: t.Nullable(
		t.String({
			examples: [
				'https://unifi.com.my/sites/default/files/page/assets/documents/mesh%20wifi/WARRANTY%20POLICY%20FOR%20UNIFI%20PLUS%20BOX_v2.pdf'
			]
		})
	),
	FaqUrl: t.Nullable(
		t.String({
			examples: [
				'https://unifi.com.my/sites/default/files/page/assets/pdf/fibre/FAQ-unifi-FREE-uPB-for-Speed-Upgrade_Final.pdf'
			]
		})
	),
	TncUrl: t.Nullable(
		t.String({
			examples: [
				'https://unifi.com.my/sites/default/files/page/assets/pdf/fibre/TnCs-Free-uPB_Final.pdf'
			]
		})
	),
	UserGuideUrl: t.Nullable(t.String({ examples: ['N/A'] })),
	OttDetailsList: t.Optional(subscribedOttListResSchema)
});

export type SubscribedAddonsObj = Static<typeof subscribedAddonsObjSchema>;

const serviceAccountProfileSchema = t.Optional(
	t.Object({
		Status: t.Optional(t.String({ examples: ['Active'] })),
		ExpDiscFlg: t.Optional(t.String({ examples: ['N'] })),
		UnitLot: t.Optional(t.String({ examples: ['A-9-38'] })),
		FloorNo: t.Optional(t.String({ examples: ['9'] })),
		BuildingName: t.Optional(
			t.String({ examples: ['FTTH BLOK A KONDO PETAL'] })
		),
		StreetType: t.Optional(t.String({ examples: [''] })),
		StreetName: t.Optional(t.String({ examples: ['TANJONG RAKIT 2'] })),
		Section: t.Optional(t.String({ examples: ['TANJONG RAKIT'] })),
		Postcode: t.Optional(t.String({ examples: ['68000'] })),
		City: t.Optional(t.String({ examples: ['SHAH ALAM'] })),
		State: t.Optional(t.String({ examples: ['SELANGOR'] })),
		Country: t.Optional(t.String({ examples: ['MALAYSIA'] })),
		Products: saProductsObjSchema,
		ProductType: t.Optional(t.String({ examples: ['FIX'] })),
		ContractStartDate: t.Optional(t.String({ examples: ['-'] })),
		ContractEndDate: t.Optional(
			t.String({ examples: ['2024-10-15 00:03:13+08:00'] })
		),
		ContractTenure: t.Optional(t.String({ examples: ['2-Years'] })),
		ServiceID: t.String({ examples: ['abc@unifi'] }),
		NetflixPlanName: t.Nullable(
			t.String({ examples: ['Netflix Basic Complementary'] })
		),
		SubscribedAddOns: t.Array(subscribedAddonsObjSchema),
		ProdPromName: t.Optional(
			t.String({ examples: ['unifi 800Mbps - TM Staff Plan'] })
		),
		ProdPlanSpeed: t.String({ examples: ['800Mbps'] }),
		ProdPlanUploadSpeed: t.String({ examples: ['200Mbps'] }),
		StartDate: t.Optional(t.String({ examples: ['2022-10-15T00:02:51+08:00'] }))
	})
);

export type ServiceAccountProfile = Static<typeof serviceAccountProfileSchema>;

export const serviceAccountObjSchema = t.Object({
	IsErrorFromWso2: t.Boolean(),
	Response: t.Array(serviceAccountProfileSchema)
});

export type ServiceAccountObj = Static<typeof serviceAccountObjSchema>;

export const billingDetailsObjSchema = t.Object({
	IsErrorFromWso2: t.Boolean(),
	Response: t.Object({
		BillingHistory: wso2BillingHistorySchema,
		CreditUtilization: wso2CreditUtilizationSchema,
		PaymentHistory: wso2PaymentHistorySchema
	})
});

export type BillingDetailsObj = Static<typeof billingDetailsObjSchema>;

export const billingProfileObjSchema = t.Nullable(
	t.Object({
		AccountStatus: t.String({ examples: ['Active'] }),
		AccountNo: t.String({ examples: ['0**********'] }),
		AccountEmail: t.String({ examples: ['<EMAIL>'] }),
		AccountName: t.String({ examples: ['Ryu Sun Jae'] }),
		AccountContactNo: t.String({ examples: ['**********'] }),
		AccountLabel: t.String({ examples: ['Sun Jae'] }),
		AccountAddress: novaAccountBillingAddressSchema,
		LatestOutstandingAmount: t.String({ examples: ['0.00'] }),
		LatestBillDueDate: t.String({ examples: ['2024-02-10'] }),
		PaymentMethod: t.String({ examples: ['Credit Card'] }),
		ServiceAccount: serviceAccountObjSchema,
		BillingDetails: billingDetailsObjSchema,
		AnnualBillStatements: annualBillStatementsSchema
	})
);

export type BillingProfileObj = Static<typeof billingProfileObjSchema>;

export const billingProfileResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			BillingAccount: billingProfileObjSchema
		})
	},
	{
		description: 'Customer billing profile successfully retrieved.'
	}
);

export type BillingProfileRes = Static<typeof billingProfileResSchema>;

/* 
############################################################################
#####Expiry Discount########################################################
############################################################################
*/

export const expDiscountReqSchema = t.Object({
	EncryptedBillAccNo: t.String({
		examples: ['AQXXNlX/6xEICGVek3neU1S2TyzXe7JVa2GdVjhw=']
	}),
	Products: saProductsObjSchema
});

export type ExpDiscountReq = Static<typeof expDiscountReqSchema>;

const expDiscountResObjSchema = t.Array(
	t.Object({
		EncryptedBillAccNo: t.String({
			examples: ['AQXXNlX/6xEICGVek3neU1S2TyzXe7JVa2GdVjhw=']
		}),
		Login: t.String({ examples: ['sunjae@unifi'] }),
		ServiceId: t.String({ examples: ['Residential High Speed Internet'] }),
		BundleName: t.Nullable(
			t.String({
				examples: ['unifi MSAP advance plus 500Mbps Discount Waiver me']
			})
		),
		DiscountName: t.String({
			examples: ['unifi MSAP advance plus 500Mbps Discount Waiver me']
		}),
		DiscountAmount: t.String({ examples: ['-159.0'] }),
		CreatedDate: t.String({ examples: ['16-02-2024 17:13:48'] }),
		EffectiveDate: t.String({ examples: ['16-02-2024 00:00:00'] }),
		EndDate: t.String({ examples: ['16-08-2024 00:00:00'] }),
		DiscountValidity: t.String({ examples: ['6'] }),
		DayToExp: t.String({ examples: ['7'] })
	})
);
export type ExpDiscountResObj = Static<typeof expDiscountResObjSchema>;

export const expDiscountResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: expDiscountResObjSchema
	},
	{
		description: 'Expiry discount details successfully retrieved.'
	}
);

export type ExpDiscountRes = Static<typeof expDiscountResSchema>;
