import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const profileAccountsReqSchema = t.Object({
	Page: t.String({ default: '1' }),
	Limit: t.String({ default: '2' })
});

export type ProfileAccountsReq = Static<typeof profileAccountsReqSchema>;

export const lightweightAddressSchema = t.Nullable(
	t.Object({
		AddressId: t.Optional(t.String({ examples: ['1'] })),
		AddressType: t.Optional(t.String({ examples: ['LANDED'] })),
		UnitLot: t.MaybeEmpty(t.String({ examples: ['A-9-38'] })),
		FloorNo: t.Optional(t.String({ examples: ['9'] })),
		BuildingName: t.Optional(
			t.String({ examples: ['FTTH BLOK A KONDO PETAL'] })
		),
		StreetType: t.String({ examples: ['JALAN'] }),
		StreetName: t.String({ examples: ['TANJONG RAKIT 2'] }),
		Section: t.String({ examples: ['TANJONG RAKIT'] }),
		City: t.String({ examples: ['SHAH ALAM'] }),
		Postcode: t.String({ examples: ['68000'] }),
		State: t.String({ examples: ['SELANGOR'] }),
		Country: t.String({ examples: ['MALAYSIA'] })
	})
);

export type LightweightAddress = Static<typeof lightweightAddressSchema>;

const saProductsObjSchema = t.Array(
	t.Object(
		{
			ProductName: t.Optional(t.String({ examples: ['Internet'] })),
			SerialNumber: t.Optional(t.String({ examples: ['sunjae@unifi'] }))
		},
		{ description: 'The serial number of the service account for each product' }
	)
);

export type SAProductsObj = Static<typeof saProductsObjSchema>;

export const lightweightBillingAccountSchema = t.Optional(
	t.Object({
		AccountStatus: t.String({ examples: ['Active'] }),
		AccountEmail: t.String({ examples: ['<EMAIL>'] }),
		AccountName: t.String({ examples: ['Ryu Sun Jae'] }),
		AccountLabel: t.String({ examples: ['Sun Jae'] }),
		AccountContactNo: t.String({ examples: ['**********'] }),
		BillingAccountNo: t.String({ examples: ['**********'] }),
		EncryptedBillAccNo: t.String({
			examples: ['AQXXNlX/6xEICGVek3neU1S2TyzXe7JVa2GdVjhw=']
		}),
		LatestOutstandingAmount: t.String({ examples: ['0.00'] }),
		LatestBillDueDate: t.String({ examples: ['2024-02-10'] }),
		PaymentMethod: t.Optional(t.String({ examples: ['Credit Card'] })),
		ProdPromName: t.Optional(
			t.String({ examples: ['unifi 800Mbps - TM Staff Plan'] })
		),
		ProdPlanSpeed: t.Optional(t.String({ examples: ['800Mbps'] })),
		Products: t.Optional(saProductsObjSchema),
		ProductType: t.Optional(t.String()),
		ContractStartDate: t.Optional(t.String({ examples: ['-'] })),
		ContractEndDate: t.Optional(
			t.String({ examples: ['2024-10-15 00:03:13+08:00'] })
		),
		ContractTenure: t.Optional(t.String({ examples: ['2-Years'] })),
		StartDate: t.Optional(
			t.String({ examples: ['2022-10-15T00:02:51+08:00'] })
		),
		ServiceAddress: lightweightAddressSchema
	})
);

export type LightweightBillingAccount = Static<
	typeof lightweightBillingAccountSchema
>;

const customerAccountsSchema = t.Object({
	SystemName: t.String({ examples: ['NOVA'] }),
	AccountStatus: t.String({ examples: ['Active'] }),
	AccountNo: t.String({ examples: ['1-PIQKKY'] }),
	AccountContactNo: t.String({ examples: ['**********'] }),
	AccountEmail: t.String({ examples: [''] }),
	ContactId: t.String({ examples: ['1-PIQKKY'] }),
	Relationship: t.Optional(t.String({ examples: ['Parent'] })),
	BillingAccounts: t.Array(lightweightBillingAccountSchema)
});

export type CustomerAccounts = Static<typeof customerAccountsSchema>;

export const profileAccountsResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			Page: t.Optional(t.Number({ examples: [1] })),
			Limit: t.Optional(t.Number({ examples: [5] })),
			TotalRecords: t.Optional(t.Number({ examples: [20] })),
			TotalPages: t.Optional(t.Number({ examples: [4] })),
			CustomerAccounts: t.Array(customerAccountsSchema)
		})
	},
	{
		description: 'Customer profile successfully retrieved.'
	}
);

export type ProfileAccountsRes = Static<typeof profileAccountsResSchema>;

/* 
############################################################################
#####Profile Accounts Address Details#######################################
############################################################################
*/

const billingAddressSchema = t.Nullable(
	t.Object({
		BillingAddress1: t.String(),
		BillingAddress2: t.String(),
		City: t.String(),
		Country: t.String(),
		Postcode: t.String(),
		State: t.String()
	})
);

export type BillingAddress = Static<typeof billingAddressSchema>;

const addressDetailsSchema = t.Object({
	Name: t.String({ example: 'John Doe' }),
	ContactNo: t.String({ example: '**********' }),
	Email: t.String({ example: '<EMAIL>' }),
	ServiceId: t.String({ example: 'johndoe@unifi' }),
	EncryptedCustomerAccountNo: t.String({
		example: 'AQXXNlX/6xEICGVek3neU1S2TyzXe7JVa2GdVjhw='
	}),
	EncryptedBillingAccountNo: t.String({
		example: 'AQXXNlX/6xEICGVek3neU1S2TyzXe7JVa2GdVjhw='
	}),
	ContactId: t.Nullable(t.String({ example: '1-PIQKKY' })),
	ServicePointId: t.Nullable(t.String({ example: '' })),
	TmDPLocation: t.Nullable(t.String({ example: '' })),
	TmPremiseType: t.Nullable(t.String({ example: '' })),
	TmExchangeName: t.Nullable(t.String({ example: '' })),
	TmAccessTechnology: t.Nullable(t.String({ example: 'FTTH' })),
	PlanName: t.Nullable(t.String({ example: 'unifi 800Mbps - TM Staff Plan' })),
	ProductName: t.Nullable(t.String({ example: 'Internet' })),
	ProductPartNumber: t.Nullable(t.String({ example: '1-PZQKKY' })),
	DownloadSpeedInMbps: t.Nullable(t.Number({ example: '800' })),
	DeliveryAddress: t.Object({
		AddressId: t.String({ example: 'G12345' }),
		UnitNo: t.String({ example: '14' }),
		FloorNo: t.String({ example: '2' }),
		BuildingName: t.String({ example: 'Bangunan Anggun' }),
		StreetType: t.String({ example: 'JALAN' }),
		StreetName: t.String({ example: 'Jalan 5/8A' }),
		Section: t.String({ example: 'Seksyen 5' }),
		City: t.String({ example: 'Kajang' }),
		Postcode: t.String({ example: '43000' }),
		State: t.String({ example: 'Selangor' }),
		Country: t.String({ example: 'Malaysia' })
	}),
	BillingAddress: billingAddressSchema
});
export type AddressDetails = Static<typeof addressDetailsSchema>;

export const addressDetailsResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Nullable(t.Array(addressDetailsSchema))
	},
	{
		description: 'Address details successfully retrieved.'
	}
);

export type AddressDetailsRes = Static<typeof addressDetailsResSchema>;
