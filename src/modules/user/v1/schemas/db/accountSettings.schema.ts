import { integer, pgTable, text, timestamp } from 'drizzle-orm/pg-core';

export const accountSettingsTableSchema = pgTable('account_settings', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	AccountLabel: text('account_label'),
	BillingAccountNo: text('billing_account_no').notNull(),
	SystemName: text('system_name').notNull(),
	Email: text('email').notNull(),
	IdType: text('id_type').notNull(),
	IdValue: text('id_value').notNull(),
	QuickLinks: text('quick_links'),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectAccountSettings =
	typeof accountSettingsTableSchema.$inferSelect;
