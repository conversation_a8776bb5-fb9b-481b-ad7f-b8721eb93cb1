import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import { Ely<PERSON> } from 'elysia';
import { getIdTokenInfo } from '../../../../middleware/uaid/util/utils';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type ConfirmAccountLinkingRes,
	type DeleteNonOwnerRes,
	type LinkedAccountRes,
	type LinkedAccountsNonOwnersRes,
	type LinkedAccountsProfileRes,
	confirmAccountLinkingReqSchema,
	confirmAccountLinkingResSchema,
	deleteLinkedAccountReqSchema,
	deleteNonOwnerReqSchema,
	deleteNonOwnerResSchema,
	linkAccountReqSchema,
	linkedAccountResSchema,
	linkedAccountsNonOwnersReqSchema,
	linkedAccountsNonOwnersResSchema,
	linkedAccountsProfileReqSchema,
	linkedAccountsProfileResSchema,
	updateLinkedAccountReqSchema
} from '../schemas/api/linkedAccount.schema';
import LinkedAccount from '../services/linkedAccount.service';

const linkedAccountV1Routes = new Elysia()
	.use(bearer())
	.resolve(async ctx => {
		const idTokenInfo = await getIdTokenInfo(ctx.bearer);
		return {
			LinkedAccount: new LinkedAccount(randomUUID(), idTokenInfo)
		};
	})
	.post(
		'/linked-account/confirmation',
		async (ctx): Promise<ConfirmAccountLinkingRes> => {
			return await ctx.LinkedAccount.confirmAccountLinking(ctx.body);
		},
		{
			detail: {
				description:
					'Verify a to-be-linked account to ensure its existence and confirm the account label, billing account number, name, email, and relationship.<br><br><b>Backend System:</b> NOVA SIEBEL',
				tags: ['User']
			},
			body: confirmAccountLinkingReqSchema,
			response: {
				200: confirmAccountLinkingResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.get(
		'/linked-accounts/profile',
		async (ctx): Promise<LinkedAccountsProfileRes> => {
			return await ctx.LinkedAccount.getLinkedAccountProfileByPage(ctx.query);
		},
		{
			query: linkedAccountsProfileReqSchema,
			response: {
				200: linkedAccountsProfileResSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Retrieve a list of linked accounts for the service dashboard, with personal information (PII) masked and a response similar to the /accounts/profile API.<br><br><b>Backend System:</b> Redis, NOVA SIEBEL<br><b>Table:</b> linked_accounts',
				tags: ['User']
			}
		}
	)
	.post(
		'/linked-account',
		async (ctx): Promise<LinkedAccountRes> => {
			const res = await ctx.LinkedAccount.add(ctx.body);
			ctx.set.status = res.Code;
			return res;
		},
		{
			detail: {
				description:
					'Add an existing account to the customer’s account linked account list for bill payments and monitoring.<br><br><b>Backend System:</b> Redis, NOVA SIEBEL<br><b>Table:</b> linked_accounts',
				tags: ['User']
			},
			body: linkAccountReqSchema,
			response: {
				201: linkedAccountResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.patch(
		'/linked-account',
		async (ctx): Promise<LinkedAccountRes> => {
			return ctx.LinkedAccount.update(ctx.body);
		},
		{
			body: updateLinkedAccountReqSchema,
			response: {
				200: linkedAccountResSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Update the label of a linked account.<br><br><b>Backend System:</b> Redis<br><b>Table:</b> linked_accounts',
				tags: ['User']
			}
		}
	)
	.delete(
		'/linked-account',
		async (ctx): Promise<LinkedAccountRes> => {
			return ctx.LinkedAccount.unlink(ctx.body);
		},
		{
			body: deleteLinkedAccountReqSchema,
			response: {
				200: linkedAccountResSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Unlink an account from the customer’s linked account list.<br><br><b>Backend System:</b> Redis<br><b>Table:</b> linked_accounts',
				tags: ['User']
			}
		}
	)
	.get(
		'/linked-accounts/non-owners',
		async (ctx): Promise<LinkedAccountsNonOwnersRes> => {
			return await ctx.LinkedAccount.getNonOwnersLinkedAccountsByPage(
				ctx.query
			);
		},
		{
			detail: {
				description:
					'Retrieve a list of linked owner accounts.<br><br><b>Backend System:</b> NOVA SIEBEL',
				tags: ['User']
			},
			query: linkedAccountsNonOwnersReqSchema,
			response: {
				200: linkedAccountsNonOwnersResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.delete(
		'/linked-account/non-owner',
		async (ctx): Promise<DeleteNonOwnerRes> => {
			return ctx.LinkedAccount.revokeAccess(ctx.body);
		},
		{
			body: deleteNonOwnerReqSchema,
			response: {
				200: deleteNonOwnerResSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Revoke access to a non-owner account from the owner account.<br><br><b>Table:</b> linked_accounts',
				tags: ['User']
			}
		}
	);
export default linkedAccountV1Routes;
