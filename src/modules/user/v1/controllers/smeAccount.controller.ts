import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import { Elysia } from 'elysia';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type CloudConnectRegistrationRes,
	type DmsCreditScoreRes,
	type ECommerceRegistrationRes,
	cloudConnectRegistrationReqSchema,
	cloudConnectRegistrationResSchema,
	dmsCreditScoreReqSchema,
	dmsCreditScoreResSchema,
	eCommerceRegistrationReqSchema,
	eCommerceRegistrationResSchema
} from '../schemas/api/smeAccount.schema';
import SmeAccount from '../services/smeAccount.service';

const smeAccountV1Routes = new Elysia({ prefix: '/sme' })
	.use(bearer())
	.resolve(() => {
		return {
			SmeAccount: new SmeAccount(randomUUID())
		};
	})
	.get(
		'/dms-credit-score',
		async (ctx): Promise<DmsCreditScoreRes> => {
			return await ctx.SmeAccount.getDmsCreditScore(ctx.query.ServiceId);
		},
		{
			query: dmsCreditScoreReqSchema,
			response: {
				200: dmsCreditScoreResSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Obtain the credit score of an SME customer by service ID for Digital Marketing Solutions (DMS).<br><br><b>Backend System:</b> NOVA SIEBEL',
				tags: ['User']
			}
		}
	)
	.post(
		'/cloud-connect',
		async (ctx): Promise<CloudConnectRegistrationRes> => {
			const res = await ctx.SmeAccount.registerCloudConnect(ctx.body);
			ctx.set.status = res.Code;
			return res;
		},
		{
			body: cloudConnectRegistrationReqSchema,
			response: {
				201: cloudConnectRegistrationResSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Register a cloud storage account with Cloud Connect for SME users.<br><br><b>Backend System:</b> Asia Cloud Connect<br><b>Table:</b> wso2_email_txn_history',
				tags: ['User']
			}
		}
	)
	.post(
		'/ecommerce',
		async (ctx): Promise<ECommerceRegistrationRes> => {
			const res = await ctx.SmeAccount.registerECommerce(ctx.body);
			ctx.set.status = res.Code;
			return res;
		},
		{
			body: eCommerceRegistrationReqSchema,
			response: {
				201: eCommerceRegistrationResSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Create an e-commerce hub account for SME users.<br><br><b>Backend System:</b> E-Commerce Hub<br><b>Table:</b> wso2_email_txn_history',
				tags: ['User']
			}
		}
	);

export default smeAccountV1Routes;
