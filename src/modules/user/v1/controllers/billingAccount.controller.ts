import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import { Ely<PERSON> } from 'elysia';
import { getIdTokenInfo } from '../../../../middleware/uaid/util/utils';
import {
	type BaseResponse,
	baseResponseSchema,
	errorBaseResponseSchema
} from '../../../../shared/schemas/api/responses.schema';
import {
	type AccountSettingsRes,
	type AnnualBillStatementRes,
	type BillingProfileRes,
	type ExpDiscountRes,
	type IbillBillingDetailsRes,
	type LightweightBillHistoryRes,
	type NovaBillingProfileRes,
	type UpdateBillingProfileRes,
	accountSettingsReqSchema,
	accountSettingsResSchema,
	annualBillStatementReqSchema,
	annualBillStatementResSchema,
	billingHistoryReqSchema,
	billingProfileReqSchema,
	billingProfileResSchema,
	expDiscountReqSchema,
	expDiscountResSchema,
	ibillDetailsReqSchema,
	ibillDetailsResSchema,
	lightweightBillHistoryResSchema,
	novaBillingProfileReqSchema,
	novaBillingProfileResSchema,
	updateAccountLabelReqSchema,
	updateBillingProfileReqSchema,
	updateBillingProfileResSchema,
	updateQuickLinksReqSchema
} from '../schemas/api/billingAccount.schema';
import BillingAccount from '../services/billingAccount.service';

const billingAccountV1Routes = new Elysia({ prefix: '/billing' })
	.use(bearer())
	.resolve(async ctx => {
		const idTokenInfo = await getIdTokenInfo(ctx.bearer);
		return {
			BillingAccount: new BillingAccount(randomUUID(), idTokenInfo)
		};
	})
	.post(
		'/expiry-discount-details',
		async (ctx): Promise<ExpDiscountRes> => {
			return await ctx.BillingAccount.getExpiryDiscountDetails(
				ctx.body.EncryptedBillAccNo,
				ctx.body.Products
			);
		},
		{
			body: expDiscountReqSchema,
			response: {
				200: expDiscountResSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					"Retrieve a customer's expiring discount offer details from WSO2 to send reminders about upcoming expirations.<br><br><b>Backend System:</b> NOVA SIEBEL",
				tags: ['User']
			}
		}
	)
	.get(
		'/history',
		async (ctx): Promise<LightweightBillHistoryRes> => {
			return await ctx.BillingAccount.getBillingHistory(ctx.query);
		},
		{
			query: billingHistoryReqSchema,
			response: {
				200: lightweightBillHistoryResSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Fetch customer billing and payment history from WSO2, including billing trends for the last six months.<br><br><b>Backend System:</b> Redis, NOVA SIEBEL, ICP',
				tags: ['User']
			}
		}
	)
	.get(
		'/annual-statement',
		async (ctx): Promise<AnnualBillStatementRes> => {
			return await ctx.BillingAccount.getAnnualBillStatementByYear(ctx.query);
		},
		{
			query: annualBillStatementReqSchema,
			response: {
				200: annualBillStatementResSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Retrieve the customer’s annual bill statement in PDF format by year.<br><br><b>Backend System:</b> Redis, NOVA SIEBEL, ICP',
				tags: ['User']
			}
		}
	)
	.get(
		'/nova/profile',
		async (ctx): Promise<NovaBillingProfileRes> => {
			return await ctx.BillingAccount.getNovaBillingProfile(
				ctx.query.EncryptedBillAccNo
			);
		},
		{
			query: novaBillingProfileReqSchema,
			response: {
				200: novaBillingProfileResSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Obtain the customer’s NOVA billing profile from WSO2, useful for autopay or features requiring personal, address, and invoice details.<br><br><b>Backend System:</b> Redis, NOVA SIEBEL, ICP',
				tags: ['User']
			}
		}
	)
	.get(
		'/profile',
		async (ctx): Promise<BillingProfileRes> => {
			const res = await ctx.BillingAccount.getBillingAccountProfile(ctx.query);
			return res;
		},
		{
			query: billingProfileReqSchema,
			response: {
				200: billingProfileResSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Fetch key details of a customer’s billing account, including personal information, subscribed addons, billing and payment history, and the annual bill statement. Initial calls may take a few seconds due to composite requests to WSO2, with faster responses on subsequent calls thanks to caching.<br><br><b>Backend System:</b> Redis, NOVA SIEBEL<br><b>Table:</b> account_settings',
				tags: ['User']
			}
		}
	)
	.patch(
		'/profile',
		async (ctx): Promise<UpdateBillingProfileRes> => {
			const res = await ctx.BillingAccount.updateBillingProfile(ctx.body);
			ctx.set.status = res.Code;
			return res;
		},
		{
			body: updateBillingProfileReqSchema,
			response: {
				201: updateBillingProfileResSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Update the billing email, mobile number, or address for NOVA or ICP account through WSO2.<br><br><b>Backend System:</b> NOVA SIEBEL, ICP',
				tags: ['User']
			}
		}
	)
	.get(
		'/ibill-details',
		async (ctx): Promise<IbillBillingDetailsRes> => {
			return await ctx.BillingAccount.getIbillBillingDetails(ctx.query);
		},
		{
			query: ibillDetailsReqSchema,
			response: {
				200: ibillDetailsResSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description: 'Fetch customer billing details from WSO2',
				tags: ['User']
			}
		}
	)
	.get(
		'/settings',
		async (ctx): Promise<AccountSettingsRes> => {
			return await ctx.BillingAccount.getAccountSettings(ctx.query);
		},
		{
			detail: {
				description:
					'Retrieve the owner account settings for each billing account, including quick links and account labels.<br><br><b>Table:</b> account_settings',
				tags: ['User']
			},
			query: accountSettingsReqSchema,
			response: {
				200: accountSettingsResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.post(
		'/setting/quick-links',
		async (ctx): Promise<BaseResponse> => {
			const res = await ctx.BillingAccount.updateQuickLinks(ctx.body);
			ctx.set.status = res.Code;
			return res;
		},
		{
			detail: {
				description:
					'Add or update quick links in the owner account settings.<br><br><b>Table:</b> account_settings',
				tags: ['User']
			},
			body: updateQuickLinksReqSchema,
			response: {
				201: baseResponseSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.post(
		'/setting/label',
		async ctx => {
			const res = await ctx.BillingAccount.updateAccountLabel(ctx.body);
			ctx.set.status = res.Code;
			return res;
		},
		{
			detail: {
				description:
					'Add or update an account label in the owner billing account settings. This label is used in the service dashboard, with the default being the billing account number from the /accounts/profile API.<br><br><b>Table:</b> account_settings',
				tags: ['User']
			},
			body: updateAccountLabelReqSchema,
			response: {
				201: baseResponseSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export default billingAccountV1Routes;
