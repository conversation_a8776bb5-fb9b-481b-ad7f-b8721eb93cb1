import { randomUUID } from 'node:crypto';
import { Elysia } from 'elysia';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type KCIBillingInfoRes,
	kciBillingInfoReqSchema,
	kciBillingInfoResSchema
} from '../schemas/api/kci.schema';
import KCI from '../services/kci.service';

const kciV1Routes = new Elysia({ prefix: '/kci' })
	.resolve(() => {
		return {
			BillingAccount: new KCI(randomUUID())
		};
	})
	.post(
		'/billing-info',
		async (ctx): Promise<KCIBillingInfoRes> => {
			return await ctx.BillingAccount.getKCIBillingInfo(ctx.body);
		},
		{
			body: kciBillingInfoReqSchema,
			response: {
				200: kciBillingInfoResSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Fetch customer billing information from WSO2 for KCI (Keep Customers Informed) payment notifications.<br><br><b>Backend System:</b> NOVA SIEBEL',
				tags: ['User']
			}
		}
	);

export default kciV1Routes;
