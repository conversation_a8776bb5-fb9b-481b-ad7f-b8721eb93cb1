import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import { Elysia } from 'elysia';
import { getIdTokenInfo } from '../../../../middleware/uaid/util/utils';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type AddressDetailsRes,
	type ProfileAccountsRes,
	addressDetailsResSchema,
	profileAccountsReqSchema,
	profileAccountsResSchema
} from '../schemas/api/profileAccount.schema';
import ProfileAccount from '../services/profileAccount.service';

const profileAccountV1Routes = new Elysia({ prefix: '/profile' })
	.use(bearer())
	.resolve(async ctx => {
		const idTokenInfo = await getIdTokenInfo(ctx.bearer);
		return {
			ProfileAccount: new ProfileAccount(randomUUID(), idTokenInfo)
		};
	})
	.get(
		'/accounts/address-details',
		async (ctx): Promise<AddressDetailsRes> => {
			return await ctx.ProfileAccount.getAccountsAddressDetails();
		},
		{
			response: {
				200: addressDetailsResSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Retrieve a list of billing accounts within a customer account, along with basic personal details for service dashboard purposes. The first call may take a few seconds due to composite requests to WSO2, but subsequent calls will benefit from cached data.<br><br><b>Backend System:</b> Redis, NOVA SIEBEL, ICP',
				tags: ['User']
			}
		}
	)
	.get(
		'/accounts',
		async (ctx): Promise<ProfileAccountsRes> => {
			const { Page, Limit } = ctx.query;
			const pageNumber = !Number.isNaN(Number(Page)) ? Number(Page) : 1;
			const limitNumber = !Number.isNaN(Number(Limit)) ? Number(Limit) : 5;
			return await ctx.ProfileAccount.getProfileByPage(pageNumber, limitNumber);
		},
		{
			query: profileAccountsReqSchema,
			response: {
				200: profileAccountsResSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Retrieve a list of billing accounts within a customer account, along with basic personal details for service dashboard purposes. The first call may take a few seconds due to composite requests to WSO2, but subsequent calls will benefit from cached data.<br><br><b>Backend System:</b> Redis, NOVA SIEBEL, ICP <br><b>Table:</b> account_settings',
				tags: ['User']
			}
		}
	);

export default profileAccountV1Routes;
