import Elysia from 'elysia';
import billingAccountV1Routes from './v1/controllers/billingAccount.controller';
import easyfixV1Routes from './v1/controllers/easyfix.controller';
import kciV1Routes from './v1/controllers/kci.controller';
import linkedAccountV1Routes from './v1/controllers/linkedAccount.controller';
import pfaBillingAccountV1Routes from './v1/controllers/payForAnyone.controller';
import profileAccountV1Routes from './v1/controllers/profileAccount.controller';
import smeAccountV1Routes from './v1/controllers/smeAccount.controller';

export const privateUserV1Routes = new Elysia({ prefix: '/v1/user' })
	.use(profileAccountV1Routes)
	.use(billingAccountV1Routes)
	.use(smeAccountV1Routes)
	.use(linkedAccountV1Routes)
	.use(easyfixV1Routes);

export const protectedUserV1Routes = new Elysia({ prefix: '/v1/user' })
	.use(pfaBillingAccountV1Routes)
	.use(kciV1Routes);
