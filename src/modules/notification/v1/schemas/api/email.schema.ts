import { type Static, t } from 'elysia';

export const submitFeedbackFormReqSchema = t.Object({
	Name: t.String(),
	Email: t.String(),
	EffectedService: t.String(),
	SignalBar: t.String(),
	NetworkIndicator: t.String(),
	Environment: t.String(),
	ProductType: t.String(),
	EnquiryType: t.String(),
	EnquiryDetails: t.String(),
	DeviceModel: t.String(),
	FailureTime: t.String(),
	FailureDate: t.String(),
	ErrorMessage: t.String(),
	State: t.String(),
	City: t.String(),
	Postcode: t.String(),
	Description: t.String(),
	AddressLine1: t.String(),
	AddressLine2: t.String(),
	AddressLine3: t.String(),
	AccountNumber: t.String(),
	MobileNumber: t.String(),
	Files: t.Optional(
		t.Files({
			maxSize: 1024 * 1024,
			description: 'Max size is 1MB and only supported png, pdf, jpg, jpeg'
		})
	)
});

export type SubmitFeedbackFormReq = Static<typeof submitFeedbackFormReqSchema>;
