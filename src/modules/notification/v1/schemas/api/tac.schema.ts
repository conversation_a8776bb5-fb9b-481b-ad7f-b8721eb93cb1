import { type Static, t } from 'elysia';
import { TacRequestTypeEnum } from '../../../../../enum/notification.enum';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const generateTacReqSchema = t.Object({
	RequestType: t.Enum(TacRequestTypeEnum),
	Value: t.String({
		examples: ['6016656569', '<EMAIL>']
	})
});

export const verifyTacReqSchema = t.Object({
	RequestType: t.Enum(TacRequestTypeEnum),
	Value: t.String({
		examples: ['6016656569', '<EMAIL>']
	}),
	TacCode: t.String({ examples: ['EPLF-6397'] })
});

export const tacResponseSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			FeFlag: t.Bo<PERSON>an({ examples: ['false'] }),
			PrefixTac: t.String({ examples: ['ACF'] })
		})
	},
	{ description: 'TAC prefix successfully received.' }
);

export type TacResponse = Static<typeof tacResponseSchema>;

export type GenerateTacReq = Static<typeof generateTacReqSchema>;

export type VerifyTacReq = Static<typeof verifyTacReqSchema>;
