import { integer, pgTable, text, timestamp } from 'drizzle-orm/pg-core';

export const tacCounterTableSchema = pgTable('tac_counter', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity().notNull(),
	MobileNumber: text('mobile_number'),
	Email: text('email'),
	TAC: text('tac').notNull(),
	Status: text('status').notNull(),
	TacCounter: integer('tac_counter').notNull(),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	ExpiredAt: timestamp('expired_at', { mode: 'date' }).notNull()
});

export type SelectTac = typeof tacCounterTableSchema.$inferSelect;

export type InsertTac = typeof tacCounterTableSchema.$inferInsert;
