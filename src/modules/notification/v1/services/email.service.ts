import { envConfig } from '../../../../config/env.config';
import { EmailEnum } from '../../../../enum/notification.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	Wso2EmailAttachmentReq,
	Wso2EmailReq
} from '../../../../integration/wso2/notification/schemas/api/wso2Email.schema';
import { UE_ERROR } from '../../../../middleware/error';
import type { BaseResponse } from '../../../../shared/schemas/api/responses.schema';
import type { SubmitFeedbackFormReq } from '../schemas/api/email.schema';

class EmailService {
	private integrationId: string;
	private mwIntegration: MwIntegration;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
		this.mwIntegration = new MwIntegration(integrationId);
	}

	async sendFeedbackFormEmail(
		req: SubmitFeedbackFormReq
	): Promise<BaseResponse> {
		this.validateFiles(req.Files);

		const emailRequest: Wso2EmailReq = this.buildEmailRequest(req);

		const wso2Response: boolean = req.Files
			? await this.sendEmailWithAttachments(emailRequest, req.Files)
			: await this.sendEmail(emailRequest);

		if (wso2Response) {
			return {
				Success: true,
				Code: StatusCodeEnum.CREATED,
				Message: 'Email sent successfully',
				IntegrationId: this.integrationId
			};
		}

		throw new UE_ERROR('Failed to send email', StatusCodeEnum.WSO2_ERROR, {
			integrationId: this.integrationId,
			response: null
		});
	}

	private validateFiles(files?: { size: number; name: string }[]): void {
		if (!files) return;

		for (const file of files) {
			if (file.size > 1024 * 1024) {
				throw new UE_ERROR(
					'File size is too large',
					StatusCodeEnum.PAYLOAD_TOO_LARGE,
					{
						integrationId: this.integrationId,
						response: null
					}
				);
			}

			const ext = file.name.split('.').pop()?.toLowerCase();
			if (!['png', 'pdf', 'jpg', 'jpeg'].includes(ext ?? '')) {
				throw new UE_ERROR(
					'File type is not supported. Only png, pdf, jpg & jpeg are allowed',
					StatusCodeEnum.BAD_REQUEST_ERROR,
					{
						integrationId: this.integrationId,
						response: null
					}
				);
			}
		}
	}

	private buildEmailRequest(req: SubmitFeedbackFormReq): Wso2EmailReq {
		return {
			from: EmailEnum.FROM_NOREPLY,
			to: envConfig().CX_EMAIL_ADDRESS,
			cc: req.Email,
			subject: `Feedback Form from Myunifi App for: ${req.Name}`,
			body: this.getEmailTemplate(req)
		};
	}

	private async sendEmail(emailRequest: Wso2EmailReq): Promise<boolean> {
		return this.mwIntegration
			.getWso2NotificationIntegration()
			.getWso2SendEmail(
				emailRequest,
				this.integrationId,
				'email feedback form'
			);
	}

	private async sendEmailWithAttachments(
		emailRequest: Wso2EmailReq,
		files: Wso2EmailAttachmentReq
	): Promise<boolean> {
		return this.mwIntegration
			.getWso2NotificationIntegration()
			.getWso2SendEmailWithAttachment(
				emailRequest,
				files,
				this.integrationId,
				'email feedback form'
			);
	}

	private getEmailTemplate(req: SubmitFeedbackFormReq): string {
		const fields = [
			{ label: 'Name', value: req.Name },
			{ label: 'Email', value: req.Email },
			{ label: 'Account Number', value: req.AccountNumber },
			{ label: 'Mobile Number', value: req.MobileNumber },
			{ label: 'Description', value: req.Description },
			{ label: 'Effected Service', value: req.EffectedService },
			{ label: 'Signal Bar', value: req.SignalBar },
			{ label: 'Network Indicator', value: req.NetworkIndicator },
			{ label: 'Environment', value: req.Environment },
			{ label: 'Product Type', value: req.ProductType },
			{ label: 'Enquiry Type', value: req.EnquiryType },
			{ label: 'Enquiry Details', value: req.EnquiryDetails },
			{ label: 'Device Model', value: req.DeviceModel },
			{ label: 'Failure Time', value: req.FailureTime },
			{ label: 'Failure Date', value: req.FailureDate },
			{ label: 'Error Message', value: req.ErrorMessage },
			{ label: 'Address Line 1', value: req.AddressLine1 },
			{ label: 'Address Line 2', value: req.AddressLine2 },
			{ label: 'Address Line 3', value: req.AddressLine3 },
			{ label: 'State', value: req.State },
			{ label: 'City', value: req.City },
			{ label: 'Postcode', value: req.Postcode }
		];

		const rows: string = fields
			.map(
				({ label, value }) =>
					`<tr><th>${label}</th><td>${value ?? 'N/A'}</td></tr>`
			)
			.join('');

		return `
      <!DOCTYPE html>
      <html>
        <head>
          <style>
            table, th, td { border: 1px solid black; border-collapse: collapse; padding: 5px; text-align: left; }
          </style>
        </head>
        <body>
          <h2>Feedback details:</h2>
          <table>${rows}</table>
        </body>
      </html>`;
	}
}

export default EmailService;
