import { randomUUID } from 'node:crypto';
import Elysia from 'elysia';
import { baseHeaderSchema } from '../../../../shared/schemas/api/headers.schema';
import {
	type BaseResponse,
	baseResponseSchema,
	errorBaseResponseSchema
} from '../../../../shared/schemas/api/responses.schema';
import { submitFeedbackFormReqSchema } from '../schemas/api/email.schema';
import EmailService from '../services/email.service';

const emailV1Routes = new Elysia({ prefix: '/email' })
	.derive(() => {
		return {
			integrationId: randomUUID()
		};
	})
	.post(
		'/feedback-form',
		async (req): Promise<BaseResponse> => {
			const res = await new EmailService(
				req.integrationId
			).sendFeedbackFormEmail(req.body);

			req.set.status = res.Code;

			return res;
		},
		{
			headers: baseHeaderSchema,
			parse: 'multipart/form-data',
			body: submitFeedbackFormReqSchema,
			response: {
				201: baseResponseSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Submit a feedback email to the support team (<EMAIL>)',
				tags: ['Notification']
			}
		}
	);

export default emailV1Routes;
