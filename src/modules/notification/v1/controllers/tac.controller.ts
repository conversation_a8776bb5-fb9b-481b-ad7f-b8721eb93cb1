import { randomUUID } from 'node:crypto';
import Elysia from 'elysia';
import { baseHeaderSchema } from '../../../../shared/schemas/api/headers.schema';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type TacResponse,
	generateTacReqSchema,
	tacResponseSchema,
	verifyTacReqSchema
} from '../schemas/api/tac.schema';
import TacNotification from '../services/tac.service';

const tacRoutes = new Elysia({ prefix: '/tac' })
	.resolve(async () => {
		return {
			TacNotification: new TacNotification(randomUUID())
		};
	})
	.post(
		'/generate',
		async (ctx): Promise<TacResponse> => {
			return await ctx.TacNotification.generateTac(
				ctx.body,
				ctx.headers.source
			);
		},
		{
			headers: baseHeaderSchema,
			body: generateTacReqSchema,
			response: {
				200: tacResponseSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Request TAC with phone number. For Automation Testing, the phone number is given upon request. <br><br> <b>Backend System:</b> MCS <br> <b>Table:</b> tac_counter',
				tags: ['Notification']
			}
		}
	)
	.post(
		'/verify',
		async (ctx): Promise<TacResponse> => {
			return await ctx.TacNotification.verifyTac(
				ctx.body,
				ctx.body.RequestType,
				ctx.headers.source
			);
		},
		{
			headers: baseHeaderSchema,
			body: verifyTacReqSchema,
			response: {
				200: tacResponseSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Request TAC with phone number. For Automation Testing, the phone number is given upon request. <br><br> <b>Table:</b> tac_counter',
				tags: ['Notification']
			}
		}
	);

export default tacRoutes;
