import { randomUUID } from 'node:crypto';
import Elysia, { t } from 'elysia';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import { osesResponseSchema } from '../schemas/api/osesResponse.schema';
import OsesCallback from '../services/osesCallback.service';

export const osesCallbackV1Routes = new Elysia({ prefix: '/callback/oses' })
	.resolve(() => {
		return {
			OsesCallback: new OsesCallback(randomUUID())
		};
	})
	.post(
		'/response',
		async ctx => {
			const res = await ctx.OsesCallback.processOsesResponse(ctx.body);
			return ctx.redirect(res, 302);
		},
		{
			body: osesResponseSchema,
			parse: 'application/x-www-form-urlencoded',
			response: {
				302: t.Null({
					description:
						'Once the response is received, the customer will be redirected to the front end page. Triggered by TM OSES System.'
				}),
				500: errorBaseResponseSchema
			},
			detail: {
				description: 'Receive the response from TM OSES.',
				tags: ['Payment', 'Callback']
			}
		}
	);
