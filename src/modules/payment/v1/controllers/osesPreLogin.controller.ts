import { randomUUID } from 'node:crypto';
import Elysia, { t } from 'elysia';
import { SourceEnum } from '../../../../enum/header.enum';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type CreateOsesUrlRes,
	createOsesUrlReqSchema,
	createOsesUrlResSchema
} from '../schemas/api/osesCreateUrl.schema';
import {
	type OsesStatusRes,
	osesStatusReqSchema,
	osesStatusResSchema
} from '../schemas/api/osesStatus.schema';
import OsesPreLogin from '../services/osesPreLogin.service';

export const osesPreLoginV1Routes = new Elysia({ prefix: '/oses' })
	.resolve(() => {
		return {
			OsesPreLogin: new OsesPreLogin(randomUUID())
		};
	})
	.post(
		'/url',
		async (ctx): Promise<CreateOsesUrlRes> => {
			const res = await ctx.OsesPreLogin.createOsesUrl(
				ctx.headers.source,
				ctx.body
			);
			ctx.set.status = res.Code;
			return res;
		},
		{
			detail: {
				description:
					"Construct a customer's transaction request information for payment via TM OSES. <br><br> <b>Backend System:</b> OSES <br> <b>Table:</b> oses_txn_history",
				tags: ['Payment']
			},
			headers: t.Object({
				source: t.Enum(SourceEnum),
				'x-api-key': t.String()
			}),
			body: createOsesUrlReqSchema,
			response: {
				201: createOsesUrlResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.post(
		'/prelogin/status',
		async (ctx): Promise<OsesStatusRes> => {
			return await ctx.OsesPreLogin.getPaymentStatus(ctx.body);
		},
		{
			detail: {
				description:
					'Get OSES payment status for non-login customer (Guest) based on merchant txn id. <br><br> <b>Table:</b> oses_txn_history',
				tags: ['Payment']
			},
			body: osesStatusReqSchema,
			response: {
				200: osesStatusResSchema,
				500: errorBaseResponseSchema
			}
		}
	);
