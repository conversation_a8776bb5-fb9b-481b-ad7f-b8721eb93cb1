import {
	boolean,
	integer,
	pgTable,
	text,
	timestamp
} from 'drizzle-orm/pg-core';

export const autopaySettingHistoryTableSchema = pgTable(
	'autopay_setting_history',
	{
		Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
		IdType: text('id_type').notNull(),
		MaskedIdValue: text('masked_id_value'),
		EncryptedBillAccNo: text('encrypted_bill_acc_no').notNull(),
		SystemName: text('system_name').notNull(),
		RequestType: text('request_type').notNull(),
		SRNumber: text('sr_number').notNull(),
		CcType: text('cc_type'),
		EncryptedCCNumber: text('encrypted_cc_number'),
		CcIssuerBank: text('cc_issuer_bank'),
		CcIssuerBankName: text('cc_issuer_bank_name'),
		CcExpiryMonth: text('cc_expiry_month'),
		CcExpiryYear: text('cc_expiry_year'),
		CcHolderName: text('cc_holder_name'),
		CcOwnerIdType: text('cc_owner_id_type'),
		MaskedCcOwnerIdValue: text('masked_cc_owner_id_value'),
		CcOwnerCustomerRelation: text('cc_owner_customer_relation'),
		EmailAddress: text('email_address').notNull(),
		IsEmailSent: boolean('is_email_sent').notNull(),
		CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull()
	}
);

export type SelectAutopaySettingHistory =
	typeof autopaySettingHistoryTableSchema.$inferSelect;
