import { integer, pgTable, text, timestamp } from 'drizzle-orm/pg-core';

export const billPaymentTxnHistoryTableSchema = pgTable(
	'bill_payment_txn_history',
	{
		Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
		MerchantTxnId: text('merchant_txn_id').notNull(),
		BillingAccountNo: text('billing_account_no').notNull(),
		BillNo: text('bill_no'),
		Email: text('email').notNull(),
		UserEnteredAmt: text('user_entered_amt').notNull(),
		OutstandingAmt: text('outstanding_amt').notNull(),
		PaidAmt: text('paid_amt'),
		TxnStatus: text('txn_status'),
		Source: text('source'),
		CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
		UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
	}
);

export type SelectBillPaymentTxnHistory =
	typeof billPaymentTxnHistoryTableSchema.$inferSelect;

export type InsertBillPaymentTxnHistory =
	typeof billPaymentTxnHistoryTableSchema.$inferInsert;
