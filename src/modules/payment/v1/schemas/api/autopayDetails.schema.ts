import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const getAutopayDetailsReqSchema = t.Object({
	EncryptedBillAccNo: t.String({
		example: 'fh2490348gh9=43tqhjuwg9i8ht9qh3e0=-',
		minLength: 1
	})
});

export type GetAutopayDetailsReq = Static<typeof getAutopayDetailsReqSchema>;

export const getAutopayDetailsResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			EncryptedCardNo: t.Nullable(t.String({ examples: ['**********'] })),
			MaskedCardNo: t.Nullable(t.String({ examples: ['**********'] })),
			CardType: t.Nullable(t.String({ examples: ['VISA'] })),
			BankName: t.Nullable(t.String({ examples: ['CIMB BANK'] })),
			CardExpiryDate: t.Nullable(t.String({ examples: ['01/2024'] })),
			OwnerName: t.Nullable(t.String({ examples: ['John Doe'] })),
			IdType: t.Nullable(t.String({ examples: ['New NRIC'] })),
			IdValue: t.Nullable(t.String({ examples: ['999999-99-9999'] })),
			Relationship: t.Nullable(t.String({ examples: ['Owner'] })),
			EmailAddress: t.Nullable(t.String({ examples: ['<EMAIL>'] }))
		})
	},
	{ description: 'Autopay details successfully retrieved.' }
);

export type GetAutopayDetailsRes = Static<typeof getAutopayDetailsResSchema>;
