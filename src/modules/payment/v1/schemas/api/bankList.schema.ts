import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

const bankListObjectSchema = t.Object({
	BankName: t.String({ examples: ['CIMB BANK'] }),
	BankCode: t.Number({ examples: [101] }),
	Flag: t.Array(t.String({ examples: ['Payment'] }))
});

export const bankListResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Array(bankListObjectSchema)
	},
	{
		description: 'A list of bank supported by autopay successfully retrieved.'
	}
);

export type BankListRes = Static<typeof bankListResSchema>;
