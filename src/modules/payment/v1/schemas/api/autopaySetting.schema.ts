import { type Static, t } from 'elysia';
import {
	AutopayRelationshipEnum,
	AutopayReqTypeEnum,
	CreditCardTypeEnum
} from '../../../../../enum/payment.enum';
import { SystemNameEnum } from '../../../../../enum/wso2.enum';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const autopaySettingReqSchema = t.Object({
	BillingAccountName: t.String({ examples: ['<PERSON>'] }),
	EncryptedBillAccNo: t.String({
		examples: ['fh2490348gh9=43tqhjuwg9i8ht9qh3e0=-']
	}),
	SystemName: t.Enum(SystemNameEnum),
	EmailAddress: t.String({ format: 'email', examples: ['<EMAIL>'] }),
	RequestType: t.Enum(AutopayReqTypeEnum),
	CreditCardInfo: t.Optional(
		t.Object({
			CcType: t.Enum(CreditCardTypeEnum),
			CcNumber: t.String({ examples: ['**********'] }),
			IsEncryptedCCNumber: t.Boolean({
				examples: [true],
				description: 'Return true if the CC number sent is encrypted value.'
			}),
			CcIssuerBank: t.String({ examples: ['44'] }),
			CcIssuerBankName: t.String({ examples: ['CIMB ISLAM MALAYSIA BERHAD'] }),
			CcExpiryMonth: t.String({ examples: ['01'] }),
			CcExpiryYear: t.String({ examples: ['2024'] }),
			CcHolderName: t.String({ examples: ['John Doe'] }),
			CcOwnerIdType: t.String({ examples: ['New NRIC'] }),
			CcOwnerIdValue: t.String({ examples: ['999999-99-9999'] }),
			CcOwnerCustomerRelation: t.Enum(AutopayRelationshipEnum)
		})
	)
});

export type AutopaySettingReq = Static<typeof autopaySettingReqSchema>;

export const autopaySettingResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			SRNumber: t.String({ examples: ['SR123456'] })
		})
	},
	{ description: 'Autopay setting successfully added.' }
);

export type AutopaySettingRes = Static<typeof autopaySettingResSchema>;
