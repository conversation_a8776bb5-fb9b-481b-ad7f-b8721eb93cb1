import { type Static, t } from 'elysia';

export const osesResponseSchema = t.Object({
	MERCHANTID: t.String(),
	MERCHANT_TRANID: t.String(),
	ERR_CODE: t.Optional(t.MaybeEmpty(t.String())),
	ERR_DESC: t.Optional(t.MaybeEmpty(t.String())),
	USR_CODE: t.Optional(t.MaybeEmpty(t.String())),
	USR_MSG: t.Optional(t.MaybeEmpty(t.String())),
	TXN_STATUS: t.String(),
	EUI: t.Optional(t.MaybeEmpty(t.String())),
	TRANSACTIONID: t.Optional(t.MaybeEmpty(t.String())),
	TRANDATE: t.Optional(t.MaybeEmpty(t.String())),
	TRANSACTIONTYPE: t.Optional(t.MaybeEmpty(t.String())),
	FRAUDRISKLEVEL: t.Optional(t.MaybeEmpty(t.String())),
	FRAUDRISKSCORE: t.Optional(t.MaybeEmpty(t.String())),
	EXCEED_HIGH_RISK: t.Optional(t.MaybeEmpty(t.String())),
	AUTH_ID: t.Optional(t.MaybeEmpty(t.String())),
	BANK_REFERENCE: t.Optional(t.MaybeEmpty(t.String())),
	BANK_STATUS_DESC: t.Optional(t.MaybeEmpty(t.String())),
	ACQUIRER_BANK: t.Optional(t.MaybeEmpty(t.String())),
	PAYMENT_METHOD: t.Optional(t.MaybeEmpty(t.String())),
	CURRENCYCODE: t.Optional(t.MaybeEmpty(t.String())),
	AMOUNT: t.String(),
	CUSTNAME: t.Optional(t.MaybeEmpty(t.String())),
	DESCRIPTION: t.String(),
	SIGNATURE: t.String(),
	IS_BLACKLISTED: t.Optional(t.MaybeEmpty(t.String())),
	CARDTYPE: t.Optional(t.MaybeEmpty(t.String())),
	CARD_NO_PARTIAL: t.Optional(t.MaybeEmpty(t.String())),
	CARDNAME: t.Optional(t.MaybeEmpty(t.String())),
	BANK_RES_CODE: t.Optional(t.MaybeEmpty(t.String())),
	BANK_RES_MSG: t.Optional(t.MaybeEmpty(t.String())),
	WHITELIST_CARD: t.Optional(t.MaybeEmpty(t.String()))
});

export type OsesResponse = Static<typeof osesResponseSchema>;
