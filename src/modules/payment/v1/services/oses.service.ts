import { and, eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { UE_ERROR } from '../../../../middleware/error';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import { decrypt } from '../../../../shared/encryption/aesGcm';
import {
	type SelectLinkedAccounts,
	linkedAccountsTableSchema
} from '../../../user/v1/schemas/db/linkedAccounts.schema';
import OsesHelper from '../helpers/oses.helper';
import type { OsesChildTxn } from '../schemas/api/osesCreateUrl.schema';
import type {
	OsesHistoryReq,
	OsesHistoryRes,
	OsesHistoryResObj
} from '../schemas/api/osesHistory.schema';
import type {
	OsesStatusReq,
	OsesStatusRes,
	OsesStatusResObj
} from '../schemas/api/osesStatus.schema';
import {
	type SelectOsesTxnHistory,
	osesTxnHistoryTableSchema
} from '../schemas/db/osesTxnHistory.schema';

class Oses {
	private db: NodePgDatabase;
	private integrationId: string;
	private osesHelper: OsesHelper;
	private idTokenInfo: IdTokenInfo;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
		this.osesHelper = new OsesHelper(integrationId);
		this.idTokenInfo = idTokenInfo;
	}

	async getPaymentHistory(req: OsesHistoryReq): Promise<OsesHistoryRes> {
		const result: SelectOsesTxnHistory[] = await this.db
			.select()
			.from(osesTxnHistoryTableSchema)
			.where(eq(osesTxnHistoryTableSchema.PayerEmail, req.Email));

		const osesHistoryResObj: OsesHistoryResObj[] = [];

		for (const row of result) {
			const txnStatus: string = this.osesHelper.getOsesTxnStatus(row.TxnStatus);
			osesHistoryResObj.push({
				MerchantTxnId: row.MerchantTxnId,
				MerchantId: row.MerchantId,
				PayerEmail: row.PayerEmail,
				PayerName: row.PayerName,
				BankReference: row.BankReference,
				Source: row.Source,
				TotalAmount: row.Amount,
				PaymentType: row.PaymentType,
				TxnDate: row.TxnDate,
				TxnStatus: txnStatus,
				BillingAccounts: row.ChildTxn.filter(
					(childTxn): childTxn is NonNullable<typeof childTxn> =>
						childTxn !== undefined
				).map(childTxn => ({
					Name: childTxn.Name,
					Email: childTxn.Email,
					BillingAccountNo: childTxn.BillingAccountNo,
					BillNo: childTxn.BillNo,
					GrossAmount: childTxn.GrossAmount,
					GbtAmount: childTxn.GbtAmount,
					NettAmount: childTxn.NettAmount,
					MiscAmount: childTxn.MiscAmount,
					SystemName: childTxn.SystemName
				}))
			});
		}

		const res: OsesHistoryRes = {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				TransactionHistory: osesHistoryResObj
			}
		};

		return res;
	}

	async getPaymentStatus(req: OsesStatusReq): Promise<OsesStatusRes> {
		let decryptedHash = null;
		if (req.EncryptedHash) {
			decryptedHash = await decrypt(req.EncryptedHash);
		}

		const result: SelectOsesTxnHistory[] = await this.db
			.select()
			.from(osesTxnHistoryTableSchema)
			.where(eq(osesTxnHistoryTableSchema.MerchantTxnId, req.MerchantTxnId));
		if (result.length === 0) {
			throw new UE_ERROR(
				`No payment record found with ${req.MerchantTxnId}`,
				StatusCodeEnum.NOT_FOUND_ERROR,
				{
					integrationId: this.integrationId,
					response: 'Payment record not found'
				}
			);
		}
		if (decryptedHash && result[0].Signature !== decryptedHash) {
			throw new UE_ERROR(
				'Not allowed to access this resource!',
				StatusCodeEnum.FORBIDDEN_ERROR,
				{
					integrationId: this.integrationId,
					response: 'Access forbidden for this resource'
				}
			);
		}

		const paymentMethod: string = this.osesHelper.getOsesPaymentMethod(
			result[0].PaymentMethod ?? ''
		);

		const txnStatus: string = this.osesHelper.getOsesTxnStatus(
			result[0].TxnStatus
		);

		// Note: PFA payment type allows user to pay for only one billing account,
		// if the billing account is linked to the customer account, the flag IsLinkedAccount is set to true
		const billingAccounts =
			result[0].PaymentType === 'PFAU'
				? await this.getPFABillingAccounts(result[0].ChildTxn)
				: result[0].ChildTxn.map(childTxn => {
						return {
							BillingAccountNo: childTxn.BillingAccountNo,
							GrossAmount: childTxn.GrossAmount
						};
					});

		const osesStatusResObj: OsesStatusResObj = {
			MerchantTxnId: result[0].MerchantTxnId,
			MerchantId: result[0].MerchantId,
			PayerEmail: result[0].PayerEmail,
			PayerName: result[0].PayerName,
			BankReference: result[0].BankReference,
			Source: result[0].Source,
			TotalAmount: result[0].Amount,
			PaymentType: result[0].PaymentType,
			PaymentMethod: paymentMethod,
			TxnDate: result[0].TxnDate,
			TxnStatus: txnStatus,
			TxnId: result[0].TxnId,
			BillingAccounts: billingAccounts
		};

		const res: OsesStatusRes = {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				TransactionStatus: osesStatusResObj
			}
		};
		return res;
	}

	async getPFABillingAccounts(osesChildTxn: OsesChildTxn): Promise<
		{
			BillingAccountNo: string;
			GrossAmount: string;
			IsLinkedAccount: boolean;
		}[]
	> {
		const billingAccounts = [];
		for (const childTxn of osesChildTxn) {
			if (childTxn) {
				const linkedAccounts: SelectLinkedAccounts[] = await this.db
					.select()
					.from(linkedAccountsTableSchema)
					.where(
						and(
							eq(
								linkedAccountsTableSchema.NonOwnerIdType,
								this.idTokenInfo.IdType
							),
							eq(
								linkedAccountsTableSchema.NonOwnerIdValue,
								this.idTokenInfo.IdValue
							),
							eq(
								linkedAccountsTableSchema.OwnerBillAccNo,
								childTxn.BillingAccountNo ?? ''
							)
						)
					)
					.execute();

				// if the billing account is linked to the customer account, the flag IsLinkedAccount is set to true
				if (linkedAccounts.length > 0) {
					billingAccounts.push({
						BillingAccountNo: childTxn.BillingAccountNo,
						GrossAmount: childTxn.GrossAmount,
						IsLinkedAccount: true
					});
				} else {
					billingAccounts.push({
						BillingAccountNo: childTxn.BillingAccountNo,
						GrossAmount: childTxn.GrossAmount,
						IsLinkedAccount: false
					});
				}
			}
		}

		return billingAccounts;
	}
}

export default Oses;
