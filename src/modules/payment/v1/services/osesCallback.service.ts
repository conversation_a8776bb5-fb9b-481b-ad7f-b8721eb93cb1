import { format, parse } from 'date-fns';
import { eq, sql } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { ProgressStatusEnum } from '../../../../enum/order.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import type { OsesPaymentItems } from '../../../../integration/emailTemplate/schemas/api/osesTemplate.schema';
import { UE_ERROR } from '../../../../middleware/error';
import { deleteWso2LightweightBillingDetailsCache } from '../../../../shared/cache';
import OsesHelper from '../helpers/oses.helper';
import type { OsesResponse } from '../schemas/api/osesResponse.schema';
import {
	type SelectOsesTxnHistory,
	osesTxnHistoryTableSchema
} from '../schemas/db/osesTxnHistory.schema';

class OsesCallback {
	private db: NodePgDatabase;
	private integrationId: string;
	private osesHelper: OsesHelper;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
		this.osesHelper = new OsesHelper(integrationId);
	}

	async processOsesResponse(req: OsesResponse): Promise<string> {
		//await getOsesTxnPass(req.MERCHANTID);
		const signature: string = this.osesHelper.getOsesSignature(
			req.MERCHANTID,
			req.MERCHANT_TRANID,
			req.AMOUNT,
			req.TXN_STATUS
		);

		if (signature !== req.SIGNATURE) {
			throw new UE_ERROR('Invalid signature', StatusCodeEnum.FORBIDDEN_ERROR, {
				integrationId: this.integrationId,
				response: null
			});
		}

		// save oses reponse to db
		const osesResObj =
			req.ERR_CODE === '5530' ||
			req.ERR_DESC?.includes('MERCHANT_TRANID submitted already exists.')
				? {
						ErrCode: req.ERR_CODE,
						ErrDesc: req.ERR_DESC,
						UpdatedAt: sql`now()`
					}
				: {
						AcquirerBank: req.ACQUIRER_BANK,
						AuthId: req.AUTH_ID,
						BankReference: req.BANK_REFERENCE,
						BankResCode: req.BANK_RES_CODE,
						BankResMsg: req.BANK_RES_MSG,
						BankStatusDesc: req.BANK_STATUS_DESC,
						CardNoPartial: req.CARD_NO_PARTIAL,
						CardName: req.CARDNAME,
						CardType: req.CARDTYPE,
						ErrCode: req.ERR_CODE,
						ErrDesc: req.ERR_DESC,
						EUI: req.EUI,
						ExceedHighRisk: req.EXCEED_HIGH_RISK,
						FraudRiskLevel: req.FRAUDRISKLEVEL,
						FraudRiskScore: req.FRAUDRISKSCORE,
						IsBlacklisted: req.IS_BLACKLISTED,
						PaymentMethod: req.PAYMENT_METHOD,
						Description: req.DESCRIPTION,
						Signature: req.SIGNATURE,
						TxnDate: req.TRANDATE,
						TxnId: req.TRANSACTIONID,
						TxnType: req.TRANSACTIONTYPE,
						TxnStatus: req.TXN_STATUS,
						UsrCode: req.USR_CODE,
						UsrMsg: req.USR_MSG,
						WhitelistCard: req.WHITELIST_CARD,
						PortalTxnStatus:
							req.EUI === 'SUC'
								? ProgressStatusEnum.COMPLETED
								: ProgressStatusEnum.INPROGRESS,
						UpdatedAt: sql`now()`
					};

		const [result]: SelectOsesTxnHistory[] = await this.db
			.update(osesTxnHistoryTableSchema)
			.set(osesResObj)
			.where(eq(osesTxnHistoryTableSchema.MerchantTxnId, req.MERCHANT_TRANID))
			.returning()
			.catch(err => {
				throw new UE_ERROR(
					'Failed to update OSES transaction history',
					StatusCodeEnum.UE_INTERNAL_SERVER,
					{
						integrationId: this.integrationId,
						response: err
					}
				);
			});

		if (result) {
			// send payment receipt email if payment is successful
			if (req.ERR_CODE === '0' && req.EUI === 'SUC') {
				// Parse the input string into a Date object and format it to the desired pattern
				const formattedDate: string = req.TRANDATE
					? format(
							parse(req.TRANDATE, 'dd-MM-yyyy HH:mm:ss', new Date()),
							'dd-MM-yyyy hh:mm:ss aa'
						)
					: '';

				const paymentItems: OsesPaymentItems[] = [];

				for (const childTxn of result.ChildTxn) {
					if (childTxn && childTxn.Email !== '') {
						const paymentItem: OsesPaymentItems = {
							billingAccountNoLabel: 'Billing Account',
							billingAccountNo: childTxn.BillingAccountNo,
							amount: childTxn.GrossAmount
						};

						paymentItems.push(paymentItem);

						await deleteWso2LightweightBillingDetailsCache(
							childTxn.BillingAccountNo
						);

						await this.osesHelper.sendPaymentReceiptEmail(
							[paymentItem],
							req.TRANSACTIONID ?? '',
							req.PAYMENT_METHOD ?? '',
							childTxn.Email,
							childTxn?.Name ?? 'Anonymous',
							childTxn?.GrossAmount ?? '',
							formattedDate
						);
					}
				}

				if (result.PaymentType === 'PFA') {
					await this.osesHelper.sendPaymentReceiptEmail(
						paymentItems,
						req.TRANSACTIONID ?? '',
						req.PAYMENT_METHOD ?? '',
						result.PayerEmail,
						result.PayerName,
						result.Amount,
						formattedDate
					);
				}
			}

			// redirect url with param MERCHANT_TRANID & HASH (hashedSignature)
			const res: string =
				await this.osesHelper.getRedirectUrlWithHashAndMerchantTranId(
					result.SourceRedirectUrl,
					result.Signature,
					req.MERCHANT_TRANID
				);
			return res;
		}
		throw new UE_ERROR(
			`No payment record found with MERCHANT_TRANID ${req.MERCHANT_TRANID}`,
			StatusCodeEnum.NOT_FOUND_ERROR,
			{
				integrationId: this.integrationId,
				response: 'Payment record not found'
			}
		);
	}
}

export default OsesCallback;
