import { eq, sql } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { envConfig } from '../../../../config/env.config';
import { ProgressStatusEnum } from '../../../../enum/order.enum';
import { OsesPaymentTypeEnum } from '../../../../enum/payment.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import {
	LightweightFlagEnum,
	SystemNameEnum
} from '../../../../enum/wso2.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type { Wso2AccountValidationRes } from '../../../../integration/wso2/user/schemas/api/wso2AccountValidation.schema';
import type {
	Wso2CustomerAccountReq,
	Wso2CustomerAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2CustomerAccount.schema';
import { UE_ERROR } from '../../../../middleware/error';
import {
	getEnumKeyByValue,
	icpBillingProfile,
	novaBillingProfile
} from '../../../../shared/common';
import { decrypt } from '../../../../shared/encryption/aesGcm';
import type { NovaIcpBillingProfile } from '../../../../shared/schemas/api/novaBillingProfile.schema';
import OsesHelper from '../helpers/oses.helper';
import type {
	CreateOsesUrlReq,
	CreateOsesUrlRes,
	OsesBillingAccountReq,
	OsesChildTxn,
	OsesChildTxnObj
} from '../schemas/api/osesCreateUrl.schema';
import type {
	OsesStatusReq,
	OsesStatusRes,
	OsesStatusResObj
} from '../schemas/api/osesStatus.schema';
import {
	type SelectOsesTxnHistory,
	osesTxnHistoryTableSchema
} from '../schemas/db/osesTxnHistory.schema';

class OsesPreLogin {
	private db: NodePgDatabase;
	private integrationId: string;
	private mwIntegration: MwIntegration;
	private osesHelper: OsesHelper;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
		this.mwIntegration = new MwIntegration(integrationId);
		this.osesHelper = new OsesHelper(integrationId);
	}

	private async getOsesChildTxnObj(
		osesBillingAccountReq: OsesBillingAccountReq
	): Promise<OsesChildTxnObj> {
		const env = envConfig();
		const getWso2UserIntegration = this.mwIntegration.Wso2UserIntegration;
		// verify billing account number
		const wso2BAVerificationRes: Wso2AccountValidationRes =
			await getWso2UserIntegration.getWso2BAVerification(
				osesBillingAccountReq.BillingAccountNo
			);

		// WSO2 will return billing account number only in body response if it is not a valid account number
		// if there is system name in the body response, it means the account number is valid
		if (
			!wso2BAVerificationRes.Response.CustomerIdType ||
			!wso2BAVerificationRes.Response.CustomerIdNumber ||
			wso2BAVerificationRes.Response.SystemName == null ||
			wso2BAVerificationRes.Response.CustomerIdType == null ||
			wso2BAVerificationRes.Response.CustomerIdNumber == null
		) {
			throw new UE_ERROR(
				`No billing account exists with account number ${osesBillingAccountReq.BillingAccountNo}`,
				StatusCodeEnum.NOT_FOUND_ERROR,
				{
					integrationId: this.integrationId,
					response: 'WSO2 BA Verification Response is not available'
				}
			);
		}

		if (wso2BAVerificationRes.Response.SystemName.toUpperCase() === 'STELLAR') {
			throw new UE_ERROR(
				'Sorry, we are no longer support payment for Stellar products',
				StatusCodeEnum.FORBIDDEN_ERROR,
				{
					integrationId: this.integrationId,
					response: 'Product no longer exist'
				}
			);
		}

		const systemName: string = wso2BAVerificationRes.Response.SystemName;
		const amount: string = Number.parseFloat(
			osesBillingAccountReq.Amount
		).toFixed(2);
		const name: string = wso2BAVerificationRes.Response.BillName ?? 'Anonymous';
		const revenueCode: string =
			systemName === SystemNameEnum.ICP
				? env.OSES_REVENUE_CODE_ICP
				: env.OSES_REVENUE_CODE_NOVA;

		const wso2CAReq: Wso2CustomerAccountReq = {
			idType: wso2BAVerificationRes.Response.CustomerIdType,
			idValue: wso2BAVerificationRes.Response.CustomerIdNumber
		};

		const wso2CARes: Wso2CustomerAccountRes =
			await getWso2UserIntegration.getWso2CustomerAccount(
				wso2CAReq,
				LightweightFlagEnum.NO
			);

		const billingProfile: NovaIcpBillingProfile =
			systemName === SystemNameEnum.ICP
				? await icpBillingProfile(
						this.integrationId,
						wso2CAReq.idType,
						wso2CAReq.idValue,
						osesBillingAccountReq.BillingAccountNo,
						wso2CARes
					)
				: await novaBillingProfile(
						this.integrationId,
						osesBillingAccountReq.BillingAccountNo
					);

		if (
			wso2CARes.Response?.CustomerAccounts &&
			wso2CARes.Response.CustomerAccounts.length > 0
		) {
			return {
				Name: name,
				Email: billingProfile.AccountEmail,
				BillingAccountNo: osesBillingAccountReq.BillingAccountNo,
				BillNo: osesBillingAccountReq.BillNo,
				SystemName: systemName,
				RevenueCode: revenueCode,
				GrossAmount: amount,
				GbtAmount: amount,
				NettAmount: '0.00',
				MiscAmount: '0.00'
			};
		}
		throw new UE_ERROR(
			'No customer account found',
			StatusCodeEnum.NOT_FOUND_ERROR,
			{
				integrationId: this.integrationId,
				response: 'WSO2 CA Response is not available'
			}
		);
	}

	async createOsesUrl(
		source: string,
		req: CreateOsesUrlReq
	): Promise<CreateOsesUrlRes> {
		const env = envConfig();
		const osesChildTxn: OsesChildTxn = [];
		let totalAmount = '0.00';

		// sum up total amount and create OsesChildTxn object for each billing account
		for (const billing of req.BillingAccounts) {
			const osesChildTxnObj: OsesChildTxnObj =
				await this.getOsesChildTxnObj(billing);
			totalAmount = (
				Number.parseFloat(billing.Amount) + Number.parseFloat(totalAmount)
			).toFixed(2);
			osesChildTxn.push(osesChildTxnObj);
		}
		const currencyCode: string = 'MYR';
		const returnUrl: string = env.OSES_RETURN_URL;
		const totalChildTxn: string = String(req.BillingAccounts.length);
		const merchantId: string = this.osesHelper.getOsesMerchantId(
			req.IsKCIPayment,
			req.IsBillReadiness,
			source
		);
		const paymentTypeKey: string | undefined = getEnumKeyByValue(
			OsesPaymentTypeEnum,
			req.PaymentType
		);

		// save to db
		const [insertOsesTxnHistoryResult] = await this.db
			.insert(osesTxnHistoryTableSchema)
			.values({
				MerchantId: merchantId,
				PayerEmail: req.PayerEmail,
				PayerName: req.PayerName,
				Amount: totalAmount,
				PaymentType: paymentTypeKey ?? req.PaymentType,
				Source: source,
				SourceRedirectUrl: req.RedirectUrl,
				CurrencyCode: currencyCode,
				PortalTxnStatus: ProgressStatusEnum.INPROGRESS,
				Description: '',
				ReturnUrl: returnUrl,
				TotalChildTxn: totalChildTxn,
				Signature: 'test',
				ChildTxn: osesChildTxn
			})
			.returning();

		// cannot proceed without merchantTxnId
		if (!insertOsesTxnHistoryResult.MerchantTxnId) {
			throw new UE_ERROR(
				'An unexpected error occurred. Please try again later.',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId, response: null }
			);
		}

		const merchantTxnId = insertOsesTxnHistoryResult.MerchantTxnId;

		//  get encrypted signature and save to db
		const signature: string = this.osesHelper.getOsesSignature(
			merchantId,
			merchantTxnId,
			totalAmount,
			''
		);

		await this.db
			.update(osesTxnHistoryTableSchema)
			.set({
				Signature: signature,
				UpdatedAt: sql`now()`
			})
			.where(eq(osesTxnHistoryTableSchema.MerchantTxnId, merchantTxnId))
			.returning();

		// save outstanding amount to db for each child transaction
		await this.osesHelper.saveOutstandingAmount(
			osesChildTxn,
			source,
			merchantTxnId
		);

		// generate XML for child transactions
		const pgOsesChildTxn: string = await this.osesHelper.getOsesChildTxnInXML(
			merchantTxnId,
			merchantId,
			osesChildTxn
		);

		const res: CreateOsesUrlRes = {
			Success: true,
			Code: StatusCodeEnum.CREATED,
			IntegrationId: this.integrationId,
			Response: {
				LANG: 'en',
				MERCHANTID: merchantId,
				MERCHANT_TRANID: merchantTxnId,
				CURRENCYCODE: currencyCode,
				AMOUNT: totalAmount,
				CUSTNAME: req.PayerName,
				CUSTEMAIL: req.PayerEmail,
				RETURN_URL: returnUrl,
				SIGNATURE: signature,
				TOTAL_CHILD_TRANSACTION: totalChildTxn,
				CHILD_TRANSACTION: pgOsesChildTxn,
				OSES_URL: env.OSES_URL,
				PYMT_IND: 'tokenization;tokenization_ref',
				PYMT_CRITERIA: `payment;${req.PayerEmail}`,
				DESCRIPTION: 'payment MyUnifi'
			}
		};

		return res;
	}

	async getPaymentStatus(req: OsesStatusReq): Promise<OsesStatusRes> {
		let decryptedHash = null;
		if (req.EncryptedHash) {
			decryptedHash = await decrypt(req.EncryptedHash);
		}

		const result: SelectOsesTxnHistory[] = await this.db
			.select()
			.from(osesTxnHistoryTableSchema)
			.where(eq(osesTxnHistoryTableSchema.MerchantTxnId, req.MerchantTxnId));
		if (result.length === 0) {
			throw new UE_ERROR(
				`No payment record found with ${req.MerchantTxnId}`,
				StatusCodeEnum.NOT_FOUND_ERROR,
				{
					integrationId: this.integrationId,
					response: 'Payment record not found'
				}
			);
		}
		if (decryptedHash && result[0].Signature !== decryptedHash) {
			throw new UE_ERROR(
				'Not allowed to access this resource!',
				StatusCodeEnum.FORBIDDEN_ERROR,
				{
					integrationId: this.integrationId,
					response: 'Access forbidden for this resource'
				}
			);
		}

		const paymentMethod: string = this.osesHelper.getOsesPaymentMethod(
			result[0].PaymentMethod ?? ''
		);

		const txnStatus: string = this.osesHelper.getOsesTxnStatus(
			result[0].TxnStatus
		);

		// Note: PFA payment type allows user to pay for only one billing account,
		// if the billing account is linked to the customer account, the flag IsLinkedAccount is set to true
		const billingAccounts = result[0].ChildTxn.map(childTxn => {
			return {
				BillingAccountNo: childTxn.BillingAccountNo,
				GrossAmount: childTxn.GrossAmount
			};
		});

		const osesStatusResObj: OsesStatusResObj = {
			MerchantTxnId: result[0].MerchantTxnId,
			MerchantId: result[0].MerchantId,
			PayerEmail: result[0].PayerEmail,
			PayerName: result[0].PayerName,
			BankReference: result[0].BankReference,
			Source: result[0].Source,
			TotalAmount: result[0].Amount,
			PaymentType: result[0].PaymentType,
			PaymentMethod: paymentMethod,
			TxnDate: result[0].TxnDate,
			TxnStatus: txnStatus,
			TxnId: result[0].TxnId,
			BillingAccounts: billingAccounts
		};

		const res: OsesStatusRes = {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				TransactionStatus: osesStatusResObj
			}
		};
		return res;
	}
}

export default OsesPreLogin;
