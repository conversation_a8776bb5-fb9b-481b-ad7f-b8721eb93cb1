import { and, desc, eq, ilike, or } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { envConfig } from '../../../../config/env.config';
import { pinoLog } from '../../../../config/pinoLog.config';
import { EmailEnum } from '../../../../enum/notification.enum';
import { AutopayReqTypeEnum } from '../../../../enum/payment.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import {
	LightweightFlagEnum,
	SystemNameEnum
} from '../../../../enum/wso2.enum';
import type { AutopayEmailTemplateReq } from '../../../../integration/emailTemplate/schemas/api/autopayTemplate.schema';
import { MwIntegration } from '../../../../integration/mw.integration';
import type { Wso2EmailReq } from '../../../../integration/wso2/notification/schemas/api/wso2Email.schema';
import type {
	Wso2EnableAutopayReq,
	Wso2EnableAutopayRes
} from '../../../../integration/wso2/payment/schemas/api/wso2AutopayEnable.schema';
import type {
	Wso2CheckPaymentSRStatusReq,
	Wso2CheckPaymentSRStatusRes
} from '../../../../integration/wso2/payment/schemas/api/wso2PaymentSRStatus.schema';
import type {
	Wso2CustomerAccountReq,
	Wso2CustomerAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2CustomerAccount.schema';
import type {
	Wso2NovaBillingProfileReq,
	Wso2NovaBillingProfileRes
} from '../../../../integration/wso2/user/schemas/api/wso2NovaBillingProfile.schema';
import type {
	Wso2ServiceAccountReq,
	Wso2ServiceAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2ServiceAccount.schema';
import { UE_ERROR } from '../../../../middleware/error';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import {
	deleteWso2CustomerAccountCache,
	deleteWso2NovaBillingProfileCache
} from '../../../../shared/cache';
import { decrypt, encrypt } from '../../../../shared/encryption/aesGcm';
import { hashHex } from '../../../../shared/encryption/hash';
import type { GetAutopayDetailsRes } from '../schemas/api/autopayDetails.schema';
import type {
	AutopaySettingReq,
	AutopaySettingRes
} from '../schemas/api/autopaySetting.schema';
import type {
	CheckSrStatusReq,
	CheckSrStatusRes
} from '../schemas/api/autopaySrStatus.schema';
import type { BankListRes } from '../schemas/api/bankList.schema';
import type { AddBankReq, AddBankRes } from '../schemas/api/newBank.schema';
import {
	type SelectAutopaySettingHistory,
	autopaySettingHistoryTableSchema
} from '../schemas/db/autopaySettingHistory.schema';
import {
	type InsertBankList,
	type SelectBankList,
	bankListTableSchema
} from '../schemas/db/bankList.schema';

class Autopay {
	private db: NodePgDatabase;
	private integrationId: string;
	private mwIntegration: MwIntegration;
	private idTokenInfo: IdTokenInfo;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
		this.mwIntegration = new MwIntegration(integrationId);
		this.idTokenInfo = idTokenInfo;
	}

	private async verifyServiceAccount(
		billingAccountNo: string
	): Promise<boolean> {
		const getWso2UserIntegration = this.mwIntegration.Wso2UserIntegration;
		const wso2CAReq: Wso2CustomerAccountReq = {
			idType: this.idTokenInfo.IdType,
			idValue: this.idTokenInfo.IdValue
		};

		const wso2CARes: Wso2CustomerAccountRes =
			await getWso2UserIntegration.getWso2CustomerAccount(
				wso2CAReq,
				LightweightFlagEnum.YES
			);

		for (const ca of wso2CARes.Response?.CustomerAccounts ?? []) {
			for (const ba of ca?.BillingAccounts ?? []) {
				if (ba && ba.AccountNumber === billingAccountNo && ca?.SystemName) {
					const wso2SAReq: Wso2ServiceAccountReq = {
						idType: this.idTokenInfo.IdType,
						idValue: this.idTokenInfo.IdValue,
						BillingAccountNo: ba.AccountNumber,
						SystemName: ca.SystemName
					};
					const wso2SARes: Wso2ServiceAccountRes =
						(await getWso2UserIntegration.getWso2ServiceAccount(
							wso2SAReq,
							LightweightFlagEnum.YES
						)) as Wso2ServiceAccountRes;

					for (const sa of wso2SARes?.Response?.ServiceAccount ?? []) {
						if (
							(sa?.Products && sa.Products.length > 0) ||
							(ca.SystemName === SystemNameEnum.NOVA && sa && sa.ServiceID)
						) {
							return true;
						}
					}
				}
			}
		}

		return false;
	}

	private maskValue(value: string | undefined): string | null {
		if (!value) {
			return null;
		}
		return value.slice(0, -4).replace(/./g, '#') + value.slice(-4);
	}

	private async getAutopayEmailBody(
		requestType: string,
		accountName: string,
		accountNumber: string,
		refNo: string
	): Promise<string> {
		const env = envConfig();
		const baseUrl: string =
			requestType === AutopayReqTypeEnum.REGISTRATION
				? env.AUTOPAY_REGISTRATION_TEMPLATE
				: requestType === AutopayReqTypeEnum.MODIFICATION
					? env.AUTOPAY_MODIFICATION_TEMPLATE
					: env.AUTOPAY_TERMINATION_TEMPLATE;

		const autopayEmailTemplateReq: AutopayEmailTemplateReq = {
			name: accountName,
			accountName: accountName,
			accountNumber: accountNumber,
			refNo: refNo
		};

		const params = new URLSearchParams(autopayEmailTemplateReq);
		const queryString: string = params.toString();
		const emailUrl: string = `${baseUrl}?${queryString}`;
		const emailBody: string =
			await this.mwIntegration.EmailTemplateIntegration.getEmailBodyTemplate(
				emailUrl
			);
		return emailBody;
	}

	async getBankList(): Promise<BankListRes> {
		const result: SelectBankList[] = await this.db
			.select()
			.from(bankListTableSchema)
			.execute();
		const res: BankListRes = {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: result
		};
		return res;
	}

	async addBank(req: AddBankReq): Promise<AddBankRes> {
		const isBankExist: SelectBankList[] = await this.db
			.select()
			.from(bankListTableSchema)
			.where(
				or(
					ilike(bankListTableSchema.BankName, req.BankName),
					eq(bankListTableSchema.BankCode, req.BankCode)
				)
			)
			.execute();
		if (isBankExist.length > 0) {
			throw new UE_ERROR('Bank already exists', StatusCodeEnum.CONFLICT, {
				integrationId: this.integrationId,
				response: null
			});
		}

		const insertionRequest: InsertBankList = {
			BankName: req.BankName,
			BankCode: req.BankCode,
			Flag: ['Payment']
		};

		const result: InsertBankList[] = await this.db
			.insert(bankListTableSchema)
			.values(insertionRequest)
			.returning();

		if (result.length === 0) {
			throw new UE_ERROR(
				'Failed to add bank to db table',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId, response: null }
			);
		}

		const res: AddBankRes = {
			Success: true,
			Code: StatusCodeEnum.CREATED,
			IntegrationId: this.integrationId,
			Response: {
				Action: 'ADDED',
				Message: 'Bank successfully added'
			}
		};
		return res;
	}

	async autopaySetting(req: AutopaySettingReq): Promise<AutopaySettingRes> {
		const decryptedBillAccNo: string = await decrypt(req.EncryptedBillAccNo);
		// Hash the billing account number
		const hashedBillAccNo: string = hashHex(decryptedBillAccNo);
		const { CcNumber, IsEncryptedCCNumber } = req.CreditCardInfo ?? {};
		// Decrypt or assign the credit card number
		const ccNumber =
			IsEncryptedCCNumber && CcNumber ? await decrypt(CcNumber) : CcNumber;
		// Encrypt the credit card number (only if not already encrypted and CcNumber exists)
		const encryptedCCNumber =
			!IsEncryptedCCNumber && CcNumber ? await encrypt(CcNumber) : CcNumber;

		const isAccExist: boolean =
			await this.verifyServiceAccount(decryptedBillAccNo);

		if (!isAccExist) {
			throw new UE_ERROR(
				'Account does not exist',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{
					integrationId: this.integrationId,
					response: null
				}
			);
		}

		// submit request to wso2
		const wso2AutopayReq: Wso2EnableAutopayReq =
			(req.RequestType === AutopayReqTypeEnum.REGISTRATION ||
				req.RequestType === AutopayReqTypeEnum.MODIFICATION) &&
			req.CreditCardInfo
				? {
						SystemName: req.SystemName,
						RequestType: req.RequestType,
						BillingAccountNo: decryptedBillAccNo,
						idType: this.idTokenInfo.IdType,
						idValue: this.idTokenInfo.IdValue,
						GetCreditCardInfoRequest: {
							ccCardType: req.CreditCardInfo.CcType,
							ccNumber: ccNumber,
							ccIssuerBank: req.CreditCardInfo.CcIssuerBank,
							ccSiebelIssuerBankCode: req.CreditCardInfo.CcIssuerBank,
							ccIssuerBankName: req.CreditCardInfo.CcIssuerBankName,
							ccExpiryMonth: req.CreditCardInfo.CcExpiryMonth,
							ccExpiryYear: req.CreditCardInfo.CcExpiryYear,
							ccHolderName: req.CreditCardInfo.CcHolderName,
							ccOwnerIdType: req.CreditCardInfo.CcOwnerIdType,
							ccOwnIdVal: req.CreditCardInfo.CcOwnerIdValue,
							ccOwnRelAcc: req.CreditCardInfo.CcOwnerCustomerRelation,
							contactId: null,
							emailAddress: req.EmailAddress
						}
					}
				: {
						SystemName: req.SystemName,
						RequestType: req.RequestType,
						BillingAccountNo: decryptedBillAccNo,
						idType: this.idTokenInfo.IdType,
						idValue: this.idTokenInfo.IdValue,
						GetCreditCardInfoRequest: {}
					};

		const wso2AutopayRes: Wso2EnableAutopayRes =
			await this.mwIntegration.Wso2PaymentIntegration.getWso2SettingAutopay(
				wso2AutopayReq
			);
		if (!wso2AutopayRes.Response) {
			throw new UE_ERROR(
				`Failed to execute ${req.RequestType} autopay request. Kindly check your personal and credit card information`,
				StatusCodeEnum.NOT_ACCEPTABLE_ERROR,
				{
					integrationId: this.integrationId,
					response: JSON.stringify(wso2AutopayRes)
				}
			);
		}

		const srNumber: string =
			wso2AutopayRes.Response.ListOfTmEaiSrPayMethCreateReq
				.TmServiceRequestIntegration.SRNumber;

		// send email
		const emailSubject: string =
			req.RequestType === AutopayReqTypeEnum.REGISTRATION
				? EmailEnum.AUTOPAY_REGISTRATION_SUBJECT
				: req.RequestType === AutopayReqTypeEnum.MODIFICATION
					? EmailEnum.AUTOPAY_MODIFICATION_SUBJECT
					: EmailEnum.AUTOPAY_TERMINATION_SUBJECT;

		const emailBody: string = await this.getAutopayEmailBody(
			req.RequestType,
			req.BillingAccountName,
			decryptedBillAccNo,
			srNumber
		);

		const wso2EmailReq: Wso2EmailReq = {
			to: req.EmailAddress,
			from: EmailEnum.FROM_NOREPLY,
			subject: emailSubject,
			body: emailBody
		};

		const isEmailSent: boolean =
			emailBody !== ''
				? await this.mwIntegration
						.getWso2NotificationIntegration()
						.getWso2SendEmail(wso2EmailReq, srNumber, 'Autopay Setting')
				: false;

		// save to db
		await this.db
			.insert(autopaySettingHistoryTableSchema)
			.values({
				IdType: this.idTokenInfo.IdType,
				MaskedIdValue: this.maskValue(this.idTokenInfo.IdValue),
				EncryptedBillAccNo: hashedBillAccNo,
				SystemName: req.SystemName,
				RequestType: req.RequestType,
				SRNumber: srNumber,
				CcType: wso2AutopayReq.GetCreditCardInfoRequest.ccCardType ?? null,
				EncryptedCCNumber: encryptedCCNumber ?? null,
				CcIssuerBank:
					wso2AutopayReq.GetCreditCardInfoRequest.ccIssuerBank ?? null,
				CcIssuerBankName:
					wso2AutopayReq.GetCreditCardInfoRequest.ccIssuerBankName ?? null,
				CcExpiryMonth:
					wso2AutopayReq.GetCreditCardInfoRequest.ccExpiryMonth ?? null,
				CcExpiryYear:
					wso2AutopayReq.GetCreditCardInfoRequest.ccExpiryYear ?? null,
				CcHolderName:
					wso2AutopayReq.GetCreditCardInfoRequest.ccHolderName ?? null,
				CcOwnerIdType:
					wso2AutopayReq.GetCreditCardInfoRequest.ccOwnerIdType ?? null,
				MaskedCcOwnerIdValue: this.maskValue(
					wso2AutopayReq.GetCreditCardInfoRequest.ccOwnIdVal
				),
				CcOwnerCustomerRelation:
					wso2AutopayReq.GetCreditCardInfoRequest.ccOwnRelAcc ?? null,
				EmailAddress: req.EmailAddress,
				IsEmailSent: isEmailSent
			})
			.returning()
			.catch(error => {
				pinoLog.error(error);
				return [];
			});

		// delete cached data
		await deleteWso2NovaBillingProfileCache(decryptedBillAccNo);
		await deleteWso2CustomerAccountCache(
			decryptedBillAccNo,
			LightweightFlagEnum.NO
		);

		// request updated customer account data background
		this.mwIntegration.Wso2UserIntegration.getWso2CustomerAccount(
			{
				idType: this.idTokenInfo.IdType,
				idValue: this.idTokenInfo.IdValue
			},
			LightweightFlagEnum.NO,
			false
		);

		const res: AutopaySettingRes = {
			Success: true,
			Code: StatusCodeEnum.CREATED,
			IntegrationId: this.integrationId,
			Response: {
				SRNumber: srNumber
			}
		};

		return res;
	}

	async checkSrStatus(req: CheckSrStatusReq): Promise<CheckSrStatusRes> {
		const decryptedBillingAccNo: string = await decrypt(req.EncryptedBillAccNo);
		const wso2Req: Wso2CheckPaymentSRStatusReq = {
			idType: this.idTokenInfo.IdType,
			idValue: this.idTokenInfo.IdValue,
			BillingAccountNo: decryptedBillingAccNo,
			SystemName: req.SystemName
		};

		const wso2Res: Wso2CheckPaymentSRStatusRes =
			await this.mwIntegration.Wso2PaymentIntegration.getWso2AutopayCheckPaymentSRStatus(
				wso2Req
			);

		const srNumber: string | null =
			wso2Res.Response.ListOfTmEaiSrPayMethCreateReq
				?.TmServiceRequestIntegration?.SRNumber || null;

		const res: CheckSrStatusRes = {
			Success: true,
			Code: StatusCodeEnum.OK,
			Message: wso2Res.Status.Message,
			IntegrationId: this.integrationId,
			Response: {
				SRNumber: srNumber
			}
		};
		return res;
	}

	async getAutopayDetails(
		encryptedBillAccNo: string
	): Promise<GetAutopayDetailsRes> {
		const decryptedBillAccNo: string = await decrypt(encryptedBillAccNo);
		const wso2Req: Wso2NovaBillingProfileReq = {
			RetrieveBillingProfileRequest: {
				BillingAccountNo: decryptedBillAccNo
			}
		};
		const wso2Res =
			(await this.mwIntegration.Wso2UserIntegration.getWso2NovaBillingProfile(
				wso2Req
			)) as Wso2NovaBillingProfileRes;

		const profile =
			wso2Res.RetrieveBillingProfileResponse.BillingProfileDetails
				.BillingAccounts.InvoiceProfileDetails.InvoiceProfile;

		// hashed the billing account number
		const hashedBillAccNo: string = hashHex(decryptedBillAccNo);

		const result: SelectAutopaySettingHistory[] = await this.db
			.select()
			.from(autopaySettingHistoryTableSchema)
			.where(
				and(
					eq(
						autopaySettingHistoryTableSchema.EncryptedBillAccNo,
						hashedBillAccNo
					)
				)
			)
			.orderBy(desc(autopaySettingHistoryTableSchema.CreatedAt))
			.limit(1);

		const requestType = result.length > 0 ? result[0].RequestType : null;
		let encryptedCardNo =
			result.length > 0 ? result[0].EncryptedCCNumber : null;
		// Check if the encrypted card number is matched to the card number in NOVA
		if (encryptedCardNo && profile.CardNumber) {
			const firstSix = profile.CardNumber.slice(0, 6);
			const lastFour = profile.CardNumber.slice(-4);
			const decryptedCardNo = await decrypt(encryptedCardNo);
			if (
				decryptedCardNo.slice(0, 6) !== firstSix ||
				decryptedCardNo.slice(-4) !== lastFour
			) {
				encryptedCardNo = null;
			}
		}

		const res: GetAutopayDetailsRes = {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				EncryptedCardNo: encryptedCardNo,
				MaskedCardNo: (profile.CardNumber ?? '').replace(/.(?=.{4,9}$)/g, '*'),
				CardType: profile.CardType,
				BankName: profile.BankName,
				CardExpiryDate: `${profile.CardExpiryDateMonth}/${profile.CardExpiryDateYear}`,
				OwnerName: profile.CardHolderName,
				IdType: profile.DDBankAccountHolderIdTypeNew,
				IdValue: profile.DDBankAccountHolderIdNew,
				Relationship: profile.DDRelationship,
				EmailAddress:
					result.length > 0 && requestType !== AutopayReqTypeEnum.TERMINATION
						? result[0].EmailAddress
						: null
			}
		};
		return res;
	}
}

export default Autopay;
