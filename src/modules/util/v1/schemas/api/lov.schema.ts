import { type Static, t } from 'elysia';
import { LovTypeEnum, TnpsRateBoundEnum } from '../../../../../enum/lov.enum';
import { SystemNameEnum } from '../../../../../enum/wso2.enum';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const lovListReqSchema = t.Object({
	Type: t.Enum(LovTypeEnum),
	SystemName: t.Enum(SystemNameEnum)
});

export type LovListReq = Static<typeof lovListReqSchema>;

const lovObj = t.Object({
	Key: t.String({
		examples: ['Cost Saving'],
		description: 'For type Complaint, this will be mapped to CategoryKey'
	}),
	Value: t.String({
		examples: ['I want to save cost'],
		description: 'For type Complaint, this will be mapped to CategoryValue'
	}),
	ComplaintType: t.Optional(
		t.String({
			examples: ['Billing'],
			description: 'Only used for type Complaint'
		})
	),
	SubCategoryKey: t.Optional(
		t.Nullable(
			t.String({
				examples: ['CPE Charge'],
				description: 'Only used for type Complaint'
			})
		)
	),
	SubCategoryValue: t.Optional(
		t.Nullable(
			t.String({
				examples: ['Equipment charge'],
				description: 'Only used for type Complaint'
			})
		)
	),
	GroupNameKey: t.Optional(
		t.Nullable(
			t.String({
				examples: ['WIDER_TMUC_TECH_ACG'],
				description: 'Only used for type Complaint'
			})
		)
	),
	GroupNameValue: t.Optional(
		t.Nullable(
			t.String({
				examples: ['WIDER_TMUC_TECH_ACG'],
				description: 'Only used for type Complaint'
			})
		)
	)
});

const lovArray = t.Array(lovObj);

export type LovArray = Static<typeof lovArray>;

export const lovListResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: lovArray
	},
	{ description: 'LOV list successfully retrieved.' }
);

export type LovListRes = Static<typeof lovListResSchema>;

export const tnpsRateLovReqSchema = t.Object({
	BoundType: t.Enum(TnpsRateBoundEnum)
});

export type TnpsRateLovReq = Static<typeof tnpsRateLovReqSchema>;

export const tnpsRateLovResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Array(
			t.Object({
				BoundType: t.String({
					examples: ['UPPER'],
					description: 'Follows the enum of the request'
				}),
				Question: t.String({
					examples: ['What is the reason for this rating?']
				}),
				Reason: t.String({ examples: ['Easy Payment Steps'] })
			})
		)
	},
	{
		description: 'TNPS LOV list successfully retrieved.'
	}
);

export type TnpsRateLovRes = Static<typeof tnpsRateLovResSchema>;
