import {
	boolean,
	integer,
	pgTable,
	text,
	timestamp
} from 'drizzle-orm/pg-core';

export const maintenancePageTableSchema = pgTable('maintenance_page', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	ContentId: text('content_id').notNull(),
	ContentName: text('content_name').notNull(),
	ActionType: text('action_type').notNull(),
	RedirectUrl: text('redirect_url'),
	Platform: text('platform').notNull(),
	Mode: text('mode').notNull(),
	IsUp: boolean('is_up').notNull(),
	Ettr: timestamp('ettr', { mode: 'date' }).notNull(),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectMaintenancePage =
	typeof maintenancePageTableSchema.$inferSelect;
