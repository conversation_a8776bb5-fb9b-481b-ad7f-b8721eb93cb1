import {
	boolean,
	integer,
	pgTable,
	text,
	timestamp
} from 'drizzle-orm/pg-core';

export const forceUpdateTableSchema = pgTable('force_update', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	Version: text('version').notNull(),
	ReleaseDate: timestamp('release_date', { mode: 'date' })
		.defaultNow()
		.notNull(),
	OsType: text('os_type').notNull(),
	TemporaryForceUpdate: boolean('temporary_force_update'),
	ForceUpdate: boolean('force_update').notNull().default(false),
	CreatedBy: text('created_by').notNull(),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectForceUpdate = typeof forceUpdateTableSchema.$inferSelect;
