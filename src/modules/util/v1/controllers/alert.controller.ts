import { randomUUID } from 'node:crypto';
import Elysia from 'elysia';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type UnifiAlertsRes,
	unifiAlertsResSchema
} from '../schemas/api/alert.schema';
import Alert from '../services/alert.service';

const privateAlertV1Routes = new Elysia({ prefix: '/alert' })
	.resolve(() => {
		return {
			Alert: new Alert(randomUUID())
		};
	})
	.get(
		'/network',
		async (ctx): Promise<UnifiAlertsRes> => {
			return await ctx.Alert.getUnifiAlerts();
		},
		{
			detail: {
				description:
					'Retrieve list of network alerts. <br><br> <b>Table: </b> alert_main, alert_main_rels, alert_service_lov, alert_status_lov, alert_area_lov, alert_cause_lov',
				tags: ['Util']
			},
			response: {
				200: unifiAlertsResSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export default privateAlertV1Routes;
