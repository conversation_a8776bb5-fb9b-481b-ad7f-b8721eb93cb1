import { randomUUID } from 'node:crypto';
import Elysia, { t } from 'elysia';
import { SourceEnum } from '../../../../enum/header.enum';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type ForceUpdateRes,
	type MaintenancePageRes,
	forceUpdateReqSchema,
	forceUpdateResSchema,
	maintenancePageResSchema
} from '../schemas/api/config.schema';
import Config from '../services/config.service';

export const protectedConfigV1Routes = new Elysia({ prefix: '/config' })
	.resolve(() => {
		return {
			Config: new Config(randomUUID())
		};
	})
	.get(
		'/maintenance-page',
		async (ctx): Promise<MaintenancePageRes> => {
			return await ctx.Config.getMaintenancePage(ctx.headers.source);
		},
		{
			detail: {
				description:
					'Retrieve list of maintenance page status. Kindly ensure only UNIFI-APP and UNIFI-PORTAL is used in source header. <br><br> <b>Table: </b> maintenance_page',
				tags: ['Util']
			},
			headers: t.Object({
				'x-api-key': t.String(),
				source: t.Enum(SourceEnum),
				segment: t.String()
			}),
			response: {
				200: maintenancePageResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.get(
		'/force-update',
		async (ctx): Promise<ForceUpdateRes> => {
			return await ctx.Config.getForceUpdate(ctx.query.OsType);
		},
		{
			detail: {
				description:
					'Retrieve list of version updates. <br><br> <b>Table: </b> force_update',
				tags: ['Util']
			},
			query: forceUpdateReqSchema,
			response: {
				200: forceUpdateResSchema,
				500: errorBaseResponseSchema
			}
		}
	);
