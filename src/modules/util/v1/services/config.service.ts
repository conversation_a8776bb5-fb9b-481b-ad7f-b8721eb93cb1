import { and, desc, eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { SourceEnum } from '../../../../enum/header.enum';
import type { OsTypeEnum } from '../../../../enum/lov.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { UE_ERROR } from '../../../../middleware/error';
import type {
	ForceUpdateRes,
	MaintenancePageRes
} from '../schemas/api/config.schema';
import {
	type SelectForceUpdate,
	forceUpdateTableSchema
} from '../schemas/db/forceUpdate.schema';
import {
	type SelectMaintenancePage,
	maintenancePageTableSchema
} from '../schemas/db/maintenancePage.schema';

class Config {
	private db: NodePgDatabase;
	private integrationId: string;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
	}

	async getMaintenancePage(source: SourceEnum): Promise<MaintenancePageRes> {
		const res: MaintenancePageRes = {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: []
		};

		let platform = 'APP';
		if (source === SourceEnum.UNIFI_PORTAL) {
			platform = 'WEB';
		}

		const maintenancePage: SelectMaintenancePage[] = await this.db
			.select()
			.from(maintenancePageTableSchema)
			.where(eq(maintenancePageTableSchema.Platform, platform));

		if (maintenancePage.length > 0) {
			for (const page of maintenancePage) {
				const pageObj = {
					ContentId: page.ContentId,
					ContentName: page.ContentName,
					ActionType: page.ActionType,
					RedirectUrl: page.RedirectUrl,
					Platform: page.Platform,
					Mode: page.Mode,
					IsUp: page.IsUp,
					Ettr: page.Ettr
				};
				res.Response.push(pageObj);
			}

			return res;
		}
		throw new UE_ERROR(
			'No maintenance page found.',
			StatusCodeEnum.NOT_FOUND_ERROR,
			{
				integrationId: this.integrationId,
				response: null
			}
		);
	}

	async getForceUpdate(osType: OsTypeEnum): Promise<ForceUpdateRes> {
		const forceUpdateData: SelectForceUpdate[] = await this.db
			.select()
			.from(forceUpdateTableSchema)
			.where(
				and(
					eq(forceUpdateTableSchema.OsType, osType.toString()),
					eq(forceUpdateTableSchema.ForceUpdate, true)
				)
			)
			.orderBy(desc(forceUpdateTableSchema.CreatedAt))
			.limit(1);

		return {
			Success: true,
			Code: 200,
			IntegrationId: this.integrationId,
			Response: {
				Version: forceUpdateData[0].Version,
				ReleaseDate: forceUpdateData[0].ReleaseDate,
				OsType: osType,
				TemporaryForceUpdate: forceUpdateData[0].TemporaryForceUpdate,
				ForceUpdate: forceUpdateData[0].ForceUpdate,
				CreatedAt: forceUpdateData[0].CreatedAt
			}
		};
	}
}

export default Config;
