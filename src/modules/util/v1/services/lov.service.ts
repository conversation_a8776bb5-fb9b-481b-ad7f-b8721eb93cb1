import { asc, eq, sql } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { LovTypeEnum, type TnpsRateBoundEnum } from '../../../../enum/lov.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { bankListTableSchema } from '../../../payment/v1/schemas/db/bankList.schema';
import type {
	LovArray,
	LovListReq,
	LovListRes,
	TnpsRateLovRes
} from '../schemas/api/lov.schema';
import { addressLovTableSchema } from '../schemas/db/addressLov.schema';
import { complaintLovTableSchema } from '../schemas/db/complaintLov.schema';
import { terminationLovTableSchema } from '../schemas/db/terminationLov.schema';
import {
	type SelectTnpsRateLov,
	tnpsRateLovTableSchema
} from '../schemas/db/tnpsRateLov.schema';

class Lov {
	private db: NodePgDatabase;
	private integrationId: string;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
	}

	async getLovList(req: LovListReq): Promise<LovListRes> {
		const lovArray: LovArray = [];
		switch (req.Type) {
			case LovTypeEnum.TERMINATION: {
				const terminationLov = await this.db
					.select({
						Key: terminationLovTableSchema.Key,
						Value: terminationLovTableSchema.Value
					})
					.from(terminationLovTableSchema)
					.where(eq(terminationLovTableSchema.SystemName, req.SystemName));

				lovArray.push(...terminationLov);
				break;
			}
			case LovTypeEnum.COMPLAINT: {
				const complaintLov = await this.db
					.select({
						ComplaintType: complaintLovTableSchema.ComplaintType,
						Key: complaintLovTableSchema.CategoryKey,
						Value: complaintLovTableSchema.CategoryValue,
						SubCategoryKey: complaintLovTableSchema.SubcategoryKey,
						SubCategoryValue: complaintLovTableSchema.SubcategoryValue,
						GroupNameKey: complaintLovTableSchema.GroupNameKey,
						GroupNameValue: complaintLovTableSchema.GroupNameValue
					})
					.from(complaintLovTableSchema)
					.where(eq(complaintLovTableSchema.SystemName, req.SystemName));
				lovArray.push(...complaintLov);
				break;
			}
			case LovTypeEnum.BANK_PAYMENT: {
				const bankList = await this.db
					.select({
						Key: sql`${bankListTableSchema.BankCode}`.mapWith(String),
						Value: bankListTableSchema.BankName
					})
					.from(bankListTableSchema)
					.where(sql`flag::jsonb @> '"Payment"'`)
					.orderBy(asc(bankListTableSchema.BankName));
				lovArray.push(...bankList);
				break;
			}
			case LovTypeEnum.BANK_REFUND: {
				const bankList = await this.db
					.select({
						Key: sql`${bankListTableSchema.BankCode}`.mapWith(String),
						Value: bankListTableSchema.BankName
					})
					.from(bankListTableSchema)
					.where(sql`flag::jsonb @> '"Refund"'`)
					.orderBy(asc(bankListTableSchema.BankName));
				lovArray.push(...bankList);
				break;
			}
			default: {
				const addressLov = await this.db
					.select({
						Key: addressLovTableSchema.Type,
						Value: addressLovTableSchema.Value
					})
					.from(addressLovTableSchema);
				lovArray.push(...addressLov);
				break;
			}
		}

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: lovArray
		};
	}

	async getTnpsRateLov(BoundType: TnpsRateBoundEnum): Promise<TnpsRateLovRes> {
		const res: TnpsRateLovRes = {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: []
		};
		const tnpsLov: SelectTnpsRateLov[] = await this.db
			.select()
			.from(tnpsRateLovTableSchema)
			.where(eq(tnpsRateLovTableSchema.BoundType, BoundType.toString()));
		if (tnpsLov.length > 0) {
			for (const lov of tnpsLov) {
				const tnpsLovObj = {
					BoundType: lov.BoundType,
					Question: lov.Question,
					Reason: lov.Reason
				};
				res.Response.push(tnpsLovObj);
			}
		}
		return res;
	}
}

export default Lov;
