import { subMonths } from 'date-fns';
import { and, eq, gte } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import type {
	FormattedAlert,
	UnifiAlertsRes,
	UnifiAlertsResObject
} from '../schemas/api/alert.schema';
import {
	type SelectAlertAreaLov,
	alertAreaLovTableSchema
} from '../schemas/db/alertAreaLov.schema';
import {
	type SelectAlertCauseLov,
	alertCauseLovTableSchema
} from '../schemas/db/alertCauseLov.schema';
import { alertMainTableSchema } from '../schemas/db/alertMainLov.schema';
import { alertMainRelsTableSchema } from '../schemas/db/alertMainRelsLov.schema';
import {
	type SelectAlertServiceLov,
	alertServiceLovTableSchema
} from '../schemas/db/alertServiceLov.schema';
import {
	type SelectAlertStatusLov,
	alertStatusLovTableSchema
} from '../schemas/db/alertStatusLov.schema';

class Alert {
	private db: NodePgDatabase;
	private integrationId: string;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
	}

	async getUnifiAlerts(): Promise<UnifiAlertsRes> {
		// Get LOV and alerts
		const alertStatusLov: SelectAlertStatusLov[] = await this.db
			.select()
			.from(alertStatusLovTableSchema);
		const alertServiceLov: SelectAlertServiceLov[] = await this.db
			.select()
			.from(alertServiceLovTableSchema);
		const alertCauseLov: SelectAlertCauseLov[] = await this.db
			.select()
			.from(alertCauseLovTableSchema);
		const alertAreaLov: SelectAlertAreaLov[] = await this.db
			.select()
			.from(alertAreaLovTableSchema);
		const rawAlerts = await this.db
			.select()
			.from(alertMainTableSchema)
			.leftJoin(
				alertMainRelsTableSchema,
				eq(alertMainTableSchema.Id, alertMainRelsTableSchema.ParentId)
			)
			.where(
				and(
					gte(alertMainTableSchema.CreatedAt, subMonths(new Date(), 3)), // 3 months before today
					eq(alertMainTableSchema.Displayed, true)
				)
			);

		// Transform the results into the desired format
		const alertMap = new Map();
		for (const row of rawAlerts) {
			const alertId = row.alert_main.Id;
			if (!alertMap.has(alertId)) {
				alertMap.set(alertId, {
					...row.alert_main,
					alert_main_rels: []
				});
			}
			if (row.alert_main_rels) {
				alertMap.get(alertId).alert_main_rels.push(row.alert_main_rels);
			}
		}
		const unifiAlerts: FormattedAlert = Array.from(alertMap.values());

		// Set Response
		let serviceImpacted: string[] = [];
		let areaAffected: string[] = [];
		const res: UnifiAlertsRes = {
			Success: true,
			Code: 200,
			IntegrationId: this.integrationId,
			Response: []
		};
		for (const alert of unifiAlerts) {
			for (const rel of alert.alert_main_rels) {
				if (rel.AlertServiceLovId) {
					serviceImpacted.push(
						alertServiceLov.find(
							service => service.Id === rel.AlertServiceLovId
						)?.Name ?? ''
					);
				}
				if (rel.AlertAreaLovId) {
					areaAffected.push(
						alertAreaLov.find(area => area.Id === rel.AlertAreaLovId)?.Name ??
							''
					);
				}
			}
			const response: UnifiAlertsResObject = {
				Title: alert.AlertTitle,
				Cause:
					alertCauseLov.find(cause => cause.Id === alert.AlertCauseId)?.Name ??
					'',
				ServiceImpacted: serviceImpacted,
				AreaAffected: areaAffected,
				Status:
					alertStatusLov.find(status => status.Id === alert.AlertStatusId)
						?.Name ?? '',
				Ettr: alert.Ettr,
				AdditionalRemarks: alert.AdditionalRemarks,
				Displayed: alert.Displayed
			};
			res.Response.push(response);
			// reset array
			serviceImpacted = [];
			areaAffected = [];
		}
		return res;
	}
}

export default Alert;
