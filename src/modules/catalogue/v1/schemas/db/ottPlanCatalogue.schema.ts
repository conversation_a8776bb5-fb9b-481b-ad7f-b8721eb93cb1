import {
	boolean,
	integer,
	json,
	pgTable,
	text,
	timestamp
} from 'drizzle-orm/pg-core';
import { type Static, t } from 'elysia';

export const netflixPlanObjSchema = t.Object({
	TmBundleId: t.String(),
	TmProductDesc: t.String(),
	TmProductPrice: t.Numeric(),
	NetflixBundleId: t.<PERSON>(t.String()),
	NetflixPlanType: t.String(),
	NetflixOfferId: t.<PERSON>(t.String()),
	NetflixPlanStatus: t.<PERSON>()
});

export type NetflixPlanObj = Static<typeof netflixPlanObjSchema>;

export const netflixPlanListSchema = t.Array(netflixPlanObjSchema);

export type NetflixPlanList = Static<typeof netflixPlanListSchema>;

const ottDetailsSchema = t.Object({
	OttName: t.String(),
	OttIsActive: t.<PERSON>(),
	OttMerchantId: t.Number(),
	OttProductId: t.String(),
	OttOmgId: t.Number(),
	OttUniversalLink: t.String(),
	OttIconPath: t.String(),
	OttLoginType: t.String(),
	OttLoginInstruction: t.String(),
	OttVerificationInstruction: t.String(),
	OttActivationLink: t.String(),
	OttSequence: t.Number(),
	OttPrice: t.Number(),
	OttVideoUrl: t.Optional(t.String()),
	OttDescription: t.Optional(t.String()),
	OttPackageType: t.Optional(t.String()),
	OttPackageDetails: t.Optional(t.Array(t.String())),
	OttPackageDuration: t.Optional(t.String())
});

export type OttDetails = Static<typeof ottDetailsSchema>;

export const ottDetailsListSchema = t.Array(ottDetailsSchema);

export type OttDetailsList = Static<typeof ottDetailsListSchema>;

export const ottPlanCatalogueTableSchema = pgTable('ott_plan_catalogue', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	PlanId: text('plan_id').notNull().unique(),
	AllowActivation: boolean('allow_activation').notNull(),
	OttPlanName: text('ott_plan_name').notNull(),
	OttBundleImageUrl: text('ott_bundle_image_url'),
	OttSwapGroup: text('ott_swap_group'),
	OttSelectionCustChoice: json('ott_selection_cust_choice')
		.$type<OttDetailsList>()
		.notNull(),
	OttSelectionFixed: json('ott_selection_fixed')
		.$type<OttDetailsList>()
		.notNull(),
	OttSelectionNetflix: json('ott_selection_netflix')
		.$type<NetflixPlanList>()
		.notNull(),
	OttAlaCarte: json('ott_ala_carte').$type<OttDetailsList>().notNull(),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectOttPlanCatalogue =
	typeof ottPlanCatalogueTableSchema.$inferSelect;
