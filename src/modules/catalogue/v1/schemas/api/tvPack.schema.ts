import { type Static, t } from 'elysia';
import { TvPackPlanTypeEnum } from '../../../../../enum/addOns.enum';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';
import {
	netflixPlanListSchema,
	ottDetailsListSchema
} from '../db/ottPlanCatalogue.schema';

const tvPackCatalogueObjectSchema = t.Array(
	t.Object({
		PlanId: t.String({ minLength: 1 }),
		AllowActivation: t.<PERSON>(),
		PlanType: t.String({ minLength: 1 }),
		PlanSpeed: t.String({ minLength: 1 }),
		SiebelTvPackName: t.String({ minLength: 1 }),
		TvPackName: t.String({ minLength: 1 }),
		OttPlanName: t.String({ minLength: 1 }),
		Description: t.Array(t.String()),
		Summary: t.String(),
		ImageUrl: t.String(),
		VideoUrl: t.Nullable(t.String()),
		OttBundleImageUrl: t.Nullable(t.String()),
		Price: t.Numeric(),
		MonthlyCommitment: t.Numeric(),
		DiscountPercentage: t.Numeric(),
		ContractTerm: t.Number(),
		PartNumber: t.Nullable(t.String()),
		ProductId: t.Nullable(t.String()),
		CommitmentName: t.Nullable(t.String()),
		CommitmentPartNumber: t.Nullable(t.String()),
		CommitmentProductId: t.Nullable(t.String()),
		OttSwapGroup: t.Nullable(t.String()),
		OttSelectionCustChoice: ottDetailsListSchema,
		OttSelectionFixed: ottDetailsListSchema,
		OttSelectionNetflix: netflixPlanListSchema,
		OttAlaCarte: ottDetailsListSchema,
		StartDate: t.String({ minLength: 1 }),
		EndDate: t.String({ minLength: 1 })
	})
);

export const getTvPackCatalogueByPlanTypeReqSchema = t.Object({
	InternetPlanName: t.String({ minLength: 1 }),
	PlanType: t.Enum(TvPackPlanTypeEnum),
	CurrentSiebelTvPackName: t.Optional(t.String())
});

export type GetTvPackCatalogueByPlanTypeReq = Static<
	typeof getTvPackCatalogueByPlanTypeReqSchema
>;

export const getTvPackCatalogueResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: tvPackCatalogueObjectSchema
	},
	{ description: 'TV pack plans by plan type are successfully retrieved.' }
);

export type GetTvPackCatalogueRes = Static<typeof getTvPackCatalogueResSchema>;

export const addTvPackCatalogueReqSchema = t.Object({
	tvPack: tvPackCatalogueObjectSchema
});

export type AddTvPackCatalogueReq = Static<typeof addTvPackCatalogueReqSchema>;

export const addTvPackCatalogueResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Detail: t.String()
	},
	{
		description:
			'New tv pack plan is successfully added to the tv pack catalogue.'
	}
);

export type AddTvPackCatalogueRes = Static<typeof addTvPackCatalogueResSchema>;

const deleteTvPackCatalogueByIdReqSchema = t.Object({
	Id: t.Array(t.Number())
});

export type DeleteTvPackCatalogueByIdReq = Static<
	typeof deleteTvPackCatalogueByIdReqSchema
>;
