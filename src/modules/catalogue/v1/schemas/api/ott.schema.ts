import { type Static, t } from 'elysia';
import { OttCategoryEnum } from '../../../../../enum/addOns.enum';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';
import {
	netflixPlanListSchema,
	ottDetailsListSchema
} from '../db/ottPlanCatalogue.schema';

export const addNetflixPlanByPlanIdReqSchema = t.Object({
	PlanId: t.String(),
	NetflixPlanList: netflixPlanListSchema
});

export type AddNetflixPlanByPlanIdReq = Static<
	typeof addNetflixPlanByPlanIdReqSchema
>;

const updateNetflixPlanByPlanIdAndBundleIdReqSchema = t.Object({
	PlanId: t.String(),
	TmBundleId: t.String(),
	NetflixPlanList: netflixPlanListSchema
});

export type UpdateNetflixPlanByPlanIdAndBundleIdReq = Static<
	typeof updateNetflixPlanByPlanIdAndBundleIdReqSchema
>;

export const deleteNetflixPlanByPlanIdAndBundleIdReqSchema = t.Object({
	PlanId: t.String(),
	TmBundleId: t.String()
});

export const ottCatalogueDetailsSchema = t.Array(
	t.Object({
		PlanId: t.String(),
		OttSelectionCustChoice: t.Optional(ottDetailsListSchema),
		OttSelectionFixed: t.Optional(ottDetailsListSchema),
		OttAlaCarte: t.Optional(ottDetailsListSchema),
		OttSelectionNetflix: t.Optional(netflixPlanListSchema)
	})
);

export type OttCatalogueDetails = Static<typeof ottCatalogueDetailsSchema>;

export const getOttCatalogueResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: ottCatalogueDetailsSchema
	},
	{ description: 'A list of OTT by tv pack plan id is successfully retrieved.' }
);

export type GetOttCatalogueRes = Static<typeof getOttCatalogueResSchema>;

export type AddOttCatalogueByPlanIdReq = Static<
	typeof ottCatalogueDetailsSchema
>;

const updateOttCatalogueByPlanIdReqSchema = t.Object({
	PlanId: t.String(),
	OttOmgId: t.String(),
	OttDetailsList: ottDetailsListSchema
});

export type UpdateOttCatalogueByPlanIdReq = Static<
	typeof updateOttCatalogueByPlanIdReqSchema
>;

export const deleteOttByPlanIdAndOmgIdReqSchema = t.Object({
	PlanId: t.String({ minLength: 1 }),
	Category: t.Enum(OttCategoryEnum),
	OttOmgId: t.Optional(t.Number()),
	TmBundleId: t.Optional(t.String())
});

export type DeleteOttByPlanIdAndOmgIdReq = Static<
	typeof deleteOttByPlanIdAndOmgIdReqSchema
>;
