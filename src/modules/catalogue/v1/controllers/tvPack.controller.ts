import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import Elysia, { t } from 'elysia';
import {
	type BaseResponse,
	baseResponseSchema,
	errorBaseResponseSchema
} from '../../../../shared/schemas/api/responses.schema';
import { addTvPackCatalogueReqSchema } from '../schemas/api/tvPack.schema';
import TvPackCatalogue from '../services/tvPack.service';

const tvPackV1Routes = new Elysia({ prefix: '/tvpacks' })
	.use(bearer())
	.resolve(async () => {
		return {
			TvPackCatalogue: new TvPackCatalogue(randomUUID())
		};
	})
	.post(
		'',
		async (ctx): Promise<BaseResponse> => {
			const res = await ctx.TvPackCatalogue.addTvPackCatalogue(ctx.body);
			ctx.set.status = res.Code;
			return res;
		},
		{
			detail: {
				description:
					'Add new TV pack plans to the TV pack catalogue. This API is intended for internal use by developers only.<br><br><b>Table:</b> tv_pack_catalogue',
				tags: ['Catalogue'],
				hide: true
			},
			body: addTvPackCatalogueReqSchema,
			response: {
				201: baseResponseSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.delete(
		'/:planId',
		async (ctx): Promise<null> => {
			ctx.set.status = 204;
			return await ctx.TvPackCatalogue.deleteTvPackCatalogueByPlanId(
				ctx.params.planId
			);
		},
		{
			detail: {
				description:
					'Delete a TV pack plan by its ID. The TV pack ID is available in the TV pack catalogue. Exercise caution when deleting a TV pack, ensuring it is no longer in use by existing or new customers. This API is intended for internal use by developers only.</b><br><br><b>Table:</b> tv_pack_catalogue',
				tags: ['Catalogue'],
				hide: true
			},
			params: t.Object({ planId: t.String() }),
			response: {
				204: t.Null({ description: 'The tv pack was deleted successfully.' }),
				500: errorBaseResponseSchema
			}
		}
	);

export default tvPackV1Routes;
