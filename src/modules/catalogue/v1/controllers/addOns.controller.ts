import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import Elysia from 'elysia';
import { getIdTokenInfo } from '../../../../middleware/uaid/util/utils';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type AddOnsCatalogueRes,
	type OttChangePlanCatalogueRes,
	addonsCatalogueReqSchema,
	addonsCatalogueResSchema,
	ottAlaCarteCatalogueReqSchema,
	ottChangePlanCatalogueReqSchema,
	ottChangePlanCatalogueResSchema,
	ottSwapCatalogueReqSchema
} from '../schemas/api/addOns.schema';
import AddOnsCatalogue from '../services/addOns.service';

const addOnsV1Routes = new Elysia()
	.use(bearer())
	.resolve(async ctx => {
		const idTokenInfo = await getIdTokenInfo(ctx.bearer);
		return {
			AddOnsCatalogue: new AddOnsCatalogue(randomUUID(), idTokenInfo)
		};
	})
	.get(
		'/add-ons',
		async (ctx): Promise<AddOnsCatalogueRes> => {
			return await ctx.AddOnsCatalogue.getAddOnsCatalogue(ctx.query);
		},
		{
			detail: {
				description:
					'Retrieve a list of add-ons catalogue for Smart Device, Smart Home, Mesh Wifi, UPB, TV Pack, and Blacknut.<br><br><b>Table:</b> addons_metadata, addons_catalogue, tv_pack_catalogue',
				tags: ['Catalogue']
			},
			query: addonsCatalogueReqSchema,
			response: {
				200: addonsCatalogueResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.get(
		'/add-ons/ott/ala-carte',
		async (ctx): Promise<AddOnsCatalogueRes> => {
			return await ctx.AddOnsCatalogue.getOttAlaCarteCatalogue(ctx.query);
		},
		{
			detail: {
				description:
					'Retrieve a list of eligible ala carte streaming apps.<br><br><b>Table:</b> addons_metadata, tv_pack_catalogue',
				tags: ['Catalogue']
			},
			query: ottAlaCarteCatalogueReqSchema,
			response: {
				200: addonsCatalogueResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.get(
		'/add-ons/ott/swap',
		async (ctx): Promise<AddOnsCatalogueRes> => {
			return await ctx.AddOnsCatalogue.getOttSwapCatalogue(ctx.query);
		},
		{
			detail: {
				description:
					'Retrieve a list of eligible ala carte streaming apps.<br><br><b>Table:</b> addons_metadata, tv_pack_catalogue',
				tags: ['Catalogue']
			},
			query: ottSwapCatalogueReqSchema,
			response: {
				200: addonsCatalogueResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.get(
		'/add-ons/ott/change-plan',
		async (ctx): Promise<OttChangePlanCatalogueRes> => {
			return await ctx.AddOnsCatalogue.getOttChangePlanCatalogue(ctx.query);
		},
		{
			detail: {
				description:
					'Retrieve a list of available plan for upgrade.<br><br><b>Table:</b> ott_plan_catalogue',
				tags: ['Catalogue']
			},
			query: ottChangePlanCatalogueReqSchema,
			response: {
				200: ottChangePlanCatalogueResSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export default addOnsV1Routes;
