import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import Elysia, { t } from 'elysia';
import {
	type BaseResponse,
	baseResponseSchema,
	errorBaseResponseSchema
} from '../../../../shared/schemas/api/responses.schema';
import {
	type GetOttCatalogueRes,
	deleteOttByPlanIdAndOmgIdReqSchema,
	getOttCatalogueResSchema,
	ottCatalogueDetailsSchema
} from '../schemas/api/ott.schema';
import OttCatalogue from '../services/ott.service';

const ottV1Routes = new Elysia({ prefix: '/ott' })
	.use(bearer())
	.resolve(async () => {
		return {
			OttCatalogue: new OttCatalogue(randomUUID())
		};
	})
	.post(
		'',
		async (ctx): Promise<BaseResponse> => {
			const res = await ctx.OttCatalogue.addOttByPlanId(ctx.body);
			ctx.set.status = res.Code;
			return res;
		},
		{
			detail: {
				description:
					'Add a list of OTT services by plan ID. This API is intended for internal use by developers only.<br><br><b>Table:</b> tv_pack_catalogue',
				tags: ['Catalogue'],
				hide: true
			},
			body: ottCatalogueDetailsSchema,
			response: {
				202: baseResponseSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.delete(
		'',
		async (ctx): Promise<BaseResponse> => {
			return await ctx.OttCatalogue.deleteOttByPlanIdAndOmgId(ctx.query);
		},
		{
			detail: {
				description:
					'Delete a list of OTT by TV pack plan ID.<br><br><b>Table:</b> TvPackCatalogue',
				tags: ['Catalogue'],
				hide: true
			},
			query: deleteOttByPlanIdAndOmgIdReqSchema,
			response: {
				200: baseResponseSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.get(
		'/:planId',
		async (ctx): Promise<GetOttCatalogueRes> => {
			return await ctx.OttCatalogue.getOttByPlanId(ctx.params.planId);
		},
		{
			detail: {
				description:
					'Retrieve a list of OTT by TV pack plan ID.<br><br><b>Table:</b> tv_pack_catalogue',
				tags: ['Catalogue'],
				hide: true
			},
			params: t.Object({ planId: t.String() }),
			response: {
				200: getOttCatalogueResSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export default ottV1Routes;
