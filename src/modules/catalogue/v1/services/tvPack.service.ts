import { eq, sql } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { UE_ERROR } from '../../../../middleware/error';
import { isValidDate } from '../../../../shared/common';
import type {
	AddTvPackCatalogueReq,
	AddTvPackCatalogueRes
} from '../schemas/api/tvPack.schema';
import {
	type InsertTvPackCatalogue,
	tvPackCatalogueTableSchema
} from '../schemas/db/tvPackCatalogue.schema';

class TvPackCatalogue {
	private db: NodePgDatabase;
	private integrationId: string;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
	}

	async addTvPackCatalogue(
		req: AddTvPackCatalogueReq
	): Promise<AddTvPackCatalogueRes> {
		const insertedPlanId: string[] = [];
		const failedInsertionPlanId: string[] = [];

		for (const tvPack of req.tvPack) {
			if (!isValidDate(tvPack.StartDate) || !isValidDate(tvPack.EndDate)) {
				throw new UE_ERROR(
					'Invalid start or end date! Kindly follow pattern yyyy-MM-dd',
					StatusCodeEnum.BAD_REQUEST_ERROR
				);
			}

			const insertResult: InsertTvPackCatalogue[] = await this.db
				.insert(tvPackCatalogueTableSchema)
				.values({
					PlanId: tvPack.PlanId,
					PlanType: tvPack.PlanType,
					PlanSpeed: tvPack.PlanSpeed,
					SiebelTvPackName: tvPack.SiebelTvPackName,
					TvPackName: tvPack.TvPackName,
					Description: tvPack.Description,
					Summary: tvPack.Summary,
					ImageUrl: tvPack.ImageUrl,
					VideoUrl: tvPack.VideoUrl,
					MonthlyCommitment: tvPack.MonthlyCommitment,
					DiscountPercentage: tvPack.DiscountPercentage,
					ContractTerm: tvPack.ContractTerm,
					PartNumber: tvPack.PartNumber,
					ProductId: tvPack.ProductId,
					CommitmentName: tvPack.CommitmentName,
					CommitmentPartNumber: tvPack.CommitmentPartNumber,
					CommitmentProductId: tvPack.CommitmentProductId,
					StartDate: sql`TO_TIMESTAMP(${tvPack.StartDate}, 'YYYY-MM-DD HH:MI:SS')`,
					EndDate: sql`TO_TIMESTAMP(${tvPack.EndDate}, 'YYYY-MM-DD HH:MI:SS')`
				})
				.returning();

			if (insertResult.length > 0) {
				insertedPlanId.push(tvPack.PlanId);
			} else {
				failedInsertionPlanId.push(tvPack.PlanId);
			}
		}

		const res: AddTvPackCatalogueRes = {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Message: `Successfully inserted plan id: ${insertedPlanId}`,
			Detail: `Failed insertion plan id: ${failedInsertionPlanId}`
		};

		return res;
	}

	async deleteTvPackCatalogueByPlanId(id: string): Promise<null> {
		await this.db
			.delete(tvPackCatalogueTableSchema)
			.where(eq(tvPackCatalogueTableSchema.Id, +id));

		return null;
	}
}

export default TvPackCatalogue;
