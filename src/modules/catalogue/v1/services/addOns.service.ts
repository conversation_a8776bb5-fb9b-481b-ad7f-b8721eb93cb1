import { and, desc, eq, gte, lte, ne, or, sql } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { pinoLog } from '../../../../config/pinoLog.config';
import {
	AddOnsCatalogueCategoryEnum,
	OttMerchantIdEnum,
	TvPackPlanTypeEnum
} from '../../../../enum/addOns.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { IdTypeEnum } from '../../../../enum/user.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	Wso2DeviceBundleReq,
	Wso2DeviceBundleRes
} from '../../../../integration/wso2/eligibility/schemas/api/wso2RetrieveBundleDevice.schema';
import { UE_ERROR } from '../../../../middleware/error';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import {
	findPlanSpeedByDownloadSpeed,
	getUltimatePackSiebelTvPackName
} from '../../../../shared/common';
import AccountService from '../../../user/v1/helpers/accountService.helper';
import SubscribedAddOns from '../../../user/v1/helpers/subscribedAddOns.helper';
import type {
	SubscribedOttList,
	SubscribedOttListRes
} from '../../../user/v1/schemas/api/billingAccount.schema';
import type {
	AddOnsCatalogueList,
	AddOnsCatalogueReq,
	AddOnsCatalogueRes,
	OttAlaCarteCatalogueReq,
	OttChangePlanCatalogueReq,
	OttChangePlanCatalogueRes,
	OttSwapCatalogueReq,
	ProductDetailsList,
	TvPackProductList
} from '../schemas/api/addOns.schema';
import {
	type SelectAddonsCatalogue,
	addonsCatalogueTableSchema
} from '../schemas/db/addOnsCatalogue.schema';
import {
	type SelectAddonsMetadata,
	addonsMetadataTableSchema
} from '../schemas/db/addOnsMetadata.schema';
import {
	type OttDetailsList,
	type SelectOttPlanCatalogue,
	ottPlanCatalogueTableSchema
} from '../schemas/db/ottPlanCatalogue.schema';
import {
	type SelectTvPackCatalogue,
	tvPackCatalogueTableSchema
} from '../schemas/db/tvPackCatalogue.schema';

class AddOnsCatalogue {
	private integrationId: string;
	private idTokenInfo: IdTokenInfo;
	private mwIntegration: MwIntegration;
	private db: NodePgDatabase;
	private accountService: AccountService;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo) {
		this.integrationId = integrationId;
		this.idTokenInfo = idTokenInfo;
		this.mwIntegration = new MwIntegration(integrationId);
		this.db = getDbInstance();
		this.accountService = new AccountService(integrationId);
	}

	async getAddOnsCatalogue(
		req: AddOnsCatalogueReq
	): Promise<AddOnsCatalogueRes> {
		const { planName, planSpeed, tvPackName } = await this.getServiceInfo(
			req.AccountId
		);
		try {
			const addOnsMetadata: SelectAddonsMetadata[] = await this.db
				.select()
				.from(addonsMetadataTableSchema)
				.execute();

			if (addOnsMetadata.length === 0) {
				return {
					Success: true,
					Code: StatusCodeEnum.OK,
					IntegrationId: this.integrationId,
					Response: []
				};
			}

			const wso2DeviceBundleReq: Wso2DeviceBundleReq = {
				DeviceBundleRetrieveRequest: {
					TmDeviceBundle: {
						TMActiveFlag: 'Y',
						TMBundleName: planName
					}
				}
			};
			const wso2DeviceBundleRes: Wso2DeviceBundleRes =
				await this.mwIntegration.Wso2EligibilityIntegration.getWso2DeviceBundle(
					wso2DeviceBundleReq,
					false
				);

			const catalogueList: AddOnsCatalogueList =
				this.idTokenInfo.IdType === IdTypeEnum.BRN ||
				this.idTokenInfo.IdType === IdTypeEnum.NON_BRN
					? await this.getSmeCatalogue(
							planSpeed,
							addOnsMetadata,
							wso2DeviceBundleRes
						)
					: await this.getConsumerCatalogue(
							planSpeed,
							tvPackName,
							addOnsMetadata,
							wso2DeviceBundleRes
						);

			return {
				Success: true,
				Code: StatusCodeEnum.OK,
				IntegrationId: this.integrationId,
				Response: catalogueList
			};
		} catch (err) {
			pinoLog.error(err);
			throw new UE_ERROR(
				'Error in getAddOnsByCategory',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{
					integrationId: this.integrationId,
					response: err
				}
			);
		}
	}

	private async getServiceInfo(accountId: string): Promise<{
		planName: string;
		planSpeed: string;
		tvPackName: string;
	}> {
		let planName = '';
		let planSpeed = '';
		let tvPackName = '';

		const nonLightweightService =
			await this.accountService.getServiceAccountByServiceId(
				this.idTokenInfo.IdType,
				this.idTokenInfo.IdValue,
				accountId
			);

		if (!nonLightweightService) {
			throw new UE_ERROR(
				'You have no access to this service',
				StatusCodeEnum.FORBIDDEN_ERROR
			);
		}

		for (const sa of nonLightweightService?.Response?.ServiceAccount ?? []) {
			if (sa.Status === 'Active') {
				planName = sa.ProductName ?? '';
				for (const moli of sa.ServiceAccountMoli ?? []) {
					for (const oli of moli.ServiceAccountOli ?? []) {
						if (oli.Type === 'Speed') {
							const downloadSpeedKbps = oli[
								'ListOfTmAssetMgmt-AssetXaIntegration'
							]?.['TmAssetMgmt-AssetXaIntegration']?.find(
								asset => asset.Name === 'Download Speed'
							);

							planSpeed = findPlanSpeedByDownloadSpeed(
								downloadSpeedKbps?.Value ?? ''
							);
						}

						if (oli.Type === 'HyppTV Package') {
							tvPackName = getUltimatePackSiebelTvPackName(
								sa.ProductName ?? '',
								oli.ProductName ?? ''
							);
						}
					}
				}
			}
		}

		return {
			planName,
			planSpeed,
			tvPackName
		};
	}

	private async getConsumerCatalogue(
		planSpeed: string,
		tvPackName: string,
		addOnsMetadata: SelectAddonsMetadata[],
		wso2DeviceBundleRes: Wso2DeviceBundleRes
	): Promise<AddOnsCatalogueList> {
		const consumerCatalogueList: AddOnsCatalogueList = [];
		for (const metadata of addOnsMetadata) {
			switch (metadata.Category) {
				case AddOnsCatalogueCategoryEnum.SMART_DEVICE:
				case AddOnsCatalogueCategoryEnum.SMART_HOME:
				case AddOnsCatalogueCategoryEnum.MESH_WIFI:
				case AddOnsCatalogueCategoryEnum.BLACKNUT:
					consumerCatalogueList.push(
						...(await this.getBundleCatalogueList(
							wso2DeviceBundleRes,
							metadata
						))
					);
					break;
				case AddOnsCatalogueCategoryEnum.MESH_WIFI_6:
					consumerCatalogueList.push(
						...(await this.getMeshWifi6CatalogueList(planSpeed, metadata))
					);
					break;
				case AddOnsCatalogueCategoryEnum.UPB:
					consumerCatalogueList.push(
						...(await this.getCatalogueList(metadata))
					);
					break;
				case AddOnsCatalogueCategoryEnum.TV_PACK:
					consumerCatalogueList.push(
						...(await this.getTvPackCatalogue(planSpeed, tvPackName, metadata))
					);
					break;
				default:
					break;
			}
		}
		return consumerCatalogueList;
	}

	private async getSmeCatalogue(
		planSpeed: string,
		addOnsMetadata: SelectAddonsMetadata[],
		wso2DeviceBundleRes: Wso2DeviceBundleRes
	): Promise<AddOnsCatalogueList> {
		const smeCatalogueList: AddOnsCatalogueList = [];
		for (const metadata of addOnsMetadata) {
			switch (metadata.Category) {
				case AddOnsCatalogueCategoryEnum.SME_SMART_DEVICE:
				case AddOnsCatalogueCategoryEnum.MESH_WIFI:
					smeCatalogueList.push(
						...(await this.getBundleCatalogueList(
							wso2DeviceBundleRes,
							metadata
						))
					);
					break;
				case AddOnsCatalogueCategoryEnum.MESH_WIFI_6:
					smeCatalogueList.push(
						...(await this.getMeshWifi6CatalogueList(planSpeed, metadata))
					);
					break;
				default:
					break;
			}
		}
		return smeCatalogueList;
	}

	private async fetchCatalogue(
		metadata: SelectAddonsMetadata
	): Promise<ProductDetailsList> {
		const addonsCatalogue: SelectAddonsCatalogue[] = await this.db
			.select()
			.from(addonsCatalogueTableSchema)
			.where(
				and(
					eq(addonsCatalogueTableSchema.Category, metadata.Category),
					lte(
						sql`${addonsCatalogueTableSchema.StartDate}::timestamp without time zone`,
						sql`now()`
					),
					gte(
						sql`${addonsCatalogueTableSchema.EndDate}::timestamp without time zone`,
						sql`now()`
					)
				)
			)
			.execute();

		return addonsCatalogue.map(catalogue => ({
			Id: catalogue.Id,
			Name: catalogue.Name,
			DisplayName: catalogue.DisplayName,
			PartnerId: catalogue.PartnerId,
			Description: catalogue.Description ?? [],
			DevicePrice: catalogue.MonthlyCommitment * catalogue.ContractTerm,
			MonthlyCommitment: catalogue.MonthlyCommitment,
			RRP: catalogue.RRP,
			PurchasePrice: 0,
			LeasingType: 'Lease to Own',
			ContractTerm: catalogue.ContractTerm,
			Summary: catalogue.Summary,
			Specification: catalogue.Specification,
			ImageUrl: catalogue.ImageUrl,
			Save:
				catalogue.DiscountPercentage > 0
					? `Save ${catalogue.DiscountPercentage}%`
					: null,
			VoucherName: catalogue.VoucherName ?? null,
			PartNumber: catalogue.PartNumber ?? null,
			ProductId: catalogue.ProductId ?? null,
			IsBundle: catalogue.IsBundle ?? false,
			DeliveryPartner: null
		}));
	}

	private async getCatalogueList(
		metadata: SelectAddonsMetadata
	): Promise<AddOnsCatalogueList> {
		const productDetails: ProductDetailsList =
			await this.fetchCatalogue(metadata);
		return [
			{
				Category: metadata.Category,
				FaqUrl: metadata.FaqUrl,
				TncUrl: metadata.TncUrl,
				WarrantyPolicyUrl: metadata.WarrantyPolicyUrl ?? null,
				PrivacyNoticeUrl: metadata.PrivacyNoticeUrl ?? null,
				ProductDetails: productDetails
			}
		];
	}

	private async getMeshWifi6CatalogueList(
		planSpeed: string,
		metadata: SelectAddonsMetadata
	): Promise<AddOnsCatalogueList> {
		const productDetailsFromDb: ProductDetailsList =
			await this.fetchCatalogue(metadata);
		const productDetails = planSpeed.toLowerCase().includes('gbps')
			? productDetailsFromDb.filter(
					product =>
						product.Name.includes('RM15') ||
						product.Name.includes('RM350') ||
						product.Name.includes('X60')
				)
			: productDetailsFromDb.filter(
					product =>
						product.Name.includes('RM20') || product.Name.includes('RM380')
				);
		return [
			{
				Category: metadata.Category,
				FaqUrl: metadata.FaqUrl,
				TncUrl: metadata.TncUrl,
				WarrantyPolicyUrl: metadata.WarrantyPolicyUrl ?? null,
				PrivacyNoticeUrl: metadata.PrivacyNoticeUrl ?? null,
				ProductDetails: productDetails
			}
		];
	}

	private async getBundleCatalogueList(
		wso2DeviceBundleRes: Wso2DeviceBundleRes,
		metadata: SelectAddonsMetadata
	): Promise<AddOnsCatalogueList> {
		const productDetails: ProductDetailsList = [];
		const productDetailsFromDb: ProductDetailsList =
			await this.fetchCatalogue(metadata);

		for (const product of productDetailsFromDb) {
			for (const deviceBundle of wso2DeviceBundleRes?.Response
				?.DeviceBundleRetrieveResponse?.TmDeviceBundle ?? []) {
				if (
					product.Name.toLowerCase().replace(/\s+/g, '') ===
					deviceBundle.TMDeviceName?.toLowerCase().replace(/\s+/g, '')
				) {
					product.ContractTerm = Number(deviceBundle.TMContractMonth ?? 0);
					product.MonthlyCommitment = Number(deviceBundle.TMMRC ?? 0);
					product.PartnerId = deviceBundle.TMPartnerID ?? product.PartnerId;
					product.PurchasePrice = Number(deviceBundle.TMPurchasePrice ?? 0);
					product.LeasingType =
						deviceBundle.TMLeasingType ?? product.LeasingType;
					product.DevicePrice = Number(deviceBundle.TMDevicePrice ?? 0);
					product.DeliveryPartner = deviceBundle.TMDeliveryPartner ?? null;
					productDetails.push(product);
				}
			}
		}

		return [
			{
				IsErrorFromWso2: wso2DeviceBundleRes === null,
				Category: metadata.Category,
				FaqUrl: metadata.FaqUrl,
				TncUrl: metadata.TncUrl,
				WarrantyPolicyUrl: metadata.WarrantyPolicyUrl ?? null,
				PrivacyNoticeUrl: metadata.PrivacyNoticeUrl ?? null,
				ProductDetails: productDetails
			}
		];
	}

	private async getTvPackCatalogue(
		planSpeed: string,
		tvPackName: string,
		addonsMetadata: SelectAddonsMetadata
	): Promise<AddOnsCatalogueList> {
		const catalogueList: AddOnsCatalogueList = [];
		let tvPackCatalogue = [];

		if (tvPackName !== '') {
			const [currentTvPackPlan]: SelectTvPackCatalogue[] = await this.db
				.select()
				.from(tvPackCatalogueTableSchema)
				.where(
					and(
						eq(tvPackCatalogueTableSchema.PlanType, TvPackPlanTypeEnum.BUNDLE),
						eq(tvPackCatalogueTableSchema.SiebelTvPackName, tvPackName),
						or(
							eq(tvPackCatalogueTableSchema.PlanSpeed, planSpeed),
							eq(tvPackCatalogueTableSchema.PlanSpeed, 'All')
						)
					)
				)
				.execute();

			tvPackCatalogue = await this.db
				.select({
					tvPack: tvPackCatalogueTableSchema,
					ottPlan: ottPlanCatalogueTableSchema
				})
				.from(tvPackCatalogueTableSchema)
				.innerJoin(
					ottPlanCatalogueTableSchema,
					eq(
						tvPackCatalogueTableSchema.PlanId,
						ottPlanCatalogueTableSchema.PlanId
					)
				)
				.where(
					and(
						eq(tvPackCatalogueTableSchema.PlanType, TvPackPlanTypeEnum.BUNDLE),
						ne(tvPackCatalogueTableSchema.PlanId, currentTvPackPlan.PlanId),
						gte(
							tvPackCatalogueTableSchema.MonthlyCommitment,
							currentTvPackPlan.MonthlyCommitment
						),
						or(
							eq(tvPackCatalogueTableSchema.PlanSpeed, planSpeed),
							eq(tvPackCatalogueTableSchema.PlanSpeed, 'All')
						),
						lte(
							sql`${tvPackCatalogueTableSchema.StartDate}::timestamp without time zone`,
							sql`now()`
						),
						gte(
							sql`${tvPackCatalogueTableSchema.EndDate}::timestamp without time zone`,
							sql`now()`
						)
					)
				)
				.orderBy(desc(tvPackCatalogueTableSchema.MonthlyCommitment))
				.execute();
		} else {
			tvPackCatalogue = await this.db
				.select({
					tvPack: tvPackCatalogueTableSchema,
					ottPlan: ottPlanCatalogueTableSchema
				})
				.from(tvPackCatalogueTableSchema)
				.innerJoin(
					ottPlanCatalogueTableSchema,
					eq(
						tvPackCatalogueTableSchema.PlanId,
						ottPlanCatalogueTableSchema.PlanId
					)
				)
				.where(
					and(
						eq(tvPackCatalogueTableSchema.PlanType, TvPackPlanTypeEnum.BUNDLE),
						or(
							eq(tvPackCatalogueTableSchema.PlanSpeed, planSpeed),
							eq(tvPackCatalogueTableSchema.PlanSpeed, 'All')
						),
						lte(
							sql`${tvPackCatalogueTableSchema.StartDate}::timestamp without time zone`,
							sql`now()`
						),
						gte(
							sql`${tvPackCatalogueTableSchema.EndDate}::timestamp without time zone`,
							sql`now()`
						)
					)
				)
				.orderBy(desc(tvPackCatalogueTableSchema.MonthlyCommitment))
				.execute();
		}

		const productDetails: TvPackProductList = tvPackCatalogue.map(
			({ tvPack, ottPlan }) => ({
				Id: tvPack.Id,
				Name: tvPack.SiebelTvPackName,
				DisplayName: tvPack.TvPackName,
				PartnerId: 'UPB',
				Description: tvPack.Description ?? [],
				MonthlyCommitment: tvPack.MonthlyCommitment,
				LeasingType: null,
				ContractTerm: tvPack.ContractTerm,
				Summary: tvPack.Summary,
				Specification: null,
				ImageUrl: tvPack.ImageUrl ?? 'N/A',
				VideoUrl: tvPack.VideoUrl,
				Save:
					tvPack.DiscountPercentage > 0
						? `Save ${tvPack.DiscountPercentage}%`
						: null,
				VoucherName: tvPack.VoucherName,
				PartNumber: tvPack.PartNumber,
				ProductId: tvPack.ProductId,
				PlanId: tvPack.PlanId,
				OttSelectionCustChoice: ottPlan.OttSelectionCustChoice,
				OttSelectionFixed: ottPlan.OttSelectionFixed,
				OttSelectionNetflix: ottPlan.OttSelectionNetflix
			})
		);

		catalogueList.push({
			Category: addonsMetadata.Category,
			FaqUrl: addonsMetadata.FaqUrl,
			TncUrl: addonsMetadata.TncUrl,
			WarrantyPolicyUrl: addonsMetadata.WarrantyPolicyUrl,
			PrivacyNoticeUrl: addonsMetadata.PrivacyNoticeUrl,
			ProductDetails: productDetails
		});

		return catalogueList;
	}

	async getOttAlaCarteCatalogue(
		req: OttAlaCarteCatalogueReq
	): Promise<AddOnsCatalogueRes> {
		const { planSpeed, tvPackName } = await this.getServiceInfo(req.AccountId);

		// Check advanced payment
		// const hasAdvancedPayment = await this.hasPendingAdvancedPayment(
		// 	req.AccountId
		// );
		// if (hasAdvancedPayment) {
		// 	throw new UE_ERROR(
		// 		'You have a pending advanced payment. Please contact support for assistance.',
		// 		StatusCodeEnum.FORBIDDEN_ERROR,
		// 		{
		// 			integrationId: this.integrationId
		// 		}
		// 	);
		// }
		try {
			const [alaCartePlan] = await this.getAlaCartePlan(tvPackName, planSpeed);
			const [addOnsMetadata]: SelectAddonsMetadata[] =
				await this.getOttAddOnsMetadata();
			const subscribedOtts: SubscribedOttListRes = await this.getSubscribedOtts(
				req.AccountId,
				tvPackName,
				planSpeed
			);
			const ottAlaCarteList: OttDetailsList = this.filterOttList(
				alaCartePlan.ottPlan.OttAlaCarte,
				subscribedOtts
			);

			return this.buildOttResponse(
				addOnsMetadata,
				alaCartePlan.tvPack.PlanId !== 'P8' ? 'P7' : 'P8',
				ottAlaCarteList
			);
		} catch (error) {
			throw new UE_ERROR(
				'Something went wrong with the OTT catalogue.',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{
					integrationId: this.integrationId,
					response: String(error)
				}
			);
		}
	}

	async getOttSwapCatalogue(
		req: OttSwapCatalogueReq
	): Promise<AddOnsCatalogueRes> {
		const { planSpeed, tvPackName } = await this.getServiceInfo(req.AccountId);

		try {
			const [swapPlan]: SelectOttPlanCatalogue[] = await this.getSwapPlan(
				req.OttPlanSwapGroup
			);
			const [addOnsMetadata]: SelectAddonsMetadata[] =
				await this.getOttAddOnsMetadata();
			const subscribedOtts: SubscribedOttListRes = await this.getSubscribedOtts(
				req.AccountId,
				tvPackName,
				planSpeed
			);
			const ottSwapList: OttDetailsList = this.filterOttList(
				swapPlan.OttSelectionCustChoice,
				subscribedOtts
			);

			return this.buildOttResponse(
				addOnsMetadata,
				swapPlan.PlanId,
				ottSwapList
			);
		} catch (error) {
			throw new UE_ERROR(
				'Something went wrong with the OTT catalogue.',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{
					integrationId: this.integrationId,
					response: String(error)
				}
			);
		}
	}

	async getOttChangePlanCatalogue(
		req: OttChangePlanCatalogueReq
	): Promise<OttChangePlanCatalogueRes> {
		const [addOnsMetadata]: SelectAddonsMetadata[] = await this.db
			.select()
			.from(addonsMetadataTableSchema)
			.where(
				eq(
					addonsMetadataTableSchema.Category,
					AddOnsCatalogueCategoryEnum.OTT_CHANGE_PLAN
				)
			)
			.execute();
		const [changePlanCatalogue]: SelectOttPlanCatalogue[] = await this.db
			.select()
			.from(ottPlanCatalogueTableSchema)
			.where(eq(ottPlanCatalogueTableSchema.PlanId, req.OttPlanId))
			.execute();

		// construct netflix plan response
		if (
			Number(req.OttMerchantId) === OttMerchantIdEnum.NETFLIX &&
			changePlanCatalogue.OttSelectionFixed.some(
				ott => ott.OttMerchantId === Number(req.OttMerchantId)
			)
		) {
			const [netflixFixedPlanList]: OttDetailsList =
				changePlanCatalogue.OttSelectionFixed.filter(
					ott => ott.OttMerchantId === Number(req.OttMerchantId)
				);
			return {
				Success: true,
				Code: StatusCodeEnum.OK,
				IntegrationId: this.integrationId,
				Response: [
					{
						Category: AddOnsCatalogueCategoryEnum.OTT_CHANGE_PLAN,
						FaqUrl: addOnsMetadata?.FaqUrl ?? null,
						TncUrl: addOnsMetadata?.TncUrl ?? null,
						WarrantyPolicyUrl: addOnsMetadata?.WarrantyPolicyUrl ?? null,
						PrivacyNoticeUrl: addOnsMetadata?.PrivacyNoticeUrl ?? null,
						ProductDetails: changePlanCatalogue.OttSelectionNetflix.map(
							netflix => {
								return {
									OttName: netflixFixedPlanList.OttName,
									OttIsActive: netflix.NetflixPlanStatus,
									OttMerchantId: netflixFixedPlanList.OttMerchantId,
									OttProductId: netflixFixedPlanList.OttProductId,
									OttOmgId: netflixFixedPlanList.OttOmgId,
									OttUniversalLink: netflixFixedPlanList.OttUniversalLink,
									OttIconPath: netflixFixedPlanList.OttIconPath,
									OttLoginType: netflixFixedPlanList.OttLoginType,
									OttLoginInstruction: netflixFixedPlanList.OttLoginInstruction,
									OttVerificationInstruction:
										netflixFixedPlanList.OttVerificationInstruction,
									OttActivationLink: netflixFixedPlanList.OttActivationLink,
									OttSequence: netflixFixedPlanList.OttSequence,
									OttPrice: netflix.TmProductPrice,
									OttVideoUrl: netflixFixedPlanList.OttVideoUrl,
									OttDescription: netflixFixedPlanList.OttDescription,
									OttPackageType: netflix.NetflixPlanType,
									OttPackageDetails: netflixFixedPlanList.OttPackageDetails,
									OttPackageDuration: netflixFixedPlanList.OttPackageDuration,
									TmBundleId: netflix.TmBundleId,
									NetflixBundleId: netflix.NetflixBundleId,
									NetflixOfferId: netflix.NetflixOfferId
								};
							}
						)
					}
				]
			};
		}

		const ottChangePlanList: OttDetailsList =
			changePlanCatalogue.OttSelectionCustChoice.filter(
				ott => ott.OttMerchantId === Number(req.OttMerchantId)
			);

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: [
				{
					Category: AddOnsCatalogueCategoryEnum.OTT_CHANGE_PLAN,
					FaqUrl: addOnsMetadata?.FaqUrl ?? null,
					TncUrl: addOnsMetadata?.TncUrl ?? null,
					WarrantyPolicyUrl: addOnsMetadata?.WarrantyPolicyUrl ?? null,
					PrivacyNoticeUrl: addOnsMetadata?.PrivacyNoticeUrl ?? null,
					ProductDetails: ottChangePlanList
				}
			]
		};
	}

	private async getAlaCartePlan(
		tvPackName: string,
		planSpeed: string
	): Promise<
		{ tvPack: SelectTvPackCatalogue; ottPlan: SelectOttPlanCatalogue }[]
	> {
		if (tvPackName !== '') {
			return await this.db
				.select({
					tvPack: tvPackCatalogueTableSchema,
					ottPlan: ottPlanCatalogueTableSchema
				})
				.from(tvPackCatalogueTableSchema)
				.innerJoin(
					ottPlanCatalogueTableSchema,
					eq(
						tvPackCatalogueTableSchema.PlanId,
						ottPlanCatalogueTableSchema.PlanId
					)
				)
				.where(
					and(
						eq(tvPackCatalogueTableSchema.PlanType, TvPackPlanTypeEnum.BUNDLE),
						eq(tvPackCatalogueTableSchema.SiebelTvPackName, tvPackName),
						or(
							eq(tvPackCatalogueTableSchema.PlanSpeed, planSpeed),
							eq(tvPackCatalogueTableSchema.PlanSpeed, 'All')
						)
					)
				)
				.execute();
		}
		return await this.db
			.select({
				tvPack: tvPackCatalogueTableSchema,
				ottPlan: ottPlanCatalogueTableSchema
			})
			.from(tvPackCatalogueTableSchema)
			.innerJoin(
				ottPlanCatalogueTableSchema,
				eq(
					tvPackCatalogueTableSchema.PlanId,
					ottPlanCatalogueTableSchema.PlanId
				)
			)
			.where(eq(tvPackCatalogueTableSchema.PlanId, 'P8'))
			.execute();
	}

	private async getSwapPlan(
		OttPlanSwapGroup: string
	): Promise<SelectOttPlanCatalogue[]> {
		return await this.db
			.select()
			.from(ottPlanCatalogueTableSchema)
			.where(eq(ottPlanCatalogueTableSchema.PlanId, OttPlanSwapGroup))
			.execute();
	}

	private async getOttAddOnsMetadata(): Promise<SelectAddonsMetadata[]> {
		try {
			return await this.db
				.select()
				.from(addonsMetadataTableSchema)
				.where(
					eq(
						addonsMetadataTableSchema.Category,
						AddOnsCatalogueCategoryEnum.OTT
					)
				)
				.execute();
		} catch (err) {
			pinoLog.error(err);
			return [];
		}
	}

	/**
	 *
	 * intentionally commented out until confirmed advanced payment checking is applicable for New Install only
	 */
	// private async hasPendingAdvancedPayment(serviceId: string): Promise<boolean> {
	// 	const wso2Req: Wso2ConciseCustInfoReq = {
	// 		requestHeader: {
	// 			requestId: `XE-${Math.random().toString(36).slice(2)}`,
	// 			eventName: 'evOXECONCISECustInfo'
	// 		},
	// 		kciRequest: {
	// 			customerId: '',
	// 			serviceNo: '',
	// 			serviceId: serviceId,
	// 			account: ''
	// 		}
	// 	};
	// 	const wso2Res: Wso2ConciseCustInfoRes =
	// 		await this.mwIntegration.Wso2UserIntegration.getWso2ConciseCustInfo(
	// 			wso2Req
	// 		);

	// 	if (
	// 		wso2Res.kciResponse === null ||
	// 		wso2Res.kciResponse.kciresponseData === null ||
	// 		wso2Res.kciResponse.kciresponseData.length === 0
	// 	) {
	// 		return true;
	// 	}

	// 	const conciseResponse = wso2Res?.kciResponse?.kciresponseData
	// 		.filter(item => item !== null) // Remove nulls
	// 		.find(() => true); // Pick the first element

	// 	if (conciseResponse && 'Y' === conciseResponse.advancepaymentflag) {
	// 		return true;
	// 	}

	// 	return false;
	// }

	private async getSubscribedOtts(
		accountId: string,
		tvPackName: string,
		planSpeed: string
	): Promise<SubscribedOttListRes> {
		return await new SubscribedAddOns(
			this.db,
			this.integrationId
		).getOttListByTvPack(accountId, tvPackName, planSpeed, null, true);
	}

	private filterOttList(
		ottAlaCarteList: OttDetailsList,
		subscribedOtts: SubscribedOttListRes
	): OttDetailsList {
		const subscribedOttList: SubscribedOttList = [
			...subscribedOtts.OttSelectionCustChoice,
			...subscribedOtts.OttSelectionFixed,
			...subscribedOtts.OttAlaCarte
		];

		let filteredList = ottAlaCarteList.filter(
			ottAlaCarte =>
				!subscribedOttList.some(
					userOtt =>
						(userOtt.OttMerchantId === OttMerchantIdEnum.YUPPTV &&
							userOtt.OttName === ottAlaCarte.OttName) ||
						(userOtt.OttMerchantId !== OttMerchantIdEnum.YUPPTV &&
							userOtt.OttMerchantId === ottAlaCarte.OttMerchantId)
				)
		);

		filteredList = this.removeConflictingOtts(
			filteredList,
			subscribedOttList,
			OttMerchantIdEnum.TVB,
			{
				Premium: ['Premium', 'VOD', 'Channel'],
				VOD: ['Premium', 'VOD'],
				Channel: ['Premium', 'Channel']
			}
		);

		return filteredList;
	}

	private removeConflictingOtts(
		ottAlaCarteList: OttDetailsList,
		userOttListAll: SubscribedOttList,
		merchantId: number,
		conflictDict: Record<string, string[]>
	): OttDetailsList {
		const exists: boolean = userOttListAll.some(
			entitlement => entitlement.OttMerchantId === merchantId
		);

		if (exists) {
			let filteredList: OttDetailsList = [];
			for (const [key, values] of Object.entries(conflictDict)) {
				if (
					userOttListAll.some(entitlement => entitlement.OttName.includes(key))
				) {
					filteredList = ottAlaCarteList.filter(
						alaCarte => !values.some(name => alaCarte.OttName.includes(name))
					);
				}
			}
			return filteredList;
		}

		return ottAlaCarteList;
	}

	private buildOttResponse(
		addOnsMetadata: SelectAddonsMetadata,
		planId: string,
		ottAlaCarteList: OttDetailsList
	): AddOnsCatalogueRes {
		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: [
				{
					Category: addOnsMetadata.Category,
					FaqUrl: addOnsMetadata?.FaqUrl ?? null,
					TncUrl: addOnsMetadata?.TncUrl ?? null,
					WarrantyPolicyUrl: addOnsMetadata?.WarrantyPolicyUrl ?? null,
					PrivacyNoticeUrl: addOnsMetadata?.PrivacyNoticeUrl ?? null,
					PlanId: planId,
					ProductDetails: ottAlaCarteList
				}
			]
		};
	}
}

export default AddOnsCatalogue;
