//import { MwIntegration } from '../../../../integration/MwIntegration';
import { eq, sql } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { OttCategoryEnum } from '../../../../enum/addOns.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { UE_ERROR } from '../../../../middleware/error';
import type { BaseResponse } from '../../../../shared/schemas/api/responses.schema';
import type {
	AddOttCatalogueByPlanIdReq,
	DeleteOttByPlanIdAndOmgIdReq,
	GetOttCatalogueRes,
	OttCatalogueDetails
} from '../schemas/api/ott.schema';
import {
	type SelectOttPlanCatalogue,
	ottPlanCatalogueTableSchema
} from '../schemas/db/ottPlanCatalogue.schema';

class OttCatalogue {
	private db: NodePgDatabase;
	private integrationId: string;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
	}

	async getOttByPlanId(planId: string): Promise<GetOttCatalogueRes> {
		const findTvPackByPlanId: SelectOttPlanCatalogue[] = await this.db
			.select()
			.from(ottPlanCatalogueTableSchema)
			.where(eq(ottPlanCatalogueTableSchema.PlanId, planId))
			.execute();

		if (findTvPackByPlanId.length === 0) {
			throw new UE_ERROR(
				`No tv pack plan with plan id: ${planId}`,
				StatusCodeEnum.NOT_FOUND_ERROR
			);
		}

		const ottCatalogueRes: OttCatalogueDetails = [];
		for (const tvPackCatalogueObj of findTvPackByPlanId) {
			ottCatalogueRes.push({
				PlanId: tvPackCatalogueObj.PlanId,
				OttSelectionCustChoice: tvPackCatalogueObj.OttSelectionCustChoice,
				OttSelectionFixed: tvPackCatalogueObj.OttSelectionFixed,
				OttAlaCarte: tvPackCatalogueObj.OttAlaCarte,
				OttSelectionNetflix: tvPackCatalogueObj.OttSelectionNetflix
			});
		}

		const res: GetOttCatalogueRes = {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: ottCatalogueRes
		};

		return res;
	}

	async addOttByPlanId(req: AddOttCatalogueByPlanIdReq): Promise<BaseResponse> {
		for (const reqObj of req) {
			const findTvPackByPlanId: SelectOttPlanCatalogue[] = await this.db
				.select()
				.from(ottPlanCatalogueTableSchema)
				.where(eq(ottPlanCatalogueTableSchema.PlanId, reqObj.PlanId))
				.execute();

			if (findTvPackByPlanId.length > 0) {
				for (const tvPackCatalogueObj of findTvPackByPlanId) {
					if (
						reqObj.OttSelectionCustChoice &&
						reqObj.OttSelectionCustChoice.length > 0
					) {
						tvPackCatalogueObj.OttSelectionCustChoice.push(
							...reqObj.OttSelectionCustChoice
						);
					}
					if (reqObj.OttSelectionFixed && reqObj.OttSelectionFixed.length > 0) {
						tvPackCatalogueObj.OttSelectionFixed.push(
							...reqObj.OttSelectionFixed
						);
					}
					if (reqObj.OttAlaCarte && reqObj.OttAlaCarte.length > 0) {
						tvPackCatalogueObj.OttAlaCarte.push(...reqObj.OttAlaCarte);
					}
					if (
						reqObj.OttSelectionNetflix &&
						reqObj.OttSelectionNetflix.length > 0
					) {
						tvPackCatalogueObj.OttSelectionNetflix.push(
							...reqObj.OttSelectionNetflix
						);
					}

					await this.db
						.update(ottPlanCatalogueTableSchema)
						.set({
							OttSelectionCustChoice: tvPackCatalogueObj.OttSelectionCustChoice,
							OttSelectionFixed: tvPackCatalogueObj.OttSelectionFixed,
							OttAlaCarte: tvPackCatalogueObj.OttAlaCarte,
							OttSelectionNetflix: tvPackCatalogueObj.OttSelectionNetflix,
							UpdatedAt: sql`now()`
						})
						.where(eq(ottPlanCatalogueTableSchema.PlanId, reqObj.PlanId));
				}
			}
		}

		const res: BaseResponse = {
			Success: true,
			Code: StatusCodeEnum.ACCEPTED,
			IntegrationId: this.integrationId,
			Message: 'OTT content updated successfully'
		};
		return res;
	}

	async deleteOttByPlanIdAndOmgId(
		req: DeleteOttByPlanIdAndOmgIdReq
	): Promise<BaseResponse> {
		const findTvPackByPlanId: SelectOttPlanCatalogue[] = await this.db
			.select()
			.from(ottPlanCatalogueTableSchema)
			.where(eq(ottPlanCatalogueTableSchema.PlanId, req.PlanId))
			.execute();
		if (findTvPackByPlanId.length > 0) {
			for (const tvPackCatalogueObj of findTvPackByPlanId) {
				if (req.Category === OttCategoryEnum.OTT_CUST_CHOICE) {
					tvPackCatalogueObj.OttSelectionCustChoice.forEach(
						async (item, index) => {
							if (item.OttOmgId === req.OttOmgId) {
								tvPackCatalogueObj.OttSelectionCustChoice.splice(index, 1);
								await this.db
									.update(ottPlanCatalogueTableSchema)
									.set({
										OttSelectionCustChoice:
											tvPackCatalogueObj.OttSelectionCustChoice,
										UpdatedAt: sql`now()`
									})
									.where(eq(ottPlanCatalogueTableSchema.PlanId, req.PlanId))
									.returning();
							}
						}
					);
				} else if (req.Category === OttCategoryEnum.OTT_FIXED) {
					tvPackCatalogueObj.OttSelectionFixed.forEach(async (item, index) => {
						if (item.OttOmgId === req.OttOmgId) {
							tvPackCatalogueObj.OttSelectionFixed.splice(index, 1);
							await this.db
								.update(ottPlanCatalogueTableSchema)
								.set({
									OttSelectionFixed: tvPackCatalogueObj.OttSelectionFixed,
									UpdatedAt: sql`now()`
								})
								.where(eq(ottPlanCatalogueTableSchema.PlanId, req.PlanId))
								.returning();
						}
					});
				} else if (req.Category === OttCategoryEnum.OTT_ALA_CARTE) {
					tvPackCatalogueObj.OttAlaCarte.forEach(async (item, index) => {
						if (item.OttOmgId === req.OttOmgId) {
							tvPackCatalogueObj.OttAlaCarte.splice(index, 1);
							await this.db
								.update(ottPlanCatalogueTableSchema)
								.set({
									OttAlaCarte: tvPackCatalogueObj.OttAlaCarte,
									UpdatedAt: sql`now()`
								})
								.where(eq(ottPlanCatalogueTableSchema.PlanId, req.PlanId))
								.returning();
						}
					});
				} else if (req.Category === OttCategoryEnum.OTT_NETFLIX) {
					tvPackCatalogueObj.OttSelectionNetflix.forEach(
						async (item, index) => {
							if (item.TmBundleId === req.TmBundleId) {
								tvPackCatalogueObj.OttSelectionNetflix.splice(index, 1);
								await this.db
									.update(ottPlanCatalogueTableSchema)
									.set({
										OttSelectionNetflix: tvPackCatalogueObj.OttSelectionNetflix,
										UpdatedAt: sql`now()`
									})
									.where(eq(ottPlanCatalogueTableSchema.PlanId, req.PlanId))
									.returning();
							}
						}
					);
				}
			}
		}

		const res: BaseResponse = {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Message: 'OTT content deleted successfully'
		};
		return res;
	}
}

export default OttCatalogue;
