import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../shared/schemas/api/responses.schema';

export const getCacheListResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			Keys: t.Array(t.String({}))
		})
	},
	{ additionalProperties: false }
);

export type GetCacheListRes = Static<typeof getCacheListResSchema>;

export const getCacheListReqSchema = t.Object({
	key: t.String({
		example: 'cache-key',
		description: 'Cache key'
	})
});

export type GetCacheListReq = Static<typeof getCacheListReqSchema>;

export const cacheByKeyReqSchema = t.Object({
	key: t.String({
		example: 'cache-key',
		description: 'Cache key'
	})
});

export type CacheByKeyReq = Static<typeof cacheByKeyReqSchema>;

export const getCacheByKeyResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			Value: t.Nullable(t.String({}))
		})
	},
	{ description: 'Cache successfully retrieved.' }
);

export type GetCacheByKeyRes = Static<typeof getCacheByKeyResSchema>;

export const deleteCacheByKeyResSchema = t.Object(
	{
		...baseResponseSchema.properties
	},
	{ description: 'Cache successfully deleted.' }
);

export type DeleteCacheByKeyRes = Static<typeof deleteCacheByKeyResSchema>;
