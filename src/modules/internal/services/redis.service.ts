import {
	deleteCache,
	getCache,
	getCacheKeys
} from '../../../config/cache.config';
import { StatusCodeEnum } from '../../../enum/statusCode.enum';
import type {
	CacheByKeyReq,
	DeleteCacheByKeyRes,
	GetCacheByKeyRes,
	GetCacheListRes
} from '../schemas/api/redis.schema';

class Redis {
	async getCacheValueByKey(req: CacheByKeyReq): Promise<GetCacheByKeyRes> {
		const cache = await getCache(req.key);
		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			Response: {
				Value: cache
			}
		};
	}

	async getCacheKeys(): Promise<GetCacheListRes> {
		const cache = await getCacheKeys();
		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			Response: {
				Keys: cache
			}
		};
	}

	async deleteCacheByKey(req: CacheByKeyReq): Promise<DeleteCacheByKeyRes> {
		await deleteCache(req.key);
		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			Message: 'Cache successfully deleted'
		};
	}
}

export default Redis;
