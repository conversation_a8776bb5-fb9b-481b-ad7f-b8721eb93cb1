import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import Elysia from 'elysia';
import { getIdTokenInfo } from '../../../../middleware/uaid/util/utils';
import { baseHeaderSchema } from '../../../../shared/schemas/api/headers.schema';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type AddOnsOrderRes,
	addOnsOrderReqSchema,
	addOnsOrderResSchema
} from '../schemas/api/addOnsOrder.schema';
import {
	type CreateIjoinOrderRes,
	createIjoinOrderReqSchema,
	createIjoinOrderResSchema
} from '../schemas/api/iJoinOrder.schema';
import Orderable from '../services/orderable.service';

const orderableV1Routes = new Elysia({ prefix: '/orderable' })
	.use(bearer())
	.resolve(async ctx => {
		const idTokenInfo = await getIdTokenInfo(ctx.bearer);
		return {
			Orderable: new Orderable(randomUUID(), idTokenInfo)
		};
	})
	.post(
		'/add-ons',
		async (ctx): Promise<AddOnsOrderRes> => {
			const res = await ctx.Orderable.submitAddOnOrder(ctx.headers, ctx.body);
			ctx.set.status = res.Code;
			return res;
		},
		{
			detail: {
				description:
					'Create orderable add-ons order such as Smart Device, Smart Home, TV Pack, etc.<br><br><b>Table:</b> orderable_txn_history',
				tags: ['Order']
			},
			headers: baseHeaderSchema,
			body: addOnsOrderReqSchema,
			response: {
				201: addOnsOrderResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.post(
		'/ijoin',
		async (ctx): Promise<CreateIjoinOrderRes> => {
			const res = await ctx.Orderable.submitIjoinOrder(ctx.headers, ctx.body);
			ctx.set.status = res.Code;
			return res;
		},
		{
			detail: {
				description:
					'Create orderable New Installation order such as Smart Device, Smart Home, TV Pack, etc. This API is not ready for production use yet. It is released for internal use and testing purposes only.<br><br><b>Table:</b> orderable_txn_history',
				tags: ['Order']
			},
			headers: baseHeaderSchema,
			body: createIjoinOrderReqSchema,
			response: {
				201: createIjoinOrderResSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export default orderableV1Routes;
