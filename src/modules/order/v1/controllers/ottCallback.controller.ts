import { randomUUID } from 'node:crypto';
import Elysia from 'elysia';
import { baseHeaderSchema } from '../../../../shared/schemas/api/headers.schema';
import { baseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type UpdateOrderRes,
	updateOrderReqSchema,
	updateOrderResSchema
} from '../schemas/api/ottCallback.schema';
import OttCallback from '../services/ottCallback.service';

export const ottCallbackV1Routes = new Elysia({ prefix: '/callback/ott' })
	.resolve(() => {
		return {
			OttCallback: new OttCallback(randomUUID())
		};
	})
	.post(
		'/order-update',
		async (ctx): Promise<UpdateOrderRes> => {
			return await ctx.OttCallback.updateOttOrder(ctx.headers.source, ctx.body);
		},
		{
			headers: baseHeaderSchema,
			body: updateOrderReqSchema,
			response: {
				201: updateOrderResSchema,
				500: baseResponseSchema
			},
			detail: {
				description:
					'Update OTT activation status. Triggered by OMG Enterprise System.',
				tags: ['Order', 'Callback']
			}
		}
	);
