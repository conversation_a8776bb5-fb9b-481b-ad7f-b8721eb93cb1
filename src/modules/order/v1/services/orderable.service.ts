import { and, desc, eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import randomString from 'random-string-gen';
import { getDbInstance } from '../../../../config/db.config';
import { AddOnsRequestCategoryEnum } from '../../../../enum/addOns.enum';
import { SegmentEnum, SourceEnum } from '../../../../enum/header.enum';
import { OrderTypeEnum, ProgressStatusEnum } from '../../../../enum/order.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import {
	LightweightFlagEnum,
	SystemNameEnum
} from '../../../../enum/wso2.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type { Wso2ReserveStockRes } from '../../../../integration/wso2/order/schemas/api/wso2ReserveStock';
import type {
	Wso2ServiceAccountReq,
	Wso2ServiceAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2ServiceAccount.schema';
import { UE_ERROR } from '../../../../middleware/error';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import {
	getAddOnsOrderId,
	getMyTimeZoneDate,
	getPrefix
} from '../../../../shared/common';
import { decrypt } from '../../../../shared/encryption/aesGcm';
import type { BaseHeader } from '../../../../shared/schemas/api/headers.schema';
import AddOnsOrderHelper from '../helpers/addOnsOrder.helper';
import AppointmentHelper from '../helpers/appointment.helper';
import type {
	AddOnsOrderReq,
	AddOnsOrderRes
} from '../schemas/api/addOnsOrder.schema';
import type {
	CreateIjoinOrderReq,
	CreateIjoinOrderRes
} from '../schemas/api/iJoinOrder.schema';
import {
	type IjoinCustomerDetails,
	type OrderableProgress,
	type SelectCustomerOrder,
	type SelectOrderableTxnHistory,
	customerOrderTableSchema,
	orderablePlanTableSchema,
	orderableTxnHistoryTableSchema
} from '../schemas/db/orderable.schema';
import StockOrder from './stock.service';

class Orderable {
	private db: NodePgDatabase;
	private integrationId: string;
	private idTokenInfo: IdTokenInfo;
	private appointmentHelper: AppointmentHelper;
	private addOnsOrderHelper: AddOnsOrderHelper;
	private stockOrder: StockOrder;
	private mwIntegration: MwIntegration;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
		this.idTokenInfo = idTokenInfo;
		this.appointmentHelper = new AppointmentHelper(this.integrationId);
		this.addOnsOrderHelper = new AddOnsOrderHelper(this.integrationId);
		this.stockOrder = new StockOrder(this.integrationId);
		this.mwIntegration = new MwIntegration(this.integrationId);
	}

	private async getOrCreateCustomer(
		details: IjoinCustomerDetails
	): Promise<SelectCustomerOrder> {
		let customer = await this.db
			.select()
			.from(customerOrderTableSchema)
			.where(
				and(
					eq(customerOrderTableSchema.IdType, this.idTokenInfo.IdType),
					eq(customerOrderTableSchema.IdValue, this.idTokenInfo.IdValue)
				)
			);

		if (customer.length === 0) {
			customer = await this.db
				.insert(customerOrderTableSchema)
				.values({
					CustomerId: `CUST-${randomString(9).toUpperCase()}`,
					IdType: details.idType,
					IdValue: details.idValue,
					FullName: details.fullName,
					Email: details.email,
					MobileNo: details.mobile
				})
				.returning();
		}

		return customer[0];
	}

	private async getOrderablePlanById(planId: string) {
		return this.db
			.select()
			.from(orderablePlanTableSchema)
			.where(eq(orderablePlanTableSchema.PlanId, planId));
	}

	async submitAddOnOrder(
		headers: BaseHeader,
		req: AddOnsOrderReq
	): Promise<AddOnsOrderRes> {
		const customerData = await this.getOrCreateCustomer({
			idType: this.idTokenInfo.IdType,
			idValue: this.idTokenInfo.IdValue,
			fullName: req.CustomerInfo.FullName,
			email: req.CustomerInfo.BillingEmail,
			mobile: req.CustomerInfo.BillingContactNo
		});

		const decryptedBillAccNo: string = await decrypt(
			req.CustomerInfo.EncryptedBillAccNo
		);

		const resubmissionDays: number =
			req.Category === AddOnsRequestCategoryEnum.SME_SMART_DEVICE ? 30 : 1;

		const openOrder = await this.checkOrderResubmission(
			resubmissionDays,
			decryptedBillAccNo
		);

		if (openOrder) {
			throw new UE_ERROR('Account Has Pending Order', StatusCodeEnum.CONFLICT, {
				integrationId: this.integrationId,
				response: 'ADDON-ORDER_PENDING'
			});
		}

		const wso2SAReq: Wso2ServiceAccountReq = {
			idType: this.idTokenInfo.IdType,
			idValue: this.idTokenInfo.IdValue,
			BillingAccountNo: decryptedBillAccNo,
			SystemName: SystemNameEnum.NOVA
		};

		const wso2SARes: Wso2ServiceAccountRes =
			await this.mwIntegration.Wso2UserIntegration.getWso2ServiceAccount(
				wso2SAReq,
				LightweightFlagEnum.NO,
				true,
				false
			);

		if (
			!wso2SARes?.Response?.ServiceAccount ||
			wso2SARes?.Response.ServiceAccount.length === 0
		) {
			throw new UE_ERROR(
				'No service account data found',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{
					integrationId: this.integrationId,
					response: 'ADDON-ORDER_NO_SERVICE_ACCOUNT'
				}
			);
		}

		const productName: string =
			wso2SARes.Response.ServiceAccount[0].ProductName ?? 'N/A';

		const orderId = await getAddOnsOrderId(getPrefix(req.Category));

		await this.insertInitialOrder(
			req,
			headers,
			orderId,
			customerData.CustomerId,
			decryptedBillAccNo,
			productName
		);

		/**
		 * Note: For all add-on categories except BLACKNUT, the `Products` array will contain only ONE product.
		 * For BLACKNUT, the array can contain up to TWO products: one for the Cloud Gaming (no stock reservation needed),
		 * and one (optional) controller which DOES require stock reservation in MMAG.
		 * Hence, we need to select the first product in the list that has MMAG as the delivery partner for reservation.
		 * */
		switch (req.Category) {
			case AddOnsRequestCategoryEnum.SMART_HOME:
			case AddOnsRequestCategoryEnum.SMART_DEVICE:
			case AddOnsRequestCategoryEnum.SME_SMART_DEVICE:
			case AddOnsRequestCategoryEnum.BLACKNUT:
			case AddOnsRequestCategoryEnum.UPB:
			case AddOnsRequestCategoryEnum.MESH_WIFI: {
				let reservationNo: string | undefined;
				const reserveStockOrder = req.Products.filter(
					product => product.DeliveryPartner?.toUpperCase() === 'MMAG'
				);
				if (reserveStockOrder.length > 0) {
					const reserveItems = reserveStockOrder.map(item => ({
						Item_SKU: item.PartnerId,
						Item_Name: item.ProductName,
						Rate_Plan: item.ProductName,
						Item_Qty: `${item.TotalQuantity}`
					}));
					const reserveStockRes: Wso2ReserveStockRes =
						await this.stockOrder.reserveStock(decryptedBillAccNo, orderId, {
							Items: reserveItems
						});
					reservationNo = reserveStockRes.response.Reservation_No;
				}
				return await this.addOnsOrderHelper.submitOrderDevice(
					orderId,
					req,
					wso2SARes,
					reservationNo
				);
			}
			case AddOnsRequestCategoryEnum.MESH_WIFI_6: {
				if (!req.AppointmentRequest) {
					throw new UE_ERROR(
						'AppointmentRequest information is required for MESH WIFI 6',
						StatusCodeEnum.CONFLICT,
						{
							integrationId: this.integrationId
						}
					);
				}

				const activityId = await this.appointmentHelper.bookAppointment(
					req.AppointmentRequest,
					orderId,
					wso2SARes.Response.ServiceAccount[0].ServicePointId ?? ''
				);

				return await this.addOnsOrderHelper.submitOrderDevice(
					orderId,
					req,
					wso2SARes,
					undefined,
					activityId
				);
			}
			case AddOnsRequestCategoryEnum.TV_PACK: {
				return await this.addOnsOrderHelper.submitOrderTvPack(
					orderId,
					req,
					wso2SARes
				);
			}
			default: {
				throw new UE_ERROR(
					`Category ${req.Category} not in the list`,
					StatusCodeEnum.BAD_REQUEST_ERROR,
					{
						integrationId: this.integrationId
					}
				);
			}
		}
	}

	/**
	 * Insert initial order into the database with progress status as INITIAL.
	 * @param req - AddOnsOrderReq
	 * @param headers - BaseHeader
	 * @param orderId - Order ID
	 * @param customerId - Customer ID
	 * @param billingAccountNo - Billing Account Number
	 * @param productName - Product Name
	 */
	private async insertInitialOrder(
		req: AddOnsOrderReq,
		headers: BaseHeader,
		orderId: string,
		customerId: string,
		billingAccountNo: string,
		productName: string
	): Promise<void> {
		const segment: string = headers.segment ?? SegmentEnum.CONSUMER;
		const source: string = headers.source ?? SourceEnum.UNIFI_APP;

		await this.db
			.insert(orderableTxnHistoryTableSchema)
			.values({
				OrderId: orderId,
				CustomerId: customerId,
				BillingAccountNo: billingAccountNo,
				OrderType: OrderTypeEnum.ADD_ONS,
				OrderCategory: req.Category,
				TargetSystem: SystemNameEnum.NOVA,
				SourceSystem: source,
				OrderSegment: segment,
				ProductName: productName,
				OrderStatus: ProgressStatusEnum.INITIAL,
				OrderProgress: [
					{
						Status: ProgressStatusEnum.INITIAL,
						Timestamp: `${getMyTimeZoneDate().toISOString()}`
					}
				],
				OrderData: req,
				CreatedAt: getMyTimeZoneDate(),
				UpdatedAt: getMyTimeZoneDate()
			})
			.returning()
			.catch(err => {
				throw new UE_ERROR(
					'Failed to insert add-ons order',
					StatusCodeEnum.UE_INTERNAL_SERVER,
					{
						integrationId: this.integrationId,
						response: String(err)
					}
				);
			});
	}

	private async checkOrderResubmission(
		days: number,
		billingAccountNo: string
	): Promise<boolean> {
		const [addonOrder] = await this.db
			.select()
			.from(orderableTxnHistoryTableSchema)
			.where(
				and(
					eq(orderableTxnHistoryTableSchema.BillingAccountNo, billingAccountNo)
				)
			)
			.orderBy(desc(orderableTxnHistoryTableSchema.CreatedAt))
			.limit(1)
			.execute()
			.catch((err: Error) => {
				throw new UE_ERROR(
					'Failed to fetch order from DB',
					StatusCodeEnum.UE_INTERNAL_SERVER,
					{
						integrationId: this.integrationId,
						response: String(err)
					}
				);
			});

		if (
			addonOrder &&
			addonOrder.OrderStatus === ProgressStatusEnum.INPROGRESS
		) {
			const createdDate = new Date(addonOrder.CreatedAt);
			const currentDate = getMyTimeZoneDate();

			const timeDifference = currentDate.getTime() - createdDate.getTime();
			const dayDifference = timeDifference / (1000 * 3600 * 24);

			if (dayDifference <= days) {
				return true;
			}
		}

		return false;
	}

	async submitIjoinOrder(
		headers: BaseHeader,
		req: CreateIjoinOrderReq
	): Promise<CreateIjoinOrderRes> {
		const orderPrefix: string = 'UNFH';
		const orderProgress: OrderableProgress = [
			{
				Status: ProgressStatusEnum.SUBMITTED,
				Timestamp: new Date().toISOString()
			}
		];

		const custDetails: IjoinCustomerDetails = {
			idType: req.Data.PersonalDetails?.IdType ?? '',
			idValue: req.Data.PersonalDetails?.IdNumber ?? '',
			fullName: req.Data.PersonalDetails?.FullName ?? '',
			email: req.Data.PersonalDetails?.Email ?? '',
			mobile: req.MobileNumber ?? ''
		};

		//* Prevent duplication
		const customer = await this.getOrCreateCustomer(custDetails);

		const [orderablePlan] = await this.getOrderablePlanById(
			req.Data.ProductDetails?.PlanID ?? ''
		);

		if (!orderablePlan) {
			throw new UE_ERROR(
				`Orderable plan with id ${req.Data.ProductDetails?.PlanID} not found`,
				StatusCodeEnum.BAD_REQUEST_ERROR,
				{ integrationId: this.integrationId, response: {} }
			);
		}

		const insertOrder: SelectOrderableTxnHistory[] = await this.db
			.insert(orderableTxnHistoryTableSchema)
			.values({
				OrderId:
					req.OrderId || `${orderPrefix}-${randomString(9)?.toUpperCase()}`,
				CustomerId: customer.CustomerId,
				ProductName: orderablePlan?.PlanName,
				OrderType: OrderTypeEnum.NEW_INSTALL_APP,
				OrderCategory: '',
				TargetSystem: '',
				SourceSystem: headers.source ?? SourceEnum.UNIFI_PORTAL,
				OrderSegment: req.Data?.ProductDetails?.MarketType,
				OrderStatus: ProgressStatusEnum.SUBMITTED,
				OrderProgress: orderProgress,
				OrderData: req.Data,
				Address: {
					AddressId: req.Data?.InstallationAddress?.AddressId,
					UnitNo: req.Data?.InstallationAddress?.Unit || '',
					FloorNo: req.Data?.InstallationAddress?.Floor,
					BuildingName: req.Data?.InstallationAddress?.Building,
					StreetType: req.Data?.InstallationAddress?.StreetType || '',
					StreetName: req.Data?.InstallationAddress?.StreetName || '',
					Section: req.Data?.InstallationAddress?.Section || '',
					City: req.Data?.InstallationAddress?.City || '',
					Postcode: req.Data?.InstallationAddress?.PostCode || '',
					State: req.Data?.InstallationAddress?.State || '',
					Country: req.Data?.InstallationAddress?.Country || '',
					Platform: req.Data?.InstallationAddress?.Platform || '',
					Latitude: String(
						req.Data?.SmartMapInstallationAddress?.Location?.Lat
					),
					Longitude: String(
						req.Data?.SmartMapInstallationAddress?.Location?.Lon
					),
					PremiseCategory: req.Data?.InstallationAddress?.PremiseCategory
				}
			})
			.returning();

		const res: CreateIjoinOrderRes = {
			Success: true,
			Code: StatusCodeEnum.CREATED,
			Message: 'Order submitted successfully',
			IntegrationId: this.integrationId,
			Response: {
				MobileNumber: customer.MobileNo,
				OrderId: insertOrder[0].OrderId,
				SourceChannel: insertOrder[0].SourceSystem ?? SourceEnum.UNIFI_PORTAL,
				ProductCategory: orderablePlan?.PlanCategory,
				OrderProgress: orderProgress,
				OrderStatus: insertOrder[0].OrderStatus,
				Data: insertOrder[0].OrderData,
				CreatedDate: insertOrder[0].CreatedAt,
				UpdatedDate: insertOrder[0].UpdatedAt
			}
		};
		return res;
	}
}

export default Orderable;
