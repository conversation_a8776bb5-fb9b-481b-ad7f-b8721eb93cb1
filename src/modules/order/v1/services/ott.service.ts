import { and, eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { envConfig } from '../../../../config/env.config';
import {
	ActivationCategoryEnum,
	NetflixOttTypeEnum,
	OmgChannelEnum,
	OmgResponseCodeEnum,
	OmgTokenTypeEnum,
	OttPlanTypeEnum
} from '../../../../enum/addOns.enum';
import { SourceEnum } from '../../../../enum/header.enum';
import { EmailEnum } from '../../../../enum/notification.enum';
import { ProgressStatusEnum } from '../../../../enum/order.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import type { DisneyCheckChangeMobileEmailTemplateReq } from '../../../../integration/emailTemplate/schemas/api/ottEmailTemplate.schema';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	OmgActivateOttReq,
	OmgActivateOttRes
} from '../../../../integration/omg/schemas/api/omgActivateOtt.schema';
import type {
	OmgDisneyChangeMobileReq,
	OmgDisneyChangeMobileRes
} from '../../../../integration/omg/schemas/api/omgDisneyChangeMobile.schema';
import type { OmgGetNetflixAccRecoveryUrlRes } from '../../../../integration/omg/schemas/api/omgNetflixAccountRecovery.schema';
import type {
	OmgNetflixActivationUrlReq,
	//OmgNetflixActivationUrlReq,
	OmgNetflixActivationUrlRes
} from '../../../../integration/omg/schemas/api/omgNetflixActivation.schema';
import type {
	OmgNetflixCancelPlanReq,
	OmgNetflixCancelPlanRes
} from '../../../../integration/omg/schemas/api/omgNetflixCancelPlan.schema';
import type {
	OmgNetflixChangePlanReq,
	OmgNetflixChangePlanRes
} from '../../../../integration/omg/schemas/api/omgNetflixChangePlan.schema';
import type { OmgGetCurrentNetflixPlanRes } from '../../../../integration/omg/schemas/api/omgNetflixCurrentPlan.schema';
import type {
	OmgGetOttActivationAlaCarteUrlReq,
	OmgGetOttActivationAlaCarteUrlRes,
	//OmgGetOttActivationBundleUrlReq,
	OmgGetOttActivationBundleUrlRes
} from '../../../../integration/omg/schemas/api/omgOttActivation.schema';
import type { OmgOttEntitlementRes } from '../../../../integration/omg/schemas/api/omgOttEntitlement.schema';
import type {
	OmgOttSwapOrderReq,
	OmgOttSwapOrderRes
} from '../../../../integration/omg/schemas/api/omgSwapOtt.schema';
import type {
	OmgOttUserVerifyReq,
	OmgOttUserVerifyRes
} from '../../../../integration/omg/schemas/api/omgVerifyOttUserId.schema';
import type { Wso2EmailReq } from '../../../../integration/wso2/notification/schemas/api/wso2Email.schema';
import { UE_ERROR } from '../../../../middleware/error';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import { getAddOnsOrderId, getMyTimeZoneDate } from '../../../../shared/common';
import type { BaseResponse } from '../../../../shared/schemas/api/responses.schema';
import {
	type NetflixPlanList,
	ottPlanCatalogueTableSchema
} from '../../../catalogue/v1/schemas/db/ottPlanCatalogue.schema';
import { tvPackCatalogueTableSchema } from '../../../catalogue/v1/schemas/db/tvPackCatalogue.schema';
import AccountService from '../../../user/v1/helpers/accountService.helper';
import type {
	DisneyChangeMobileReq,
	DisneyChangeMobileRes
} from '../schemas/api/disneyChangeMobile.schema';
import type {
	NetflixAccRecoveryReqType,
	NetflixAccRecoveryResType
} from '../schemas/api/netflixAccRecovery.schema';
import type { NetflixCancelPlanReq } from '../schemas/api/netflixCancelPlan.schema';
import type {
	NetflixChangePlanReqType,
	NetflixChangePlanRes
} from '../schemas/api/netflixChangePlan.schema';
import type {
	NetflixCurrentPlanReq,
	NetflixCurrentPlanRes
} from '../schemas/api/netflixCurrentPlan.schema';
import type {
	OttActivateReq,
	OttActivateRes
} from '../schemas/api/ottActivate.schema';
import type {
	OttActivationUrlReq,
	OttActivationUrlRes
} from '../schemas/api/ottActivationUrl.schema';
import type {
	OttEntitlementReq,
	OttEntitlementRes
} from '../schemas/api/ottEntitlement.schema';
import type { OttSwapReq, OttSwapRes } from '../schemas/api/ottSwap.schema';
import type {
	OttVerifyUserReq,
	OttVerifyUserRes
} from '../schemas/api/ottVerifyUser.schema';
import {
	type SelectOttOrder,
	ottOrderTableSchema
} from '../schemas/db/ottOrder.schema';

class Ott {
	private db: NodePgDatabase;
	private integrationId: string;
	private idTokenInfo: IdTokenInfo;
	private mwIntegration: MwIntegration;
	private accountService: AccountService;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo) {
		this.integrationId = integrationId;
		this.db = getDbInstance();
		this.idTokenInfo = idTokenInfo;
		this.mwIntegration = new MwIntegration(integrationId);
		this.accountService = new AccountService(integrationId);
	}

	async getOttEntitlement(req: OttEntitlementReq): Promise<OttEntitlementRes> {
		const omgRes: OmgOttEntitlementRes =
			await this.mwIntegration.OmgIntegration.getOmgOttEntitlement({
				accountType: req.AccountType,
				accountId: req.AccountId,
				ottMerchantId: req.OttMerchantId
			});

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				ResponseCode: omgRes.responseCode,
				ResponseMsg: omgRes.responseMsg
			}
		};
	}

	async userVerify(req: OttVerifyUserReq): Promise<OttVerifyUserRes> {
		const omgReq: OmgOttUserVerifyReq = {
			accountType: req.AccountType,
			accountId: req.AccountId,
			ottMerchantId: req.OttMerchantId,
			ottUserId: req.OttUserId
		};

		const omgRes: OmgOttUserVerifyRes =
			await this.mwIntegration.OmgIntegration.getOmgOttUserVerify(omgReq);

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				IsAvailable: omgRes.responseMsg === 'Available'
			}
		};
	}

	async activateOtt(
		_source: SourceEnum,
		req: OttActivateReq
	): Promise<OttActivateRes> {
		const hasAccessToServiceId: boolean =
			await this.accountService.hasAccessToServiceId(
				this.idTokenInfo.IdType,
				this.idTokenInfo.IdValue,
				req.AccountId
			);
		if (!hasAccessToServiceId) {
			throw new UE_ERROR(
				'You do not have access to this service',
				StatusCodeEnum.FORBIDDEN_ERROR,
				{
					integrationId: this.integrationId,
					response: {}
				}
			);
		}

		// Get the current date
		const currentDate: Date = getMyTimeZoneDate();

		//find the ott order by account Id and status and category
		const openOrder: SelectOttOrder[] = await this.db
			.select()
			.from(ottOrderTableSchema)
			.where(
				and(
					eq(ottOrderTableSchema.AccountId, req.AccountId),
					eq(ottOrderTableSchema.Status, ProgressStatusEnum.SUBMITTED),
					eq(ottOrderTableSchema.Category, 'Activation')
				)
			)
			.execute();

		if (openOrder.length > 0) {
			for (const order of openOrder) {
				if (order.CreatedAt) {
					// Convert to JS Date object
					const createdDate: Date = new Date(order.CreatedAt);
					// Convert to UTC date only (ignoring time)
					const createdDateOnly: Date = new Date(
						createdDate.toISOString().split('T')[0]
					);
					const currentDateOnly: Date = new Date(
						currentDate.toISOString().split('T')[0]
					);
					const diffInDays: number =
						(currentDateOnly.getTime() - createdDateOnly.getTime()) /
						(1000 * 60 * 60 * 24);
					if (diffInDays <= 1) {
						throw new UE_ERROR(
							'Account Has Pending Activation Order',
							StatusCodeEnum.CONFLICT,
							{
								integrationId: this.integrationId
							}
						);
					}
				}
			}
		}

		// Determine the category based on ottPlanId
		const category =
			req.OttPlanId === 'P7' || req.OttPlanId === 'P8'
				? 'OTT_ALA_CARTE'
				: 'OTT_ACTIVATION';

		const orderRefNo: string = await getAddOnsOrderId('OTT');
		// Insert the new order into the database
		await this.db
			.insert(ottOrderTableSchema)
			.values({
				OttPlanId: req.OttPlanId,
				AccountType: req.AccountType,
				AccountId: req.AccountId,
				OrderRefNo: orderRefNo,
				OttOrder: JSON.stringify(req.OttOrder),
				Status: ProgressStatusEnum.INITIAL,
				CustName: req.CustName,
				CustEmail: req.CustEmail,
				TvPackName: req.TvPackName,
				IptvId: req.IptvId,
				Category: category,
				Source: _source
			})
			.returning({ id: ottOrderTableSchema.Id })
			.execute();

		// create OMG order
		const omgActivateOttReq: OmgActivateOttReq = {
			ottPlanId: req.OttPlanId,
			accountType: req.AccountType,
			accountId: req.AccountId,
			orderRefNo: orderRefNo,
			ottOrder: req.OttOrder.map(ottOrder => ({
				ottMerchantId: ottOrder.OttMerchantId,
				ottProductId: ottOrder.OttProductId,
				ottOmgId: ottOrder.OttOmgId,
				ottLoginType: ottOrder.OttLoginType,
				ottUserId: ottOrder.OttUserId
			}))
		};
		//submit order to OMG
		const omgOttOrderResponse: OmgActivateOttRes =
			await this.mwIntegration.OmgIntegration.omgOttOrder(omgActivateOttReq);

		//update the order status
		await this.db
			.update(ottOrderTableSchema)
			.set({ Status: ProgressStatusEnum.SUBMITTED })
			.where(eq(ottOrderTableSchema.OrderRefNo, orderRefNo));

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Message: omgOttOrderResponse.responseMsg,
			Response: {
				OrderRefNo: orderRefNo
			}
		};
	}

	async swapOtt(_source: SourceEnum, req: OttSwapReq): Promise<OttSwapRes> {
		// Generate order reference number
		const orderRefNo: string = await getAddOnsOrderId('OTT');

		await this.db
			.insert(ottOrderTableSchema)
			.values({
				OttPlanId: req.OttPlanId,
				AccountType: req.AccountType,
				AccountId: req.AccountId,
				OrderRefNo: orderRefNo,
				OttOrder: JSON.stringify(req), // Convert object/array to string
				Status: ProgressStatusEnum.INITIAL,
				CustName: req.CustName,
				CustEmail: req.CustEmail,
				TvPackName: req.TvPackName,
				IptvId: req.IptvId,
				Category: 'Swapping',
				Source: _source
			})
			.returning({ id: ottOrderTableSchema.Id })
			.execute();

		// create OMG order
		const omgSwapOttOrder: OmgOttSwapOrderReq = {
			ottPlanId: req.OttPlanId,
			accountType: req.AccountType,
			accountId: req.AccountId,
			orderRefNo: orderRefNo,
			ottTxnId: req.OttTxnId,
			ottMerchantId: req.OttMerchantId,
			ottProductId: req.OttProductId,
			ottOmgId: req.OttOmgId,
			ottLoginType: req.OttLoginType,
			ottUserId: req.OttUserId
		};

		//submit swap order to OMG
		const ottSwapOrderRes: OmgOttSwapOrderRes =
			await this.mwIntegration.OmgIntegration.omgOttSwapOrder(omgSwapOttOrder);

		//update the order status
		await this.db
			.update(ottOrderTableSchema)
			.set({ Status: ProgressStatusEnum.SUBMITTED })
			.where(eq(ottOrderTableSchema.OrderRefNo, orderRefNo));

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Message: ottSwapOrderRes.responseMsg,
			Response: {
				OrderRefNo: ottSwapOrderRes.orderRefNo
			}
		};
	}

	async getCurrentNetflixPlan(
		req: NetflixCurrentPlanReq
	): Promise<NetflixCurrentPlanRes> {
		const omgRes: OmgGetCurrentNetflixPlanRes =
			await this.mwIntegration.OmgIntegration.getCurrentNetflixPlan({
				accountType: req.AccountType,
				accountId: req.AccountId
			});

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Message: omgRes.responseMsg,
			Response:
				omgRes.netflixCurrentPlan?.map(plan => ({
					NetflixPai: plan.netflixPAI,
					NetflixBundleId: plan.netflixBundleId,
					NetflixOfferId: plan.netflixOfferId,
					TMbundleId: plan.TMbundleId
				})) || []
		};
	}

	async changeNetflixPlanService(
		req: NetflixChangePlanReqType,
		source: string
	): Promise<NetflixChangePlanRes> {
		// Step 1: Validate user access
		const hasAccess = await this.accountService.hasAccessToServiceId(
			this.idTokenInfo.IdType,
			this.idTokenInfo.IdValue,
			req.AccountId
		);

		if (!hasAccess) {
			throw new UE_ERROR('Access Denied', StatusCodeEnum.FORBIDDEN_ERROR, {
				integrationId: this.integrationId,
				response: `Access denied for account ID: ${req.AccountId}`
			});
		}

		// Step 2: Fetch Netflix plan details and TvPackName using a JOIN query
		const planDetails = await this.db
			.select({
				ottSelectionNetflix: ottPlanCatalogueTableSchema.OttSelectionNetflix,
				tvPackName: tvPackCatalogueTableSchema.TvPackName
			})
			.from(ottPlanCatalogueTableSchema)
			.innerJoin(
				tvPackCatalogueTableSchema,
				eq(
					ottPlanCatalogueTableSchema.PlanId,
					tvPackCatalogueTableSchema.PlanId
				)
			)
			.where(eq(ottPlanCatalogueTableSchema.PlanId, req.OttPlanId))
			.limit(1);

		if (!planDetails.length || !planDetails[0].ottSelectionNetflix.length) {
			throw new UE_ERROR(
				'Invalid Netflix Plan',
				StatusCodeEnum.BAD_REQUEST_ERROR,
				{
					integrationId: this.integrationId,
					response: `No Netflix plan found for OttPlanId: ${req.OttPlanId}`
				}
			);
		}

		const ottSelectionNetflix: NetflixPlanList =
			planDetails[0].ottSelectionNetflix;
		const tvPackName: string = planDetails[0].tvPackName;

		const selectedNetflixPlan = ottSelectionNetflix.find(
			plan => plan.TmBundleId === req.NewTmBundleId
		);

		if (!selectedNetflixPlan) {
			throw new UE_ERROR(
				'Invalid NewTmBundleId',
				StatusCodeEnum.BAD_REQUEST_ERROR,
				{
					integrationId: this.integrationId,
					response: `No matching Netflix plan found for NewTmBundleId: ${req.NewTmBundleId}`
				}
			);
		}

		const netflixBundleId: string = selectedNetflixPlan.NetflixBundleId ?? '';
		const netflixOfferId: string = selectedNetflixPlan.NetflixOfferId ?? '';

		if (!netflixBundleId || !netflixOfferId) {
			throw new UE_ERROR(
				'Invalid Netflix Plan Data',
				StatusCodeEnum.BAD_REQUEST_ERROR,
				{
					integrationId: this.integrationId,
					response: `NetflixBundleId or NetflixOfferId missing for NewTmBundleId: ${req.NewTmBundleId}`
				}
			);
		}

		// Step 3: Generate Netflix Billing Event ID
		const netflixBillingEventId: string = await getAddOnsOrderId('NETFLIX');

		// Step 4: Convert `source` to `OmgChannelEnum` before sending to OMG
		const omgChannel: OmgChannelEnum = (() => {
			switch (source) {
				case SourceEnum.UNIFI_PORTAL:
					return OmgChannelEnum.Web;
				case SourceEnum.UNIFI_APP:
					return OmgChannelEnum.Mobile;
				default:
					return OmgChannelEnum.Other;
			}
		})();

		// Step 5: Retrieve Netflix Partner Account Identification (PAI)
		const getCurrentNetflixPlan: OmgGetCurrentNetflixPlanRes =
			await this.mwIntegration.OmgIntegration.getCurrentNetflixPlan({
				accountType: req.AccountType,
				accountId: req.AccountId
			});

		if (
			!getCurrentNetflixPlan.netflixCurrentPlan ||
			!getCurrentNetflixPlan.netflixCurrentPlan.length
		) {
			throw new UE_ERROR(
				'Netflix PAI not found',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{
					integrationId: this.integrationId,
					response: `No Netflix PAI found for accountId: ${req.AccountId}`
				}
			);
		}

		const netflixPai = getCurrentNetflixPlan.netflixCurrentPlan[0].netflixPAI;

		// Step 6: Build integration request for OMG API
		const integrationRequest: OmgNetflixChangePlanReq = {
			accountType: req.AccountType,
			accountId: req.AccountId,
			netflixPAI: netflixPai,
			netflixBundleId: netflixBundleId,
			netflixBillingEventId: netflixBillingEventId,
			netflixNewOfferId: netflixOfferId,
			channel: omgChannel,
			TMnewBundleId: req.NewTmBundleId
		};

		// Step 8: Call OMG API for plan change
		const changeNetflixPlan: OmgNetflixChangePlanRes =
			await this.mwIntegration.OmgIntegration.changeNetflixPlan(
				integrationRequest
			);

		// Step 9: Handle different response codes from OMG
		if (changeNetflixPlan.responseCode === '200') {
			await this.db.insert(ottOrderTableSchema).values({
				OttPlanId: req.OttPlanId,
				AccountType: req.AccountType,
				AccountId: req.AccountId,
				OrderRefNo: netflixBillingEventId,
				OttOrder: JSON.stringify(integrationRequest),
				Status: ProgressStatusEnum.COMPLETED,
				CustName: req.CustName,
				CustEmail: req.CustEmail,
				TvPackName: tvPackName,
				IptvId: 'N/A',
				Category: NetflixOttTypeEnum.NETFLIX_CHANGE_PLAN,
				Source: source
			});
			return {
				Success: true,
				Code: StatusCodeEnum.CREATED,
				IntegrationId: this.integrationId,
				Response: {
					OrderRefNo: netflixBillingEventId
				}
			};
		}

		switch (changeNetflixPlan.responseCode) {
			case '400':
				throw new UE_ERROR(
					'Incorrect parameters sent',
					StatusCodeEnum.BAD_REQUEST_ERROR,
					{
						integrationId: this.integrationId,
						response: changeNetflixPlan
					}
				);
			case '403':
				throw new UE_ERROR(
					'User account is inactive',
					StatusCodeEnum.FORBIDDEN_ERROR,
					{
						integrationId: this.integrationId,
						response: changeNetflixPlan
					}
				);
			default:
				throw new UE_ERROR(
					'OMG service encountered an error',
					StatusCodeEnum.OMG_ERROR,
					{
						integrationId: this.integrationId,
						response: changeNetflixPlan
					}
				);
		}
	}

	async cancelNetflixPlan(req: NetflixCancelPlanReq): Promise<BaseResponse> {
		const omgReq: OmgNetflixCancelPlanReq = {
			accountType: req.AccountType,
			accountId: req.AccountId,
			ottTxnId: req.OttTxnId,
			netflixTxnId: req.NetflixTxnId
		};
		const omgRes: OmgNetflixCancelPlanRes =
			await this.mwIntegration.OmgIntegration.omgCancelNetflixPlan(omgReq);

		switch (Number(omgRes.responseCode)) {
			case 200:
			case 201:
				return {
					Success: true,
					Code: StatusCodeEnum.CREATED,
					IntegrationId: this.integrationId,
					Message:
						'Your request has been received and is currently being processed. Please check the Entertainment page shortly for the latest update.'
				};
			case 410:
				throw new UE_ERROR(
					'Your Netflix cancellation request could not be processed because it has exceeded 24 hours. To proceed with cancellation, please visit the Netflix website.',
					StatusCodeEnum.FORBIDDEN_ERROR,
					{
						integrationId: this.integrationId
					}
				);
			default:
				throw new UE_ERROR(
					'Your Netflix cancellation request was unsuccessful. Please try again on the Entertainment page.',
					StatusCodeEnum.OMG_ERROR,
					{
						integrationId: this.integrationId
					}
				);
		}
	}

	async getNetflixAccRecoveryUrl(
		request: NetflixAccRecoveryReqType,
		source: string
	): Promise<NetflixAccRecoveryResType> {
		// Step 1: Validate user access
		const hasAccess: boolean = await this.accountService.hasAccessToServiceId(
			this.idTokenInfo.IdType,
			this.idTokenInfo.IdValue,
			request.AccountId
		);

		if (!hasAccess) {
			throw new UE_ERROR('Access Denied', StatusCodeEnum.FORBIDDEN_ERROR, {
				integrationId: this.integrationId,
				response: `Access denied for account ID: ${request.AccountId}`
			});
		}

		// Step 2: Validate if the user has an active Netflix subscription
		const getCurrentNetflixPlan: OmgGetCurrentNetflixPlanRes =
			await this.mwIntegration.OmgIntegration.getCurrentNetflixPlan({
				accountType: request.AccountType,
				accountId: request.AccountId
			});

		if (
			!getCurrentNetflixPlan.netflixCurrentPlan ||
			!getCurrentNetflixPlan.netflixCurrentPlan.length
		) {
			throw new UE_ERROR(
				'Netflix PAI not found',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{
					integrationId: this.integrationId,
					response: `User did not subscribe to any Netflix plan!: ${request.AccountId}`
				}
			);
		}

		const tokenRefNo: string = await getAddOnsOrderId('NETFLIX');

		// Step 4: Determine the correct OMG channel
		const tokenChannel: OmgChannelEnum = (() => {
			switch (source) {
				case SourceEnum.UNIFI_PORTAL:
					return OmgChannelEnum.Web;
				case SourceEnum.UNIFI_APP:
					return OmgChannelEnum.Mobile;
				default:
					return OmgChannelEnum.Other;
			}
		})();

		// Step 5: Retrieve netflix account recovery token url
		const omgResponse: OmgGetNetflixAccRecoveryUrlRes =
			await this.mwIntegration.OmgIntegration.getNetflixAccRecoveryToken({
				accountType: request.AccountType,
				accountId: request.AccountId,
				tokenRefNo,
				tokenType: OmgTokenTypeEnum.AccountRecovery,
				tokenChannel,
				tokenErrorURL: request.TokenErrorURL
			});

		if (
			!omgResponse ||
			!omgResponse.tokenURL ||
			omgResponse.responseCode !== OmgResponseCodeEnum.CREATED
		) {
			throw new UE_ERROR('OMG API Error', StatusCodeEnum.OMG_ERROR, {
				integrationId: this.integrationId,
				response: omgResponse
			});
		}

		try {
			await this.db.insert(ottOrderTableSchema).values({
				OttPlanId: request.OttPlanId,
				AccountType: request.AccountType,
				AccountId: request.AccountId,
				OrderRefNo: tokenRefNo,
				Status: ProgressStatusEnum.COMPLETED,
				CustName: request.CustName,
				CustEmail: request.CustEmail,
				TvPackName: 'N/A',
				IptvId: 'N/A',
				Category: NetflixOttTypeEnum.NETFLIX_RECOVERY,
				Source: source
			});
		} catch (error) {
			throw new UE_ERROR(
				'Database Error',
				StatusCodeEnum.UNPROCESSABLE_ENTITY,
				{
					integrationId: this.integrationId,
					response: 'Failed to save Netflix Recovery Order in the database'
				}
			);
		}

		// Step 8: Return the Response
		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				NetflixAccRecoveryUrl: omgResponse.tokenURL
			}
		};
	}

	async getCheckChangeDisneyMobile(
		req: DisneyChangeMobileReq
	): Promise<DisneyChangeMobileRes> {
		const omgDisneyChangeMobileReq: OmgDisneyChangeMobileReq = {
			ottTxnId: req.OttTxnId,
			accountId: req.AccountId,
			accountType: req.AccountType,
			mode: 'check',
			oldMobileNo: req.OldMobileNo,
			newMobileNo: req.NewMobileNo
		};

		//Disney Change Mobile check request to OMG
		const omgDisneyChangeMobileRes: OmgDisneyChangeMobileRes =
			await this.mwIntegration.OmgIntegration.omgGetDisneyChangeMobile(
				omgDisneyChangeMobileReq
			);

		if (omgDisneyChangeMobileRes.responseCode === '200') {
			const emailFrom: string = EmailEnum.FROM_NOREPLY;
			const emailTo: string = req.BillingEmail;

			const url: string = envConfig().DISNEY_CHANGE_MOBILE_EMAIL_URL;
			const emailTemplateReq: DisneyCheckChangeMobileEmailTemplateReq = {
				ottUniversalLink: 'https://hotstarid.onelink.me/p2eR/tw85ins1',
				customerName: req.AccountId,
				mobileNo: req.NewMobileNo
			};

			const emailBody: string =
				await this.mwIntegration.EmailTemplateIntegration.getEmailBodyTemplateWithBodyRequest(
					url,
					emailTemplateReq
				);

			const wso2EmailReq: Wso2EmailReq = {
				to: emailTo,
				cc: '',
				from: emailFrom,
				subject: 'Successful change Disney+ Mobile No',
				body: emailBody
			};

			this.mwIntegration
				.getWso2NotificationIntegration()
				.getWso2SendEmail(
					wso2EmailReq,
					req.OttTxnId.toString(),
					'change Disney+ Mobile No'
				);

			return {
				Success: true,
				Code: StatusCodeEnum.CREATED,
				IntegrationId: this.integrationId,
				Message: 'Successful change Disney+ Mobile No',
				Response: {
					NextAllowDate: null
				}
			};
		}

		if (omgDisneyChangeMobileRes.responseCode === '403') {
			return {
				Success: false,
				Code: StatusCodeEnum.FORBIDDEN_ERROR,
				IntegrationId: this.integrationId,
				Message: 'Failed to change Disney+ Mobile No',
				Response: {
					NextAllowDate: omgDisneyChangeMobileRes.nextAllowDate.toString()
				}
			};
		}

		throw new UE_ERROR(
			`Failed to change Disney+ Mobile No: ${omgDisneyChangeMobileRes.responseMsg}`,
			StatusCodeEnum.OMG_ERROR,
			{
				integrationId: this.integrationId
			}
		);
	}

	async getOttActivationUrl(
		req: OttActivationUrlReq
	): Promise<OttActivationUrlRes> {
		let url: string;

		//check account accessibility
		const hasAccess: boolean = await this.accountService.hasAccessToServiceId(
			this.idTokenInfo.IdType,
			this.idTokenInfo.IdValue,
			req.AccountId
		);

		if (!hasAccess) {
			throw new UE_ERROR('Access Denied', StatusCodeEnum.FORBIDDEN_ERROR, {
				integrationId: this.integrationId,
				response: `Access denied for account ID: ${req.AccountId}`
			});
		}

		if (req.Category === ActivationCategoryEnum.OTHERS) {
			//hbo
			const ottPlanType = req.Others?.OttPlanType;

			if (ottPlanType === OttPlanTypeEnum.ALA_CARTE) {
				//ala carte
				const omgGetHboActivationAlaCarteUrlRes: OmgGetOttActivationAlaCarteUrlRes =
					await this.getOttAlaCarteActivationUrl(req);
				url = omgGetHboActivationAlaCarteUrlRes.tokenURL;

				if (omgGetHboActivationAlaCarteUrlRes.responseCode !== '201') {
					throw new UE_ERROR(
						'OMG Error - Invalid Activation URL',
						StatusCodeEnum.OMG_ERROR,
						{
							integrationId: this.integrationId,
							response: null
						}
					);
				}
			} else {
				//bundle
				const omgGetHboActivationBundleUrlRes: OmgGetOttActivationBundleUrlRes =
					await this.getOttBundleActivationUrl(req);
				url = omgGetHboActivationBundleUrlRes.bundleTokenURL;

				if (omgGetHboActivationBundleUrlRes.responseCode !== '200') {
					throw new UE_ERROR(
						'OMG Error - Invalid Activation URL',
						StatusCodeEnum.OMG_ERROR,
						{
							integrationId: this.integrationId,
							response: null
						}
					);
				}
			}
		} else {
			//netflix
			//check netflix account entitlement
			const omgRes: OmgOttEntitlementRes =
				await this.mwIntegration.OmgIntegration.getOmgOttEntitlement({
					accountType: req.AccountType,
					accountId: req.AccountId,
					ottMerchantId: 14
				});

			if (omgRes.responseCode !== '000') {
				throw new UE_ERROR(
					'You are not entitled to activate this Netflix plan!',
					StatusCodeEnum.OMG_ERROR,
					{
						integrationId: this.integrationId,
						response: null
					}
				);
			}

			const omgNetflixActivationUrlRes: OmgNetflixActivationUrlRes =
				await this.getNetflixActivationUrl(req);
			url = omgNetflixActivationUrlRes.tokenURL;

			if (omgNetflixActivationUrlRes.responseCode !== '201') {
				throw new UE_ERROR(
					'OMG Error - Invalid Activation URL',
					StatusCodeEnum.OMG_ERROR,
					{
						integrationId: this.integrationId,
						response: null
					}
				);
			}
		}

		return {
			Success: true,
			IntegrationId: this.integrationId,
			Code: StatusCodeEnum.OK,
			Response: {
				Url: url
			}
		};
	}

	private async getOttAlaCarteActivationUrl(req: OttActivationUrlReq) {
		const tokenRefNo: string = await getAddOnsOrderId('OTT');
		const omgReq: OmgGetOttActivationAlaCarteUrlReq = {
			accountType: req.AccountType,
			accountId: req.AccountId,
			ottMerchantId: req.Others?.OttMerchantId ?? 0,
			tokenRefNo: tokenRefNo
		};

		return await this.mwIntegration.OmgIntegration.getOmgOttActivationAlaCarteUrl(
			omgReq
		);
	}

	private async getOttBundleActivationUrl(
		req: OttActivationUrlReq
	): Promise<OmgGetOttActivationBundleUrlRes> {
		const tokenRefNo: string = await getAddOnsOrderId('OTT');
		const omgReq = {
			//: OmgGetOttActivationBundleUrlReq
			accountType: req.AccountType,
			accountId: req.AccountId,
			txnRefNo: tokenRefNo,
			ottPlanId: req.Others?.OttPlanId ?? '',
			ottProductId: req.Others?.OttProductId ?? '',
			ottPlanType: req.Others?.OttPlanType ?? '',
			ottMerchantId: req.Others?.OttMerchantId ?? 0
		};

		return await this.mwIntegration.OmgIntegration.getOmgOttActivationBundleUrl(
			omgReq
		);
	}

	private async getNetflixActivationUrl(
		req: OttActivationUrlReq
	): Promise<OmgNetflixActivationUrlRes> {
		const tokenRefNo: string = await getAddOnsOrderId('OTT');
		const omgReq: OmgNetflixActivationUrlReq = {
			accountType: req.AccountType,
			accountId: req.AccountId,
			tokenErrorUrl: req.Netflix?.TokenErrorUrl ?? '',
			tokenRefNo: tokenRefNo,
			tokenType: req.Netflix?.TokenType ?? '',
			tokenChannel: req.Netflix?.TokenChannel ?? '',
			tokenPromoID: req.Netflix?.PromoCode
		};

		return await this.mwIntegration.OmgIntegration.getOmgNetflixActivationUrl(
			omgReq
		);
	}
}

export default Ott;
