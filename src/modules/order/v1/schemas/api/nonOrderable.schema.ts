import { type Static, t } from 'elysia';
import { SystemNameEnum } from '../../../../../enum/wso2.enum';

export const nonOrderableReqSchema = t.Object({
	FullName: t.String(),
	Email: t.String(),
	MobileNo: t.String(),
	ServiceId: t.String(),
	Category: t.String(),
	ProductName: t.String(),
	SystemName: t.Enum(SystemNameEnum),
	Segment: t.String(),
	SlofData: t.String()
});

export type NonOrderableReq = Static<typeof nonOrderableReqSchema>;
