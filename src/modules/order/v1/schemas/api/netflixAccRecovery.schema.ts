import { type Static, t } from 'elysia';
import { AccountTypeEnum } from '../../../../../enum/addOns.enum';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const netflixAccRecoveryReqSchema = t.Object({
	CustName: t.String({
		description: 'Customer Name',
		example: '<PERSON>'
	}),
	CustEmail: t.String({
		description: 'Customer Email Address',
		example: '<EMAIL>'
	}),
	AccountType: t.Enum(AccountTypeEnum, {
		description: 'Type of account (Broadband/Mobile)',
		example: AccountTypeEnum.Broadband
	}),
	AccountId: t.String({
		description: 'User account ID',
		example: 'johndoe@unifi'
	}),
	OttPlanId: t.String({
		description: 'OTT Plan ID to identify the user’s plan',
		example: 'P39'
	}),
	TokenErrorURL: t.String({
		description: 'URL to redirect if an error occurs.',
		format: 'uri',
		example: 'https://example.com/error'
	})
});

export type NetflixAccRecoveryReqType = Static<
	typeof netflixAccRecoveryReqSchema
>;

export const netflixAccRecoveryResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			NetflixAccRecoveryUrl: t.String({
				format: 'uri',
				example: 'https://netflix.com/recover/12345',
				description: 'The Netflix account recovery URL returned by OMG.'
			})
		})
	},
	{
		description: 'Returns the Netflix account recovery URL for the user.'
	}
);

export type NetflixAccRecoveryResType = Static<
	typeof netflixAccRecoveryResSchema
>;
