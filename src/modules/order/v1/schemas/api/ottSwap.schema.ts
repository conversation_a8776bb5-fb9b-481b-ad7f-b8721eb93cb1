import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

//Swap request
export const ottSwapReqSchema = t.Object({
	OttPlanId: t.String({ example: 'P39' }),
	AccountType: t.String({ example: 'Broadband' }),
	AccountId: t.String({ example: 'johndoe@unifi' }),
	OttTxnId: t.Number({ example: 1001 }),
	OttMerchantId: t.Number({ example: 38 }),
	OttProductId: t.String({ example: 'Broadband' }),
	OttOmgId: t.Number({ example: 141 }),
	OttLoginType: t.String({ example: 'mobile' }),
	OttUserId: t.String({ example: 'johndoe@ott' }),
	CustName: t.String({ example: '<PERSON> Doe' }),
	CustEmail: t.String({ example: '<PERSON><PERSON><PERSON>@gmail.com' }),
	TvPackName: t.String({ example: 'Aneka Pack' }),
	IptvId: t.String({ example: 'johndoe@iptv' })
});

export type OttSwapReq = Static<typeof ottSwapReqSchema>;

export const ottSwapResSchema = t.Object({
	...baseResponseSchema.properties,
	Response: t.Object({
		OrderRefNo: t.String({ examples: ['OTT-698964'] })
	})
});

export type OttSwapRes = Static<typeof ottSwapResSchema>;
