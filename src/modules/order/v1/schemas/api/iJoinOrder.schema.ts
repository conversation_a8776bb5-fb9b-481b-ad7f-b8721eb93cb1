import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';
import {
	ijoinInstallationAddressSchema,
	orderableJSONDataSchema,
	orderableProgressSchema
} from '../db/orderable.schema';

export const createIjoinOrderReqSchema = t.Object({
	MobileNumber: t.String({ examples: ['+60123456789'] }),
	OrderId: t.Optional(t.Nullable(t.String({ examples: ['UNFH-ABC123DEF'] }))),
	SourceChannel: t.String({ examples: ['IJOIN-HOME'] }),
	ProductCategory: t.String({ examples: ['Home Broadband'] }),
	OrderStatus: t.String({ examples: ['SUBMITTED'] }),
	Data: t.Object({
		ProductDetails: t.Object({
			PlanID: t.String({ examples: ['500Mbps-PP-OKB-3MW-149'] }),
			PlanName: t.String({ examples: ['Unifi 500Mbps - Home Broadband'] }),
			PlanFee: t.String({ examples: ['149.00'] }),
			PromoCode: t.Optional(t.String()),
			AgentName: t.Optional(t.String()),
			PlanType: t.String({ examples: ['Unifi'] }),
			PlanHybrid: t.String({ examples: ['Higher'] }),
			HsiOffer: t.String({ examples: ['nova-cons'] }),
			BrandType: t.String({ examples: ['unifi'] }),
			CustType: t.String({ examples: ['home'] }),
			WhatsIncluded: t.String({
				examples: [
					'Unifi Home,Wi-Fi 6 Combo Box,24 hrs Service Guarantee,Voice,T&C apply'
				]
			}),
			WhatsIncludedPostpaid: t.Optional(t.String({ examples: [''] })),
			MarketType: t.String({ examples: ['Consumer'] }),
			Service: t.Optional(
				t.String({
					examples: [
						'Get RM50 bill rebate if your service is not restored within 24 hours'
					]
				})
			),
			Home: t.Optional(
				t.String({
					examples: [
						'Unlimited Home Internet;Download speed up to 500Mbps;Upload speed up to 100mbps;24 months contract;Sign up now and enjoy free 3 months subscription'
					]
				})
			),
			IsFMC: t.Boolean()
		}),
		SmartMapInstallationAddress: t.Object({
			FormattedAddress: t.String({
				examples: ['51,JALAN ASAM KUMBANG,TAMAN KEPONG BARU,52100 KUALA LUMPUR']
			}),
			Apt: t.Optional(t.String({ examples: [''] })),
			Building: t.Optional(t.String({ examples: [''] })),
			StreetType: t.Optional(t.String({ examples: [''] })),
			Street: t.String({ examples: ['JALAN ASAM KUMBANG'] }),
			Section: t.String({ examples: ['TAMAN KEPONG BARU'] }),
			PostCode: t.String({ examples: ['52100'] }),
			City: t.String({ examples: ['KUALA LUMPUR'] }),
			State: t.String({ examples: ['WILAYAH PERSEKUTUAN'] }),
			Location: t.Object({
				Lat: t.Number({ examples: [3.2097339] }),
				Lon: t.Number({ examples: [101.6432997] })
			})
		}),
		InstallationAddress: ijoinInstallationAddressSchema,
		PersonalDetails: t.Object({
			FullName: t.String({ examples: ['John Snow'] }),
			IdType: t.String({ examples: ['New NRIC'] }),
			IdNumber: t.String({ examples: ['901010-10-1010'] }),
			Email: t.String({ examples: ['<EMAIL>'] }),
			CountryOfIssue: t.Optional(t.String({ examples: [''] })),
			DateOfBirth: t.Optional(t.String({ examples: [''] }))
		}),
		Addons: t.Object({
			VoicePlan: t.Object({
				NationalPlan: t.Object({}),
				InternationalPlan: t.Object({})
			}),
			EntertainmentPlan: t.Object({
				ProductID: t.Optional(t.String()),
				ProductName: t.Optional(t.String()),
				Price: t.Optional(t.String())
			}),
			Gaming: t.Object({
				BlacknutName: t.Optional(t.String()),
				BlacknutPartNo: t.Optional(t.String()),
				ControllerName: t.Optional(t.String()),
				ControllerPartNo: t.Optional(t.String()),
				ControllerCount: t.Optional(t.Number()),
				Price: t.Optional(t.Number()),
				DeliveryAddress: ijoinInstallationAddressSchema
			}),
			Devices: t.Object({
				SmartDevices: t.Array(t.Any()),
				DeliveryDetails: t.Object({
					IsSameAsInstallationAddress: t.Boolean(),
					DeliveryAddress: ijoinInstallationAddressSchema
				})
			})
		}),
		MobileFmc: t.Object({
			PostpaidPlan: t.Optional(t.String()),
			PurchaseType: t.Optional(t.String()),
			Operator: t.Optional(t.String()),
			PhoneNumber: t.Optional(t.String()),
			LastFourDigit: t.Optional(t.String()),
			LastFourDigitSupplementaryOne: t.Optional(t.String()),
			LastFourDigitSupplementaryTwo: t.Optional(t.String()),
			LastFourDigitSupplementaryThree: t.Optional(t.String()),
			LastDigitsAddon: t.Optional(t.Array(t.String())),
			WithDevice: t.Boolean(),
			SelectedDevice: t.Object({
				DeviceName: t.Optional(t.String()),
				DeviceFee: t.Optional(t.String()),
				UpfrontPayment: t.Optional(t.String()),
				Contract: t.Optional(t.String())
			}),
			DeliveryAddress: ijoinInstallationAddressSchema,
			FirstMonthTotal: t.Optional(t.String())
		}),
		LifestyleFlc: t.Object({
			SmartHomeDevices: t.Optional(t.String()),
			DeliveryAddress: ijoinInstallationAddressSchema
		}),
		BillingAddress: t.Object({}),
		PromoCode: t.Object({
			Name: t.Optional(t.String()),
			Applied: t.Boolean()
		}),
		MonthlyTotal: t.String(),
		PreferredInstallationTime: t.String({ examples: ['26/12/2024 09:30:00'] }),
		ApplicationSubmittedTime: t.Nullable(t.String()),
		FeedbackSubmission: t.Boolean()
	}),
	CreatedDate: t.Optional(t.String()),
	UpdatedDate: t.Optional(t.String())
});

export type CreateIjoinOrderReq = Static<typeof createIjoinOrderReqSchema>;

export const createIjoinOrderResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			MobileNumber: t.String({ examples: ['+60123456789'] }),
			OrderId: t.Optional(t.String({ examples: ['UNFH-ABC123DEF'] })),
			SourceChannel: t.String({ examples: ['App'] }),
			ProductCategory: t.String({ examples: ['Unifi'] }),
			OrderProgress: orderableProgressSchema,
			OrderStatus: t.String({ examples: ['SUBMITTED'] }),
			Data: orderableJSONDataSchema,
			CreatedDate: t.Date(),
			UpdatedDate: t.Date()
		})
	},
	{
		description: 'Ijoin order successfully submitted'
	}
);

export type CreateIjoinOrderRes = Static<typeof createIjoinOrderResSchema>;
