import { type Static, t } from 'elysia';

// Change the schema as per the requirement from OMG
export const ottSubscribedSchema = t.Object({
	ottMerchantId: t.Number({ example: 38 }),
	ottName: t.String({ example: 'Naked BB Netflix 300Mbps' }),
	ottOmgId: t.Number({ example: 141 }),
	ottTxnId: t.Number({ example: 1001 }),
	ottStartDate: t.String({ example: '18-06-2025' }),
	ottExpiryDate: t.String({ example: '18-06-2025' }),
	ottStatus: t.String({ example: 'Activated' }),
	ottLoginType: t.String({ example: 'mobile' }),
	ottUserId: t.String({ example: 'johndoe@ott' }),
	failureReason: t.Optional(t.String())
});

export type OttSubscribed = Static<typeof ottSubscribedSchema>;

// Change the schema as per the requirement from OMG
export const updateOrderReqSchema = t.Object({
	orderRefNo: t.String({ example: 'ADDONS-698964' }),
	ottSubscribed: t.Array(ottSubscribedSchema)
});

export type UpdateOrderReq = Static<typeof updateOrderReqSchema>;

export const updateOrderResSchema = t.Object({
	responseCode: t.String({ examples: ['200'] }), // 200, 500
	responseMsg: t.String({ examples: ['Success'] }), // Success, Failure
	orderRefNo: t.String({ examples: ['ADDONS-698964'] })
});

export type UpdateOrderRes = Static<typeof updateOrderResSchema>;
