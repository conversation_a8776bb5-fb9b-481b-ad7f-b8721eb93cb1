import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const netflixCurrentPlanReqSchema = t.Object({
	AccountType: t.Enum(
		{
			Broadband: 'Broadband',
			Mobile: 'Mobile'
		},
		{
			description: 'Account type (Broadband or Mobile)',
			example: 'Broadband'
		}
	),
	AccountId: t.String({ example: 'johndoe@unifi' })
});

export type NetflixCurrentPlanReq = Static<typeof netflixCurrentPlanReqSchema>;

export const netflixCurrentPlanResSchema = t.Object({
	...baseResponseSchema.properties,
	Response: t.Array(
		t.Object({
			NetflixPai: t.String({ example: '' }),
			NetflixBundleId: t.String({
				example: 'd4ead331-32e3-4450-972f-af0e9b6d5bd3'
			}),
			NetflixOfferId: t.String({
				example: '26c07ec4-5887-45e6-a7b9-189c23aef781'
			}),
			TMbundleId: t.String({ example: 'UM1S' })
		})
	)
});

export type NetflixCurrentPlanRes = Static<typeof netflixCurrentPlanResSchema>;
