import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const disneyChangeMobileRequestSchema = t.Object({
	AccountType: t.String({ example: 'Broadband' }),
	AccountId: t.String({ example: 'johndoe@unifi' }),
	OttTxnId: t.Integer({ example: 1001 }),
	BillingEmail: t.String({ example: '<EMAIL>' }),
	OldMobileNo: t.String({ example: '**********' }),
	NewMobileNo: t.String({ example: '**********' })
});

export type DisneyChangeMobileReq = Static<
	typeof disneyChangeMobileRequestSchema
>;

export const disneyChangeMobileReschema = t.Object({
	...baseResponseSchema.properties,
	Response: t.Object({
		NextAllowDate: t.Nullable(t.String({ example: '' }))
	})
});

export type DisneyChangeMobileRes = Static<typeof disneyChangeMobileReschema>;
