import { type Static, t } from 'elysia';
import {
	AccountTypeEnum,
	ActivationCategoryEnum,
	OMGTokenTypeEnum,
	OmgTokenChannelEnum
} from '../../../../../enum/addOns.enum';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const netflixReqSchema = t.Object({
	TokenType: t.Enum(OMGTokenTypeEnum),
	TokenChannel: t.Enum(OmgTokenChannelEnum),
	TokenErrorUrl: t.String({ example: 'https://example.com/error' }),
	PromoCode: t.Optional(
		t.String({
			examples: [''],
			description: ''
		})
	)
});

export const othersReqObjSchema = t.Object({
	OttPlanType: t.String({ example: 'Basic' }),
	OttPlanId: t.String({ example: 'P39' }),
	OttProductId: t.String({ example: '42' }),
	OttMerchantId: t.Number({ example: 38 })
});

export const ottActivationUrlReqSchema = t.Object({
	Category: t.Enum(ActivationCategoryEnum),
	AccountType: t.Enum(AccountTypeEnum),
	AccountId: t.String({ example: 'Broadband' }),
	Netflix: t.Optional(netflixReqSchema),
	Others: t.Optional(othersReqObjSchema)
});

export type OttActivationUrlReq = Static<typeof ottActivationUrlReqSchema>;

export const ottActivationUrlResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			Url: t.String()
		})
	},
	{
		description: 'OTT Activation URL.'
	}
);

export type OttActivationUrlRes = Static<typeof ottActivationUrlResSchema>;
