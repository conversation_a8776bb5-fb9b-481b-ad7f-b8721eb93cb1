import { type Static, t } from 'elysia';

export const netflixCancelPlanReqSchema = t.Object({
	AccountType: t.Enum(
		{
			Broadband: 'Broadband',
			Mobile: 'Mobile'
		},
		{
			description: 'Account type (Broadband or Mobile)',
			example: 'Broadband'
		}
	),
	AccountId: t.String({
		description: 'User account ID',
		example: 'johndoe@unifi'
	}),
	OttTxnId: t.String({
		description: 'OTT transaction ID',
		example: '1'
	}),
	NetflixTxnId: t.String({
		description: 'Netflix transaction ID',
		example: 'KCU8WQI+akOOL97B8h8o+BvtJ3A='
	})
});

export type NetflixCancelPlanReq = Static<typeof netflixCancelPlanReqSchema>;
