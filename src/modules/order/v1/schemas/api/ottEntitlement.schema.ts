import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const ottEntitlementReqSchema = t.Object({
	AccountType: t.String({ example: 'Broadband' }),
	AccountId: t.String({ example: 'johndoe@unifi' }),
	OttMerchantId: t.Number({ example: 38 })
});

export type OttEntitlementReq = Static<typeof ottEntitlementReqSchema>;

export const ottEntitlementResSchema = t.Object({
	...baseResponseSchema.properties,
	Response: t.Object({
		ResponseCode: t.String({ example: '200' }),
		ResponseMsg: t.String({ example: 'Success' })
	})
});

export type OttEntitlementRes = Static<typeof ottEntitlementResSchema>;
