import { sql } from 'drizzle-orm';
import { integer, pgTable, text, timestamp } from 'drizzle-orm/pg-core';

export const siebelProductMapTableSchema = pgTable('siebel_product_map', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	ProductName: text('product_name').notNull(),
	PartNumber: text('part_number').notNull(),
	ProductId: text('product_id').notNull(),
	CreatedAt: timestamp('created_at', { mode: 'date' })
		.default(sql`now()`)
		.notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' })
		.default(sql`now()`)
		.notNull()
});

export type SelectSiebelProductMap =
	typeof siebelProductMapTableSchema.$inferSelect;
