import { type Static, t } from 'elysia';
import {
	SRContactMethodEnum,
	SRTypeEnum
} from '../../../../../enum/serviceRequest.enum';
import { IdTypeEnum } from '../../../../../enum/user.enum';
import { SystemNameEnum } from '../../../../../enum/wso2.enum';
import { wso2CustomerAccountResSchema } from '../../../../../integration/wso2/user/schemas/api/wso2CustomerAccount.schema';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

const serviceDetailsSchema = t.Object({
	decryptedBillAccNo: t.String(),
	accountNo: t.String(),
	contactId: t.String(),
	billingAccountRowId: t.String(),
	serviceRowId: t.String(),
	serviceId: t.String(),
	wso2CARes: wso2CustomerAccountResSchema
});

export type ServiceDetails = Static<typeof serviceDetailsSchema>;

export const createSRReqSchema = t.Object({
	SystemName: t.Enum(SystemNameEnum),
	CustomerIdType: t.Enum(IdTypeEnum),
	CustomerIdValue: t.String({ examples: ['999999-99-9999'] }),
	EncryptedBillAccNo: t.String({
		examples: ['dfxgno38w450hfadpozg9a4w6q45q25gsnbwsrt3-=']
	}),
	ProductName: t.String({ examples: ['Unifi Business 800M - Pro Upgrade'] }),
	Type: t.Enum(SRTypeEnum),
	CustomerComments: t.String({ examples: ['Cost saving'] }),
	Termination: t.Optional(
		t.Object({
			ContactNo: t.String({ examples: ['**********'] }),
			EmailAddress: t.String({ examples: ['<EMAIL>'] }),
			TMTAndCFlag: t.String({
				examples: ['Y']
			}),
			BankAcctNum: t.String({
				examples: ['************']
			}),
			BankName: t.String({
				examples: ['Bigpay']
			})
		})
	),
	Complaint: t.Optional(
		t.Object({
			Case: t.String({
				examples: ['Penalty Charge'],
				description: 'The value can be retrieved from LOV'
			}),
			Category: t.String({
				examples: ['Billing'],
				description: 'The value can be retrieved from LOV'
			}),
			SubCategory: t.String({
				examples: ['Dispute-Invalid Charges'],
				description: 'The value can be retrieved from LOV'
			}),
			OwnerGroup: t.String({
				examples: ['WIDER_TMUC_TECH_ACG'],
				description: 'The value can be retrieved from LOV'
			}),
			PreferredAcknowledgement: t.Enum(SRContactMethodEnum),
			PreferredContactValue: t.String({
				examples: ['<EMAIL>'],
				description: 'The value is depending on the PreferredAcknowledgement'
			}),
			PreferredContactName: t.String({
				examples: ['John Doe'],
				description: 'The value should be from UDID'
			})
		})
	)
});

export type CreateSRReq = Static<typeof createSRReqSchema>;

export const createSRResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			SRNumber: t.String({ examples: ['1-1234567890'] }),
			IsEmailSent: t.Boolean({
				examples: [true],
				description:
					'This boolean is returned to allow the frontend handling the response to user if the email is not sent'
			}),
			CustomerIdType: t.Enum(IdTypeEnum),
			CustomerIdValue: t.String({ examples: ['99**********99'] })
		})
	},
	{
		description: 'SR ticket successfully created.'
	}
);

export type CreateSRRes = Static<typeof createSRResSchema>;

const srEmailSchema = t.Object({
	SRNumber: t.Optional(t.String()),
	currentTime: t.Optional(t.String()),
	username: t.Optional(t.String())
});

export type SREmail = Static<typeof srEmailSchema>;
