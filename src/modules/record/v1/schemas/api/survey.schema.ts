import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const submitNesSurveyReqSchema = t.Object({
	Context: t.String({
		examples: ['POST'],
		description: 'POST is after a journey, PRE is before a journey'
	}),
	Journey: t.String({
		examples: ['I Pay'],
		description: 'The journey the customer is currently in'
	}),
	Activity: t.String({
		examples: ['Home Payment'],
		description: 'The activity the customer is currently doing'
	}),
	Rating: t.String({ examples: ['5'] }),
	Question: t.String({
		examples: ['How easy was your experience in using our Selfcare portal?']
	}),
	Comments: t.String({ examples: ['The service is good'] }),
	Reason: t.String({ examples: ['Quick response time'] }),
	AccountNo: t.String({ examples: ['********'] }),
	Name: t.String({ examples: ['JOHN DOE'] }),
	Mobile: t.String({ examples: ['+***********'] }),
	Email: t.String({ examples: ['<EMAIL>'] }),
	ServiceID: t.Optional(t.String({ examples: ['<EMAIL>'] })),
	ReferenceID: t.Number({ examples: [123456] }),
	DisableNES: t.Boolean({ examples: [false] }),
	IsVoc: t.Boolean({ examples: [false] })
});

export type SubmitNesSurveyReq = Static<typeof submitNesSurveyReqSchema>;

export const vocUserDetailsReqschema = t.Object({
	request: t.Object({
		requestId: t.String({
			examples: ['urn:uuid:123e4567-e89b-12d3-a456-************']
		})
	}),
	responseId: t.String({ examples: ['R_S124dcs4213WS'] }),
	encryptedCustomerId: t.String({
		examples: ['sfavhjnolkdhnv394pq8to4kwretfghproaub349pq28g'],
		description:
			"Combine customer's both id type, id value and billing account number <br>example: New NRIC;999999-99-9999;******** <br>ID type need to follow the enum"
	})
});

export type VocUserDetailsReq = Static<typeof vocUserDetailsReqschema>;

export const vocUserDetailsResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			Response: t.Object({
				RequestId: t.String({
					examples: ['urn:uuid:123e4567-e89b-12d3-a456-************']
				}),
				Status: t.String({ examples: ['Success'] }),
				StatusCode: t.String({ examples: ['0'] })
			}),
			ResponseId: t.String({ examples: ['R_S124dcs4213WS'] }),
			CustomerUniqueId: t.String({ examples: ['999999-99-9999'] }),
			Journey: t.String({ examples: ['Myunifi'] }),
			Activity: t.String({ examples: ['General'] }),
			CustomerName: t.String({ examples: ['JOHN DOE'] }),
			ServiceNumber: t.String({ examples: ['Test@unifi'] }),
			CustomerEmail: t.String({ examples: ['<EMAIL>'] }),
			Package: t.String({ examples: ['Unifi 100Mbps'] }),
			CustomerSourceSystem: t.String({ examples: ['NOVA'] })
		})
	},
	{
		description: 'VOC user details successfully retrieved.'
	}
);

export type VocUserDetailsRes = Static<typeof vocUserDetailsResSchema>;
