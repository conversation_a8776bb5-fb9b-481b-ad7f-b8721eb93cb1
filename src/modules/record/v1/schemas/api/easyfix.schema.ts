import { type Static, t } from 'elysia';
import { IdTypeEnum } from '../../../../../enum/user.enum';
import { wso2CreateSRICPSchema } from '../../../../../integration/wso2/record/schemas/api/wso2CreateSRIcp.schema';
import { wso2EasyfixTnpsSurveyResSchema } from '../../../../../integration/wso2/record/schemas/api/wso2EasyfixTnpsSurvey.schema';
import { wso2NovaCTTSchema } from '../../../../../integration/wso2/record/schemas/api/wso2NovaCTT.schema';
import { wso2RetrieveNTTResSchema } from '../../../../../integration/wso2/record/schemas/api/wso2RetrieveNTT.schema';
import {
	wso2SRDetailsReturnObjSchema,
	wso2TTDetailsReturnObjSchema
} from '../../../../../integration/wso2/record/schemas/api/wso2SRDetail.schema';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const easyFixActivityListReqSchema = t.Object({
	IdType: t.Enum(IdTypeEnum),
	IdValue: t.String({ example: '999999-99-9999' }),
	SearchStartDate: t.String({ example: '2024-01-01' }),
	SearchEndDate: t.String({ example: '2024-01-01' })
});

export type EasyFixActivityListReq = Static<
	typeof easyFixActivityListReqSchema
>;

export const easyFixActivityListResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			SRActivityList: t.Array(wso2SRDetailsReturnObjSchema),
			TTActivityList: t.Array(wso2TTDetailsReturnObjSchema)
		})
	},
	{
		description: 'Easyfix activity list successfully retrieved.'
	}
);

export type EasyFixActivityListRes = Static<
	typeof easyFixActivityListResSchema
>;

export const easyfixCreateNovaTTResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: wso2NovaCTTSchema
	},
	{
		description: 'Trouble ticket created successfully'
	}
);

export type EasyFixCreateNovaTTRes = Static<
	typeof easyfixCreateNovaTTResSchema
>;

export const easyfixGetNetworkTTReqSchema = t.Object({
	RequestID: t.String({
		example: 'EASYFIX-20240726160101-000917396',
		minLength: 1
	}),
	ServiceNo: t.String({
		example: '000917396',
		minLength: 1
	}),
	ServiceType: t.Optional(
		t.String({
			example: 'ServiceNo',
			description: '',
			minLength: 1
		})
	)
});

export type EasyFixGetNetworkTTReq = Static<
	typeof easyfixGetNetworkTTReqSchema
>;

export const easyfixGetNetworkTTResSchema = t.Object({
	...baseResponseSchema.properties,
	Response: wso2RetrieveNTTResSchema
});

export type EasyFixGetNetworkTTRes = Static<
	typeof easyfixGetNetworkTTResSchema
>;

export const createSRNovaResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			Id: t.Nullable(t.String({ examples: ['1'] })),
			SRNumber: t.String({ examples: ['1-1234567890'] }),
			Status: t.String({ examples: ['Open'] })
		})
	},
	{
		description: 'SR ticket successfully created.'
	}
);

export type CreateSRNovaRes = Static<typeof createSRNovaResSchema>;

export const createSRICPResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: wso2CreateSRICPSchema
	},
	{
		description: 'SR ticket successfully created.'
	}
);

export type CreateSRICPRes = Static<typeof createSRICPResSchema>;

export const submitTnpsSurveyResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: wso2EasyfixTnpsSurveyResSchema
	},
	{
		description: 'SR ticket successfully created.'
	}
);

export type SubmitTnpsSurveyRes = Static<typeof submitTnpsSurveyResSchema>;
