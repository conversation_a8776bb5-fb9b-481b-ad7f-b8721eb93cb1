import { boolean, pgTable, serial, text, timestamp } from 'drizzle-orm/pg-core';

export const siebelOrderTypeTableSchema = pgTable('siebel_order_type', {
	Id: serial('id').primaryKey(),
	SystemName: text('system_name').notNull(),
	OrderType: text('order_type').notNull(),
	SubOrderType: text('sub_order_type').notNull(),
	Title: text('title').notNull(),
	TruckRoll: boolean('truck_roll').notNull(),
	Category: text('category').notNull(),
	SubCategory: text('sub_category').notNull(),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectSiebelOrderType =
	typeof siebelOrderTypeTableSchema.$inferSelect;
