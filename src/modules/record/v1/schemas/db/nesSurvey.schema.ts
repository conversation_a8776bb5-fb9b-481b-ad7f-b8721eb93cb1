import {
	boolean,
	integer,
	pgTable,
	serial,
	text,
	timestamp
} from 'drizzle-orm/pg-core';

export const nesSurveyTableSchema = pgTable('nes_survey', {
	Id: serial('id').primaryKey(),
	Source: text('source').notNull(),
	Segment: text('segment').notNull(),
	Activity: text('activity').notNull(),
	Journey: text('journey').notNull(),
	Context: text('context').notNull(),
	Question: text('question').notNull(),
	Rating: text('rating').notNull(),
	Comments: text('comments'),
	Reason: text('reason'),
	AccountNo: text('account_no'),
	Name: text('name').notNull(),
	Mobile: text('mobile').notNull(),
	Email: text('email').notNull(),
	ServiceID: text('service_id'),
	ReferenceID: integer('reference_id').notNull(),
	DisableNES: boolean('disable_nes').notNull(),
	BackendStatus: text('backend_status').notNull(),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectNesSurvey = typeof nesSurveyTableSchema.$inferSelect;

export type InsertNesSurvey = typeof nesSurveyTableSchema.$inferInsert;
