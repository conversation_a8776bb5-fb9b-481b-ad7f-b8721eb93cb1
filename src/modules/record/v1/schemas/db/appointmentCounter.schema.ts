import { integer, pgTable, text, timestamp } from 'drizzle-orm/pg-core';

export const appointmentCounterTableSchema = pgTable('appointment_counter', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	OrderId: text('order_id').unique(),
	ChangeCounter: integer('change_counter').notNull(),
	CancelCounter: integer('cancel_counter').notNull(),
	ErrorMessage: text('error_message'),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectAppointmentCounter =
	typeof appointmentCounterTableSchema.$inferSelect;
