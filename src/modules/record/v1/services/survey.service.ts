import { format } from 'date-fns';
import { eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { SourceEnum } from '../../../../enum/header.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { LightweightFlagEnum } from '../../../../enum/wso2.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	Wso2SubmitNesSurveyReq,
	Wso2SubmitNesSurveyRes
} from '../../../../integration/wso2/record/schemas/api/wso2SubmitNesSurvey.schema';
import type {
	Wso2CustomerAccountReq,
	Wso2CustomerAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2CustomerAccount.schema';
import type {
	Wso2ServiceAccountReq,
	Wso2ServiceAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2ServiceAccount.schema';
import { UE_ERROR } from '../../../../middleware/error';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import { getMyTimeZoneDate } from '../../../../shared/common';
import type { BaseResponse } from '../../../../shared/schemas/api/responses.schema';
import type { SubmitNesSurveyReq } from '../schemas/api/survey.schema';
import {
	type InsertNesSurvey,
	nesSurveyTableSchema
} from '../schemas/db/nesSurvey.schema';

class SurveyRecord {
	private db: NodePgDatabase;
	private idTokenInfo: IdTokenInfo | null;
	private integrationId: string;
	private mwIntegration: MwIntegration;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo | null) {
		this.db = getDbInstance();
		this.idTokenInfo = idTokenInfo;
		this.integrationId = integrationId;
		this.mwIntegration = new MwIntegration(this.integrationId);
	}

	async submitNesSurvey(
		req: SubmitNesSurveyReq,
		source: SourceEnum,
		segment: string
	): Promise<BaseResponse> {
		let serviceId = '';
		if (this.idTokenInfo) {
			const getWso2UserIntegration = this.mwIntegration.Wso2UserIntegration;
			const wso2CustomerAccReq: Wso2CustomerAccountReq = {
				idType: this.idTokenInfo.IdType,
				idValue: this.idTokenInfo.IdValue
			};
			const wso2CustomerAccRes: Wso2CustomerAccountRes =
				await getWso2UserIntegration.getWso2CustomerAccount(
					wso2CustomerAccReq,
					LightweightFlagEnum.YES
				);

			//Set service ID
			if (wso2CustomerAccRes?.Response?.CustomerAccounts) {
				for (const ca of wso2CustomerAccRes.Response.CustomerAccounts) {
					if (ca?.BillingAccounts && ca?.BillingAccounts.length > 0) {
						for (const ba of ca.BillingAccounts) {
							if (
								ba?.AccountStatus !== 'Terminated' &&
								ba?.AccountStatus !== 'Suspended'
							) {
								const wso2ServiceAccountReq: Wso2ServiceAccountReq = {
									idType: this.idTokenInfo.IdType,
									idValue: this.idTokenInfo.IdValue,
									SystemName: ca?.SystemName ?? '',
									BillingAccountNo: ba?.AccountNumber ?? ''
								};
								const wso2ServiceAccountRes: Wso2ServiceAccountRes =
									(await getWso2UserIntegration.getWso2ServiceAccount(
										wso2ServiceAccountReq,
										LightweightFlagEnum.YES
									)) as Wso2ServiceAccountRes;
								if (wso2ServiceAccountRes?.Response?.ServiceAccount) {
									for (const sa of wso2ServiceAccountRes.Response
										.ServiceAccount) {
										if (sa?.Products && sa.Products.length > 0) {
											for (const product of sa.Products) {
												if (product && product.ProductName === 'Internet') {
													serviceId = product.SerialNumber;
													break;
												}
											}
										}
									}
									break;
								}
							}
						}
					}
				}
			}
		}

		//Set calling application based on source
		let callingApplication = '';
		switch (source) {
			case SourceEnum.UNIFI_APP:
			case SourceEnum.TEST_AUTOMATION:
				callingApplication = 'MyUnifiApp';
				break;
			default:
				callingApplication = 'MyUnifiPortal';
		}

		//Submit survey to WSO2
		const wso2SubmitTnpsSurveyReq: Wso2SubmitNesSurveyReq = {
			SurveyDetailsInsertRequest: {
				Context: req.Context,
				Journey: req.Journey,
				Activity: req.Activity,
				Rating: req.Rating,
				Question: req.Question,
				Reason: req.Reason,
				Comments: req.Comments,
				AccountNo: req.AccountNo,
				Name: req.Name,
				Mobile: req.Mobile,
				Email: req.Email,
				DatetimeCreated: format(
					getMyTimeZoneDate(),
					'"dd-MMM-yy hh:mm:ss.SSS aaa'
				),
				ServiceID: serviceId,
				ReferenceID: req.ReferenceID.toString()
			}
		};

		const wso2SubmitNesSurveyRes: Wso2SubmitNesSurveyRes =
			await this.mwIntegration.Wso2RecordIntegration.wso2SubmitNesSurvey(
				wso2SubmitTnpsSurveyReq,
				callingApplication
			);

		/*
		 * Update DB based on the calling application
		 * If the user is from UNIFI-APP, update the UserDetails table for VOC team. NOTE: This part has been removed upon the confirmation of VOC journey
		 * If the user is from UNIFI-PORTAL, insert the survey details into the NesSurvey table
		 * Set status to 'SUCCESS' if the operation is successful
		 */
		let status = 'FAILED';
		if (
			wso2SubmitNesSurveyRes.SurveyDetailsInsertResponse.Status.Message ===
			'SUCCESS'
		) {
			//Check if the data is duplicate, throw error if it is
			const duplicate = await this.db
				.select()
				.from(nesSurveyTableSchema)
				.where(eq(nesSurveyTableSchema.ReferenceID, req.ReferenceID));
			if (duplicate.length > 0) {
				throw new UE_ERROR(
					'Duplicate data is not allowed',
					StatusCodeEnum.FORBIDDEN_ERROR
				);
			}

			//Insert the data
			const insertReq: InsertNesSurvey = {
				Source: source,
				Segment: segment,
				Activity: req.Activity,
				Journey: req.Journey,
				Context: req.Context,
				Question: req.Question,
				Rating: req.Rating,
				Comments: req.Comments,
				Reason: req.Reason,
				AccountNo: req.AccountNo,
				Name: req.Name,
				Mobile: req.Mobile,
				Email: req.Email,
				ServiceID: serviceId,
				ReferenceID: req.ReferenceID,
				DisableNES: req.DisableNES,
				BackendStatus:
					wso2SubmitNesSurveyRes.SurveyDetailsInsertResponse.Status.Code
			};
			const insertRes: InsertNesSurvey[] = await this.db
				.insert(nesSurveyTableSchema)
				.values(insertReq)
				.returning();

			if (insertRes) {
				status = 'SUCCESS';
			}
		}

		//Check if the operation failed
		if (status === 'FAILED') {
			throw new UE_ERROR(
				'Failed to record TNPS survey',
				StatusCodeEnum.UE_INTERNAL_SERVER
			);
		}

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Message: 'Successfully submitted TNPS survey'
		};
	}
}

export default SurveyRecord;
