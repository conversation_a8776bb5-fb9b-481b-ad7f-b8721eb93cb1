import { format, parse, parseISO } from 'date-fns';
import { eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getCache, setCache } from '../../../../config/cache.config';
import { getDbInstance } from '../../../../config/db.config';
import { CacheKeyEnum } from '../../../../enum/cacheKey.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { TmForceLovEnum } from '../../../../enum/tracker.enum';
import { SystemNameEnum } from '../../../../enum/wso2.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	Wso2SRDetailReq,
	Wso2SRDetailRes
} from '../../../../integration/wso2/record/schemas/api/wso2SRDetail.schema';
import type {
	Wso2TmForceTTProgressUpdateReq,
	Wso2TmForceTTProgressUpdateRes
} from '../../../../integration/wso2/record/schemas/api/wso2TmForceTTProgressUpdate.schema';
import type {
	Wso2TmForceTechinicianDetailsReq,
	Wso2TmForceTechinicianDetailsRes
} from '../../../../integration/wso2/record/schemas/api/wso2TmForceTechinicianDetails.schema';
import { formatDate, getMyTimeZoneDate } from '../../../../shared/common';
import type {
	ActivityDetailsReq,
	ActivityDetailsRes,
	ActivityListObj,
	ActivityListRes,
	CaseDetails,
	//type OrderInformation,
	ReportItem,
	ReportItemList,
	TechnicianDetails
} from '../schemas/api/activityTracker.schema';
import {
	type SelectTmForceStatusLov,
	tmForceStatusLovTableSchema
} from '../schemas/db/tmForceLov.schema';

class ActivityTracker {
	private db: NodePgDatabase;
	private integrationId: string;
	private mwIntegration: MwIntegration;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
		this.mwIntegration = new MwIntegration(integrationId);
	}

	async getActivityList(
		accountNo: string,
		systemName: string
	): Promise<ActivityListRes> {
		const srActivityTrackerObj: ActivityListObj = [];
		const ttActivityTrackerObj: ActivityListObj = [];
		const today: Date = getMyTimeZoneDate();
		const startDate: number = new Date(today).setDate(today.getDate() - 90);
		const endDate: number = new Date(today).setDate(today.getDate() + 1);
		const novaFormattedStartDate: string = format(startDate, 'dd-MMM-yyyy');
		const novaFormattedEndDate: string = format(endDate, 'dd-MMM-yyyy');
		const icpFormattedStartDate: string = format(startDate, 'MM/dd/yyyy');
		const icpFormattedEndDate: string = format(endDate, 'MM/dd/yyyy');
		const searchPeriod: string = `[Created]>='${icpFormattedStartDate}' AND [Created]<='${icpFormattedEndDate}'`;
		const wso2Req: Wso2SRDetailReq =
			systemName === SystemNameEnum.NOVA
				? {
						novaAccountNo: accountNo,
						SearchSpecByStartDate: novaFormattedStartDate,
						SearchSpecByEndDate: novaFormattedEndDate
					}
				: {
						icpAccountNo: accountNo,
						SearchSpecByPeriod: searchPeriod
					};

		const wso2Res: Wso2SRDetailRes =
			await this.mwIntegration.Wso2RecordIntegration.getWso2SRDetails(
				wso2Req,
				'V2'
			);

		if (wso2Res.Response?.SRDetailsReturn) {
			for (const sr of wso2Res.Response.SRDetailsReturn) {
				const createdDate: string = sr.CreatedDate
					? systemName === SystemNameEnum.NOVA
						? format(
								parse(sr.CreatedDate, 'dd/MM/yyyy hh:mm:ss aaa', new Date()),
								'dd MMMM yyyy hh:mm aaa'
							)
						: format(
								parse(sr.CreatedDate, 'MM/dd/yyyy HH:mm:ss', new Date()),
								'dd MMMM yyyy hh:mm aaa'
							)
					: '';

				const closedDate: string = sr.ClosedDate
					? systemName === SystemNameEnum.NOVA
						? format(parseISO(sr.ClosedDate), 'dd MMMM yyyy hh:mm aaa')
						: format(
								parse(sr.ClosedDate, 'MM/dd/yyyy HH:mm:ss', new Date()),
								'dd MMMM yyyy hh:mm aaa'
							)
					: '';
				srActivityTrackerObj.push({
					Title: sr.Type ?? '',
					CaseTitle: '',
					Category: sr.Category ?? '',
					SubCategory: sr.SubCategory ?? sr.Source ?? '',
					Status: sr.Status,
					CreatedDate: createdDate,
					ClosedDate: closedDate,
					BillingAccountNo: sr.BillingAccount,
					ReferenceNo: sr.SRNumber,
					AccountNo: sr.AccountNo,
					ServiceId: ''
				});
			}
		}

		if (wso2Res.Response?.TTDetailsReturn) {
			for (const tt of wso2Res.Response.TTDetailsReturn) {
				const createdDate: string = tt.CreatedDate
					? systemName === SystemNameEnum.NOVA
						? format(
								parse(tt.CreatedDate, 'dd/MM/yyyy hh:mm:ss aaa', new Date()),
								'dd MMMM yyyy hh:mm aaa'
							)
						: format(
								parse(tt.CreatedDate, 'MM/dd/yyyy HH:mm:ss', new Date()),
								'dd MMMM yyyy hh:mm aaa'
							)
					: '';

				const closedDate: string = tt.ClosedDate
					? systemName === SystemNameEnum.NOVA
						? format(parseISO(tt.ClosedDate), 'dd MMMM yyyy hh:mm aaa')
						: format(
								parse(tt.ClosedDate, 'MM/dd/yyyy HH:mm:ss', new Date()),
								'dd MMMM yyyy hh:mm aaa'
							)
					: '';

				ttActivityTrackerObj.push({
					Title: 'Unifi Home Technical Report',
					CaseTitle:
						systemName === SystemNameEnum.NOVA
							? (tt.NovaCategory ?? '')
							: (tt.ICPProductCategory ?? ''),
					Category: 'TMFORCE',
					SubCategory: 'Unifi Home Technical Report',
					Status: tt.Status,
					CreatedDate: createdDate,
					ClosedDate: closedDate,
					BillingAccountNo: tt.BillingAccount,
					ReferenceNo: tt.TTNumber,
					AccountNo: tt.AccountNo,
					ServiceId: tt.ServiceID
				});
			}
		}

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				SRActivityList: srActivityTrackerObj,
				TTActivityList: ttActivityTrackerObj
			}
		};
	}

	async getActivityDetails(
		req: ActivityDetailsReq
	): Promise<ActivityDetailsRes> {
		const {
			horizontalDescription,
			caseDetails,
			reportItemList,
			technicianDetails
		} = await this.getTicketActivityDetails(req);

		return {
			IntegrationId: this.integrationId,
			Success: true,
			Code: StatusCodeEnum.OK,
			Response: {
				ReferenceNumber: req.ReferenceNo,
				TicketStatusDescription: horizontalDescription,
				Status:
					reportItemList.length === 0
						? 'Not Available'
						: reportItemList[0].Title,
				CaseDetails: caseDetails,
				ReportStatus: reportItemList,
				TechnicianDetails: technicianDetails
			}
		};
	}

	// Trouble Ticket
	private async getTicketActivityDetails(req: ActivityDetailsReq): Promise<{
		horizontalDescription: string;
		caseDetails: CaseDetails;
		reportItemList: ReportItemList;
		technicianDetails: TechnicianDetails;
	}> {
		const logDateTimePattern: string = 'dd/MM/yyyy HH:mm:ss';
		const logDateTimeFormat: string = 'dd MMMM yyyy, hh:mm a';
		let reportItem = {} as ReportItem;
		let technicianDetails: TechnicianDetails = null;
		let horizontalDescription =
			'Oh uh, we could not retrieve your information.';
		const caseDetails: CaseDetails = {} as CaseDetails;
		const reportItemList: ReportItemList = [];
		const lovList: SelectTmForceStatusLov[] = await this.getAssuranceLovList();

		const wso2Req: Wso2TmForceTTProgressUpdateReq = {
			ProgressUpdateRequest: { TTNumber: req.ReferenceNo }
		};
		const wso2Res: Wso2TmForceTTProgressUpdateRes =
			await this.mwIntegration.Wso2RecordIntegration.getWso2TmForceTTProgressUpdate(
				wso2Req
			);

		for (const progressUpdate of wso2Res.Response?.ProgressUpdate ?? []) {
			if (
				progressUpdate.NewStatus === TmForceLovEnum.NEW ||
				progressUpdate.NewStatus?.includes(TmForceLovEnum.POSTFIX_ENGINE)
			) {
				continue;
			}

			const lov = lovList.find(l => l.Vertical === progressUpdate.NewStatus);
			if (lov) {
				reportItem = {
					Title: lov.HorizontalValue,
					Description: lov.Description,
					Timestamp: formatDate(
						progressUpdate.LogDateTime,
						logDateTimePattern,
						logDateTimeFormat
					)
				};

				// Returned Case
				if (progressUpdate.NewStatus === TmForceLovEnum.RETURNED) {
					const secondLevelLov = lovList.find(
						l => l.ActivityType === progressUpdate.ActivityType
					);
					if (secondLevelLov) {
						horizontalDescription =
							reportItemList.length === 0
								? secondLevelLov.HorizontalDescription
								: horizontalDescription;

						reportItem = {
							Title: secondLevelLov.HorizontalValue,
							Description: secondLevelLov.Description,
							Timestamp: formatDate(
								progressUpdate.LogDateTime,
								logDateTimePattern,
								logDateTimeFormat
							)
						};
					}
				}
			} else {
				reportItem = {
					Title: progressUpdate.NewStatus ?? '',
					Description: '-',
					Timestamp: formatDate(
						progressUpdate.LogDateTime,
						logDateTimePattern,
						logDateTimeFormat
					)
				};
			}

			if (progressUpdate.Description) {
				// 2nd level escalation - NTT & NEXT escalation - format : LINK: **********-16158
				const linkDescription =
					progressUpdate.Description.split(':')[0].toUpperCase();
				if (linkDescription === 'LINK') {
					const secondLevelLov = lovList.find(
						l => l.ActivityType === progressUpdate.ActivityType
					);
					if (secondLevelLov) {
						horizontalDescription =
							reportItemList.length === 0
								? (secondLevelLov?.HorizontalDescription ?? '')
								: horizontalDescription;

						reportItem = {
							Title: secondLevelLov?.HorizontalValue ?? '',
							Description: secondLevelLov?.Description ?? '',
							Timestamp: formatDate(
								progressUpdate.LogDateTime,
								logDateTimePattern,
								logDateTimeFormat
							)
						};
					}
				}
			}

			// Case Details & Main Description - first index always the status reference
			if (reportItemList.length === 0) {
				horizontalDescription =
					lovList.find(l => l.Vertical === progressUpdate.NewStatus)
						?.HorizontalDescription ?? '';

				reportItem.OperationFlag =
					progressUpdate.NewStatus === TmForceLovEnum.CANCEL;

				caseDetails.CaseInformation = progressUpdate.ActivityType ?? '';

				if (
					progressUpdate.NewStatus === TmForceLovEnum.RESOLVED ||
					progressUpdate.NewStatus === TmForceLovEnum.CANCEL
				) {
					Object.assign(caseDetails, {
						CompletedDate: formatDate(
							progressUpdate.LogDateTime,
							logDateTimePattern,
							'dd MMMM yyyy'
						),
						CompletedTime: formatDate(
							progressUpdate.LogDateTime,
							logDateTimePattern,
							'hh:mm a'
						)
					});
				}
			}

			reportItemList.push(reportItem);
		}

		if (reportItemList.length > 0) {
			const lov = lovList.find(
				l => l.HorizontalValue === reportItemList[0].Title
			);
			if (lov) {
				if (
					lov.Vertical !== TmForceLovEnum.CLOSED &&
					lov.Vertical !== TmForceLovEnum.RESOLVED &&
					lov.Vertical !== TmForceLovEnum.CANCEL
				) {
					const wso2TechnicianReq: Wso2TmForceTechinicianDetailsReq = {
						TechnicianDetailsRequest: {
							TTNumber: req.ReferenceNo,
							OrderNumber: ''
						}
					};
					const wso2TechnicianRes: Wso2TmForceTechinicianDetailsRes =
						await this.mwIntegration.Wso2RecordIntegration.getWso2TmForceTechinicianDetails(
							wso2TechnicianReq
						);
					if (wso2TechnicianRes.Response) {
						const details = wso2TechnicianRes.Response.TechnicianDetail;
						technicianDetails = {
							ProfilePicture: details.TechnicianPhoto,
							TechnicianName: details.TechnicianName,
							PhoneNumber: details.TechnicianPhoneNumber,
							Latitude: details.Latitude === '0' ? '-' : details.Latitude,
							Longitude: details.Longitude === '0' ? '-' : details.Longitude,
							ETTA: details.ETTA ?? '-'
						};

						const appointmentDate: string =
							wso2TechnicianRes.Response.TechnicianDetail.TTAppointmentDate ??
							wso2TechnicianRes.Response.TechnicianDetail.ETTA;

						Object.assign(caseDetails, {
							AppointmentDate: formatDate(
								appointmentDate,
								'MM/dd/yyyy HH:mm:ss',
								'dd MMMM yyyy'
							),
							AppointmentTime: formatDate(
								appointmentDate,
								'MM/dd/yyyy HH:mm:ss',
								'hh:mm a'
							)
						});
					}
				}
			}
		}

		return {
			horizontalDescription,
			caseDetails,
			reportItemList,
			technicianDetails
		};
	}

	private async getAssuranceLovList(): Promise<SelectTmForceStatusLov[]> {
		// Store in redis for 24 hrs
		const cacheName = CacheKeyEnum.LOV_ASSURANCE_LIST;
		const cache = await getCache(cacheName);
		if (cache) {
			return JSON.parse(cache) as SelectTmForceStatusLov[];
		}
		const lovList: SelectTmForceStatusLov[] = await this.db
			.select()
			.from(tmForceStatusLovTableSchema)
			.where(eq(tmForceStatusLovTableSchema.Category, 'ASSURANCE'));
		await setCache(cacheName, JSON.stringify(lovList), 86400);

		return lovList;
	}
}

export default ActivityTracker;
