import { format } from 'date-fns';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { envConfig } from '../../../../config/env.config';
import { EmailEnum } from '../../../../enum/notification.enum';
import {
	SRContactMethodEnum,
	SRTypeEnum
} from '../../../../enum/serviceRequest.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import {
	LightweightFlagEnum,
	SystemNameEnum
} from '../../../../enum/wso2.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type { Wso2EmailReq } from '../../../../integration/wso2/notification/schemas/api/wso2Email.schema';
import type {
	Wso2CreateSRReq,
	Wso2CreateSRRes
} from '../../../../integration/wso2/record/schemas/api/wso2CreateSR.schema';
import type {
	Wso2CustomerAccountReq,
	Wso2CustomerAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2CustomerAccount.schema';
import type {
	Wso2ServiceAccountReq,
	Wso2ServiceAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2ServiceAccount.schema';
import { UE_ERROR } from '../../../../middleware/error';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import {
	getMyTimeZoneDate,
	icpBillingProfile,
	novaBillingProfile
} from '../../../../shared/common';
import { decrypt } from '../../../../shared/encryption/aesGcm';
import type { NovaIcpBillingProfile } from '../../../../shared/schemas/api/novaBillingProfile.schema';
import type {
	CreateSRReq,
	CreateSRRes,
	SREmail,
	ServiceDetails
} from '../schemas/api/serviceRequest.schema';
import {
	type InsertServiceRequestHistory,
	serviceRequestHistoryTableSchema
} from '../schemas/db/serviceRequestHistory.schema';

class ServiceRequest {
	private db: NodePgDatabase;
	private integrationId: string;
	private idTokenInfo: IdTokenInfo;
	private mwIntegration: MwIntegration;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
		this.idTokenInfo = idTokenInfo;
		this.mwIntegration = new MwIntegration(this.integrationId);
	}

	async createSR(req: CreateSRReq): Promise<CreateSRRes> {
		// digital signature to verify the request is from the correct customer
		if (
			req.CustomerIdType !== this.idTokenInfo.IdType ||
			req.CustomerIdValue !== this.idTokenInfo.IdValue
		) {
			throw new UE_ERROR(
				'Customer Id is not matched',
				StatusCodeEnum.BAD_REQUEST_ERROR
			);
		}

		const serviceDetails: ServiceDetails = await this.getServiceDetails(req);

		const billingProfile: NovaIcpBillingProfile =
			req.SystemName === SystemNameEnum.ICP
				? await icpBillingProfile(
						this.integrationId,
						this.idTokenInfo.IdType,
						this.idTokenInfo.IdValue,
						serviceDetails.decryptedBillAccNo,
						serviceDetails.wso2CARes
					)
				: await novaBillingProfile(
						this.integrationId,
						serviceDetails.decryptedBillAccNo
					);

		const wso2SRReq: Wso2CreateSRReq =
			req.Type === SRTypeEnum.TERMINATION
				? await this.getTerminationSRBody(req, serviceDetails)
				: await this.getComplaintSRBody(req, serviceDetails);

		const wso2SRRes: Wso2CreateSRRes =
			await this.mwIntegration.Wso2RecordIntegration.wso2CreateSR(wso2SRReq);

		const insertReq: InsertServiceRequestHistory = {
			SystemName: wso2SRReq.SubmitGeneralSR.SystemName,
			Type: wso2SRReq.SubmitGeneralSR.Type,
			InterfaceStatus: wso2SRReq.SubmitGeneralSR.InterfaceStatus,
			OwnerGroup: wso2SRReq.SubmitGeneralSR.OwnerGroup,
			SRNumber: wso2SRRes?.RespondSubmitGeneralSR?.SRNumber ?? '',
			Case: wso2SRReq.SubmitGeneralSR.Case,
			Category: wso2SRReq.SubmitGeneralSR.Category,
			SubCategory: wso2SRReq.SubmitGeneralSR.SubCategory,
			CustomerAccountNo: wso2SRReq.SubmitGeneralSR.CustomerAccountNo,
			BillingAccountNo: wso2SRReq.SubmitGeneralSR.BillingAccountNo,
			ServiceId: wso2SRReq.SubmitGeneralSR.TMBLoginID,
			ServiceRowId: wso2SRReq.SubmitGeneralSR.ServiceRowID,
			CustomerComments: wso2SRReq.SubmitGeneralSR.CustomerComments,
			ContactDetails: {
				ContactID: wso2SRReq.SubmitGeneralSR.ContactID
			},
			PreferredAcknowledgment:
				wso2SRReq.SubmitGeneralSR.PreferredAcknowledgement,
			ChangePreference: {
				CommFlag: wso2SRReq.SubmitGeneralSR.ChangePreferredCommFlag,
				CommMethod: wso2SRReq.SubmitGeneralSR.ChangePreferredCommMethod,
				ContactName: wso2SRReq.SubmitGeneralSR.ChangePreferredContactName,
				EmailAddress: wso2SRReq.SubmitGeneralSR.ChangePreferredEmailAddress,
				CellularPhone: wso2SRReq.SubmitGeneralSR.ChangePreferredCellularPhone,
				HomePhone: wso2SRReq.SubmitGeneralSR.ChangePreferredHomePhone,
				WorkPhone: wso2SRReq.SubmitGeneralSR.ChangePreferredWorkPhone
			},
			Product: wso2SRReq.SubmitGeneralSR.Product,
			ProductCategory: wso2SRReq.SubmitGeneralSR.ProductCategory,
			ProductType: wso2SRReq.SubmitGeneralSR.ProductType,
			Priority: wso2SRReq.SubmitGeneralSR.Priority,
			Status: wso2SRReq.SubmitGeneralSR.Status,
			Source: wso2SRReq.SubmitGeneralSR.Source,
			CreatedSource: wso2SRReq.SubmitGeneralSR.CreatedSource,
			MarketingConsent: ''
		};

		await this.db
			.insert(serviceRequestHistoryTableSchema)
			.values(insertReq)
			.returning();

		// Send email
		const subject: string =
			wso2SRReq.SubmitGeneralSR.Case === 'Termination'
				? `TM Online Service Request Confirmation - Ref #${wso2SRRes?.RespondSubmitGeneralSR?.SRNumber}`
				: 'Your report has been escalated';

		const emailBody: string = await this.getServiceRequestEmailBody({
			SRNumber: wso2SRRes?.RespondSubmitGeneralSR?.SRNumber,
			currentTime: getMyTimeZoneDate().toISOString(),
			username: billingProfile.AccountName
		});

		let email = '';
		if (req.Termination?.EmailAddress) {
			email = req.Termination.EmailAddress;
		} else if (
			req.Complaint?.PreferredAcknowledgement === SRContactMethodEnum.EMAIL
		) {
			email = req.Complaint.PreferredContactValue;
		} else {
			email = billingProfile.AccountEmail;
		}

		const maskedIdValue = this.idTokenInfo.IdValue.replace(
			/^(.{2}).*(.{2})$/,
			(_, p1, p2) =>
				`${p1}${'*'.repeat(this.idTokenInfo.IdValue.length - 4)}${p2}`
		);

		if (email === 'N/A') {
			const res: CreateSRRes = {
				Success: true,
				Code: StatusCodeEnum.CREATED,
				IntegrationId: this.integrationId,
				Response: {
					SRNumber: wso2SRRes?.RespondSubmitGeneralSR?.SRNumber ?? '',
					IsEmailSent: false,
					CustomerIdType: this.idTokenInfo.IdType,
					CustomerIdValue: maskedIdValue
				}
			};
			return res;
		}

		const wso2EmailReq: Wso2EmailReq = {
			to: email,
			from: EmailEnum.FROM_NOREPLY,
			subject: subject,
			body: emailBody
		};

		const isEmailSent: boolean =
			emailBody !== ''
				? await this.mwIntegration
						.getWso2NotificationIntegration()
						.getWso2SendEmail(
							wso2EmailReq,
							this.idTokenInfo.IdValue,
							'SR Creation/Service Termination'
						)
				: false;

		// Set Response
		const res: CreateSRRes = {
			Success: true,
			Code: StatusCodeEnum.CREATED,
			IntegrationId: this.integrationId,
			Response: {
				SRNumber: wso2SRRes?.RespondSubmitGeneralSR?.SRNumber ?? '',
				IsEmailSent: isEmailSent,
				CustomerIdType: this.idTokenInfo.IdType,
				CustomerIdValue: maskedIdValue
			}
		};
		return res;
	}

	private async getServiceDetails(req: CreateSRReq): Promise<ServiceDetails> {
		const decryptedBillAccNo: string = await decrypt(req.EncryptedBillAccNo);

		const wso2CARequest: Wso2CustomerAccountReq = {
			idType: this.idTokenInfo.IdType,
			idValue: this.idTokenInfo.IdValue
		};

		const wso2CARes: Wso2CustomerAccountRes =
			await this.mwIntegration.Wso2UserIntegration.getWso2CustomerAccount(
				wso2CARequest,
				LightweightFlagEnum.NO
			);

		let accountNo = '';
		let contactId = '';
		let billingAccountRowId = '';
		let serviceRowId = '';
		let serviceId = '';

		for (const ca of wso2CARes.Response?.CustomerAccounts ?? []) {
			if (ca?.SystemName === req.SystemName) {
				contactId = ca?.PreferredCustomerContactDetails?.ContactId ?? '';
				accountNo = ca?.AccountNo ?? '';
			}

			if (
				ca?.SystemName?.toUpperCase() === SystemNameEnum.ICP &&
				ca?.BillingAccounts &&
				ca?.BillingAccounts.length > 0
			) {
				billingAccountRowId =
					ca?.BillingAccounts.find(
						ba => ba?.AccountNumber && ba.AccountNumber === decryptedBillAccNo
					)?.Id ?? '';
			}
		}

		const wso2SAReq: Wso2ServiceAccountReq = {
			idType: this.idTokenInfo.IdType,
			idValue: this.idTokenInfo.IdValue,
			BillingAccountNo: decryptedBillAccNo,
			SystemName: req.SystemName
		};

		const wso2SARes: Wso2ServiceAccountRes =
			(await this.mwIntegration.Wso2UserIntegration.getWso2ServiceAccount(
				wso2SAReq,
				LightweightFlagEnum.NO
			)) as Wso2ServiceAccountRes;

		for (const sa of wso2SARes?.Response?.ServiceAccount ?? []) {
			if (req.SystemName === SystemNameEnum.ICP) {
				for (const moli of sa?.ServiceAccountMoli ?? []) {
					// get service row id
					if (moli?.DatCode?.toUpperCase() === 'DEL') {
						serviceRowId = moli?.RootAssetId ?? '';
					}

					if (
						sa?.Type?.toLowerCase() === 'combo' &&
						sa.DatCode?.toUpperCase() !== 'DEL' &&
						moli?.DatCode?.toUpperCase() === 'STX'
					) {
						for (const oli of moli?.ServiceAccountOli ?? []) {
							if (oli?.Type?.toLowerCase() === 'speed') {
								serviceId = moli.SerialNumber ?? '';
							}
						}
					}
				}
			} else if (req.SystemName === SystemNameEnum.NOVA) {
				for (const moli of sa?.ServiceAccountMoli ?? []) {
					for (const oli of moli?.ServiceAccountOli ?? []) {
						if (oli?.Type?.toLowerCase() === 'speed') {
							serviceRowId = oli?.RootAssetId ?? '';
						}
					}
				}
			}
		}

		return {
			decryptedBillAccNo,
			accountNo,
			contactId,
			billingAccountRowId,
			serviceRowId,
			serviceId,
			wso2CARes
		};
	}

	private async getTerminationSRBody(
		req: CreateSRReq,
		serviceDetails: ServiceDetails
	): Promise<Wso2CreateSRReq> {
		if (req.Termination) {
			const wso2SRReq: Wso2CreateSRReq = {
				SubmitGeneralSR: {
					SystemName: req.SystemName,
					CustomerIDType: this.idTokenInfo.IdType,
					CustomerIDValue: this.idTokenInfo.IdValue,
					CustomerAccountNo: serviceDetails.accountNo,
					Type: req.Type,
					Category: 'Billing',
					SubCategory: 'Refund',
					Case: 'Termination',
					CreatedDate: format(getMyTimeZoneDate(), 'dd/MM/yyyy HH:mm:ss'),
					Source:
						req.SystemName === SystemNameEnum.ICP ? 'DICE' : 'Customer Portal',
					CreatedSource: 'DICE',
					Priority: '4-Low',
					Status:
						req.SystemName === SystemNameEnum.NOVA ? 'Unassigned' : 'Assigned',
					OwnerGroup: 'TM_LIVE_CHAT',
					CustomerComments: req.CustomerComments,
					ProductCategory: 'Internet',
					Product: req.ProductName,
					ProductType: 'Telephone',
					ServiceRowID: serviceDetails.serviceRowId,
					BillingAccountNo: serviceDetails.decryptedBillAccNo,
					BillingAccountRowID: serviceDetails.billingAccountRowId,
					PreferredAcknowledgement: 'Email',
					ChangePreferredCommFlag: 'N',
					ChangePreferredCommMethod: '',
					ChangePreferredContactName: '',
					ChangePreferredEmailAddress: req.Termination.EmailAddress,
					ChangePreferredCellularPhone: req.Termination.ContactNo,
					ChangePreferredHomePhone: '',
					ChangePreferredWorkPhone: '',
					ContactID: serviceDetails.contactId,
					TMTAndCFlag: req.Termination.TMTAndCFlag,
					InterfaceStatus: '',
					ContactDetailRowID: '',
					ContactDetailReportedID: '',
					LoginIDType: '',
					TMBLoginID: '',
					ClosureCategory: '',
					ClosureReason: '',
					ClosureRemarks: '',
					SerialNumber: '',
					TMBModifyOrderRequest: '',
					TMBModifyOrderReason: '',
					TMBSMARTProduct: '',
					TMTTBankAcctNum: req.Termination.BankAcctNum,
					TMTTBankName: req.Termination.BankName,
					TMTTPaymentMode: 'Telegraphic Transfer',
					TMTTIDNum: this.idTokenInfo.IdValue,
					TMTTIDType: this.idTokenInfo.IdType,
					TMTTConfirmBankAcctNum: req.Termination.BankAcctNum
				}
			};
			return wso2SRReq;
		}

		throw new UE_ERROR(
			'Termination SR values is not set',
			StatusCodeEnum.BAD_REQUEST_ERROR
		);
	}

	private async getComplaintSRBody(
		req: CreateSRReq,
		serviceDetails: ServiceDetails
	): Promise<Wso2CreateSRReq> {
		if (req.Complaint) {
			const productType: string = 'Telephone';
			let icpCustomerComments = '';
			if (req.SystemName === SystemNameEnum.ICP) {
				icpCustomerComments = `${req.CustomerComments}\n\nYour Preferred Communication Method\t: ${req.Complaint.PreferredAcknowledgement}\nContact Name\t\t\t: ${req.Complaint.PreferredContactName}\nYour Contact Info\t\t\t: ${req.Complaint.PreferredContactValue}\n\nProduct Type\t\t\t: ${productType}\nCase\t\t\t\t: ${req.Complaint.Case}`;
			}

			const wso2SRReq: Wso2CreateSRReq = {
				SubmitGeneralSR: {
					SystemName: req.SystemName,
					CustomerIDType: this.idTokenInfo.IdType,
					CustomerIDValue: this.idTokenInfo.IdValue,
					CustomerAccountNo: serviceDetails.accountNo,
					Type: req.Type,
					Category: req.Complaint.Category,
					SubCategory: req.Complaint.SubCategory,
					Case: req.Complaint.Case,
					CreatedDate:
						req.SystemName === SystemNameEnum.ICP
							? format(new Date(), 'dd/MM/yyyy HH:mm:ss')
							: '',
					Source:
						req.SystemName === SystemNameEnum.ICP ? 'DICE' : 'Customer Portal',
					CreatedSource: 'DICE',
					Priority: '4-Low',
					Status:
						req.SystemName === SystemNameEnum.NOVA ? 'Unassigned' : 'Assigned',
					OwnerGroup: req.Complaint.OwnerGroup,
					CustomerComments:
						req.SystemName === SystemNameEnum.NOVA
							? req.CustomerComments
							: icpCustomerComments,
					ProductCategory: 'Internet',
					Product: req.ProductName,
					ProductType: productType,
					ServiceRowID: serviceDetails.serviceRowId,
					BillingAccountNo: serviceDetails.decryptedBillAccNo,
					BillingAccountRowID: serviceDetails.billingAccountRowId,
					PreferredAcknowledgement: req.Complaint.PreferredAcknowledgement,
					ChangePreferredCommFlag: 'Y',
					ChangePreferredCommMethod: req.Complaint.PreferredAcknowledgement,
					ChangePreferredContactName: req.Complaint.PreferredContactName,
					ChangePreferredEmailAddress:
						req.Complaint.PreferredAcknowledgement === SRContactMethodEnum.EMAIL
							? req.Complaint.PreferredContactValue
							: '',
					ChangePreferredCellularPhone:
						req.Complaint.PreferredAcknowledgement ===
							SRContactMethodEnum.MOBILE ||
						req.Complaint.PreferredAcknowledgement === SRContactMethodEnum.SMS
							? req.Complaint.PreferredContactValue
							: '',
					ChangePreferredHomePhone:
						req.Complaint.PreferredAcknowledgement ===
						SRContactMethodEnum.HOME_PHONE
							? req.Complaint.PreferredContactValue
							: '',
					ChangePreferredWorkPhone:
						req.Complaint.PreferredAcknowledgement ===
						SRContactMethodEnum.WORK_PHONE
							? req.Complaint.PreferredContactValue
							: '',
					ContactID:
						req.SystemName === SystemNameEnum.NOVA
							? serviceDetails.contactId
							: '',
					TMTAndCFlag: '',
					InterfaceStatus: req.SystemName === SystemNameEnum.ICP ? 'New' : '',
					ContactDetailRowID:
						req.SystemName === SystemNameEnum.ICP
							? serviceDetails.contactId
							: '',
					ContactDetailReportedID:
						req.SystemName === SystemNameEnum.ICP
							? serviceDetails.contactId
							: '',
					LoginIDType:
						req.SystemName === SystemNameEnum.ICP
							? '/service/tmm_streamyx'
							: '',
					TMBLoginID:
						req.SystemName === SystemNameEnum.ICP
							? serviceDetails.serviceId
							: '',
					ClosureCategory: '',
					ClosureReason: '',
					ClosureRemarks: '',
					SerialNumber: '',
					TMBModifyOrderRequest: '',
					TMBModifyOrderReason: '',
					TMBSMARTProduct: ''
				}
			};

			return wso2SRReq;
		}

		throw new UE_ERROR(
			'Complaint SR values is not set',
			StatusCodeEnum.BAD_REQUEST_ERROR
		);
	}

	private async getServiceRequestEmailBody(body: SREmail): Promise<string> {
		const env = envConfig();
		const baseUrl: string = env.SR_CREATE_REVAMP_TEMPLATE;
		const params = new URLSearchParams(body);
		const queryString: string = params.toString();
		const emailUrl: string = `${baseUrl}?${queryString}`;
		const emailBody: string =
			await this.mwIntegration.EmailTemplateIntegration.getEmailBodyTemplate(
				emailUrl
			);
		return emailBody;
	}
}

export default ServiceRequest;
