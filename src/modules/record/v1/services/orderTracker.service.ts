import { format, isAfter, isBefore, isEqual, parse } from 'date-fns';
import { and, between, eq, sql } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { pinoLog } from '../../../../config/pinoLog.config';
import { OrderTypeEnum, ProgressStatusEnum } from '../../../../enum/order.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { AppointmentActionEnum } from '../../../../enum/tracker.enum';
import { SystemNameEnum } from '../../../../enum/wso2.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type { Wso2AcknowledgeAppointmentReq } from '../../../../integration/wso2/record/schemas/api/wso2AckAppointment.schema';
import type {
	Wso2GetAppointmentSlotReq,
	Wso2GetAppointmentSlotRes
} from '../../../../integration/wso2/record/schemas/api/wso2AppointmentSlot.schema';
import type {
	Wso2OrderTrackingReq,
	Wso2OrderTrackingRes
} from '../../../../integration/wso2/record/schemas/api/wso2OrderTracking.schema';
import type { Wso2TmForceAcceptanceFormRes } from '../../../../integration/wso2/record/schemas/api/wso2TmForceAcceptanceForm.schema';
import type {
	Wso2OrderStatusProgress,
	Wso2TmForceProgressUpdateRes
} from '../../../../integration/wso2/record/schemas/api/wso2TmForceGetProgressUpdate.schema';
import type {
	Wso2TmForcePostponeAppointmentReq,
	Wso2TmForcePostponeAppointmentRes
} from '../../../../integration/wso2/record/schemas/api/wso2TmForcePostponeAppointment.schema';
import type {
	Wso2UpdateAppointmentReq,
	Wso2UpdateAppointmentRes
} from '../../../../integration/wso2/record/schemas/api/wso2UpdateAppointment.schema';
import { UE_ERROR } from '../../../../middleware/error';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import { formatDate, getMyTimeZoneDate } from '../../../../shared/common';
import { encrypt } from '../../../../shared/encryption/aesGcm';
import type { BaseResponse } from '../../../../shared/schemas/api/responses.schema';
import {
	type SelectNonOrderableTxnHistory,
	nonOrderableTxnHistoryTableSchema
} from '../../../order/v1/schemas/db/nonOrderableTxnHistory.schema';
import {
	type SelectCustomerOrder,
	type SelectOrderablePlan,
	type SelectOrderableTxnHistory,
	customerOrderTableSchema,
	orderablePlanTableSchema,
	orderableTxnHistoryTableSchema
} from '../../../order/v1/schemas/db/orderable.schema';
import OrderTrackerHelper from '../helpers/orderTracker.helper';
import type {
	AppointmentSlotReq,
	AppointmentSlotRes,
	AppointmentUpdateReq,
	CaseDetails,
	OrderDetailsReq,
	OrderDetailsRes,
	OrderInformation,
	OrderListObj,
	OrderListRes,
	OrderObj,
	ReportItemList,
	ServiceInformation,
	TechnicianDetails,
	ValidateOrderIdRes
} from '../schemas/api/orderTracker.schema';
import {
	type SelectAppointmentCounter,
	appointmentCounterTableSchema
} from '../schemas/db/appointmentCounter.schema';
import { appointmentTxnHistoryTableSchema } from '../schemas/db/appointmentTxnHistory.schema';
import {
	type SelectSiebelOrderType,
	siebelOrderTypeTableSchema
} from '../schemas/db/siebelOrderType.schema';
import {
	type SelectTmForceStatusLov,
	tmForceStatusLovTableSchema
} from '../schemas/db/tmForceLov.schema';

class OrderTracker {
	private db: NodePgDatabase;
	private integrationId: string;
	private idTokenInfo: IdTokenInfo;
	private mwIntegration: MwIntegration;
	private orderTrackerHelper: OrderTrackerHelper;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo) {
		this.integrationId = integrationId;
		this.idTokenInfo = idTokenInfo;
		this.db = getDbInstance();
		this.mwIntegration = new MwIntegration(integrationId);
		this.orderTrackerHelper = new OrderTrackerHelper(integrationId);
	}

	async getNonOrderableHistory(): Promise<OrderListRes> {
		const orderListObj: OrderListObj = [];
		const today: Date = getMyTimeZoneDate();
		const startDate: number = new Date(today).setDate(today.getDate() - 90);
		const endDate: number = new Date(today).setDate(today.getDate() + 1);

		const orderHistory: SelectNonOrderableTxnHistory[] = await this.db
			.select()
			.from(nonOrderableTxnHistoryTableSchema)
			.where(
				and(
					eq(nonOrderableTxnHistoryTableSchema.IdType, this.idTokenInfo.IdType),
					eq(
						nonOrderableTxnHistoryTableSchema.IdValue,
						this.idTokenInfo.IdValue
					),
					between(
						nonOrderableTxnHistoryTableSchema.CreatedAt,
						new Date(startDate),
						new Date(endDate)
					)
				)
			)
			.execute();

		for (const order of orderHistory) {
			const diceOrderId: string = order.OrderPrefix.concat('-').concat(
				String(order.OrderId)
			);

			const orderObj: OrderObj = {
				Title: `SLOF - ${order.ProductName}`,
				Category: 'HOME',
				SubCategory: 'MY APPLICATION',
				Status: order.OrderStatus,
				SiebelOrderStatus: 'N/A',
				CreatedDate: format(order.CreatedAt, 'dd MMMM yyyy hh:mm aaa'),
				OrderId: null,
				DiceOrderId: diceOrderId,
				OrderNumber: null,
				EncryptedBillAccNo: await encrypt(order.BillingAccountNo),
				SystemName: order.SystemName,
				OrderType: 'N/A',
				SubOrderType: 'N/A',
				IsTruckRoll: false,
				Product: {
					Name: order.ProductName,
					Category: order.Category
				}
			};

			if (order.OrderStatus !== 'Completed') {
				orderObj.ServiceInfo = {
					Unit: order.OrderData,
					Floor: order.OrderData,
					BuildingName: order.OrderData,
					StreetType: order.OrderData,
					StreetName: order.OrderData,
					Section: order.OrderData,
					City: order.OrderData,
					Postcode: order.OrderData,
					Country: order.OrderData
				};
			}

			orderListObj.push(orderObj);
		}

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				OrderList: orderListObj
			}
		};
	}

	async getOrderableHistory(): Promise<OrderListRes> {
		const orderListObj: OrderListObj = [];
		const today: Date = getMyTimeZoneDate();
		const startDate: number = new Date(today).setDate(today.getDate() - 90);
		const endDate: number = new Date(today).setDate(today.getDate() + 1);
		const formattedStartDate: string = format(startDate, 'MM/dd/yyyy');
		const formattedEndDate: string = format(endDate, 'MM/dd/yyyy');
		const diceOrderIds: string[] = [];
		let orderObj: OrderObj;

		const customer: SelectCustomerOrder[] = await this.db
			.select()
			.from(customerOrderTableSchema)
			.where(
				and(
					eq(customerOrderTableSchema.IdType, this.idTokenInfo.IdType),
					eq(customerOrderTableSchema.IdValue, this.idTokenInfo.IdValue)
				)
			);

		const orderHistory: SelectOrderableTxnHistory[] =
			customer.length > 0
				? await this.db
						.select()
						.from(orderableTxnHistoryTableSchema)
						.where(
							and(
								eq(
									orderableTxnHistoryTableSchema.CustomerId,
									customer[0].CustomerId
								),
								between(
									orderableTxnHistoryTableSchema.CreatedAt,
									new Date(startDate),
									new Date(endDate)
								)
							)
						)
				: [];

		const wso2Req: Wso2OrderTrackingReq = {
			OrderTracking: {
				OrderList: {
					CustomerID: this.idTokenInfo.IdValue,
					IdType: this.idTokenInfo.IdType,
					StartDateTime: formattedStartDate,
					EndDateTime: formattedEndDate
				}
			}
		};

		const wso2Res: Wso2OrderTrackingRes =
			await this.mwIntegration.Wso2RecordIntegration.getWso2OrderTracking(
				wso2Req
			);

		const siebelOrderType: SelectSiebelOrderType[] = await this.db
			.select()
			.from(siebelOrderTypeTableSchema)
			.execute();

		if (wso2Res.Response.OrderList?.['TmOrderEntry-OrdersIntegration-NOVA']) {
			for (const nova of wso2Res.Response.OrderList[
				'TmOrderEntry-OrdersIntegration-NOVA'
			]) {
				const mapping: SelectSiebelOrderType | undefined = nova.SubOrderType
					? siebelOrderType.find(
							orderType =>
								orderType.SystemName === SystemNameEnum.NOVA &&
								orderType.OrderType === nova.OrderType &&
								orderType.SubOrderType === nova.SubOrderType
						)
					: siebelOrderType.find(
							orderType =>
								orderType.SystemName === SystemNameEnum.NOVA &&
								orderType.OrderType === nova.OrderType
						);

				if (mapping && nova.OrderNumber && nova.OrderId) {
					let productName = 'N/A';
					let productCategory = 'N/A';
					if (nova.TMDiCEOrderNumber) {
						diceOrderIds.push(nova.TMDiCEOrderNumber);
						const addonOrder: SelectOrderableTxnHistory[] = orderHistory.filter(
							order => order.OrderId === nova.TMDiCEOrderNumber
						);
						if (addonOrder.length > 0) {
							productName = addonOrder[0].ProductName;
							productCategory = addonOrder[0].OrderCategory;
						}
					}

					orderObj = {
						Title: mapping.Title,
						Category: mapping.Category,
						SubCategory: mapping.SubCategory,
						Status:
							nova.Status === 'Cancelled' ||
							nova.Status === 'Completed' ||
							nova.Status === 'Complete'
								? nova.Status
								: 'In Progress',
						SiebelOrderStatus: nova.Status ?? '',
						CreatedDate: formatDate(
							nova.Created,
							'MM/dd/yyyy HH:mm:ss',
							'dd MMMM yyyy hh:mm aaa'
						),
						OrderId: nova.OrderId,
						DiceOrderId: nova.TMDiCEOrderNumber ?? null,
						OrderNumber: nova.OrderNumber,
						EncryptedBillAccNo: nova.BillingAccountNumber
							? await encrypt(nova.BillingAccountNumber)
							: null,
						SystemName: SystemNameEnum.NOVA,
						OrderType: mapping.OrderType,
						SubOrderType: mapping.SubOrderType,
						IsTruckRoll: mapping.TruckRoll,
						Product: {
							Name: productName,
							Category: productCategory
						}
					};

					if (
						orderObj.Status !== 'Completed' &&
						orderObj.Status !== 'Complete'
					) {
						orderObj.ServiceInfo = await this.orderTrackerHelper.getServiceInfo(
							this.idTokenInfo.IdValue,
							this.idTokenInfo.IdType,
							nova.OrderId
						);
					}

					orderListObj.push(orderObj);
				}
			}
		}

		if (wso2Res.Response.OrderList?.['TmOrderEntry-OrdersIntegration-ICP']) {
			for (const icp of wso2Res.Response.OrderList[
				'TmOrderEntry-OrdersIntegration-ICP'
			]) {
				const mapping: SelectSiebelOrderType | undefined = siebelOrderType.find(
					orderType =>
						orderType.SystemName === SystemNameEnum.ICP &&
						orderType.OrderType === icp.OrderType
				);

				if (mapping && icp.SiebelOrderId) {
					orderObj = {
						Title: mapping.Title,
						Category: mapping.Category,
						SubCategory: mapping.SubCategory,
						Status:
							icp.OrderStatus === 'Cancelled' ||
							icp.OrderStatus === 'Completed' ||
							icp.OrderStatus === 'Complete'
								? icp.OrderStatus
								: 'In Progress',
						SiebelOrderStatus: icp.OrderStatus ?? '',
						CreatedDate: icp.CreationDate
							? formatDate(
									icp.CreationDate,
									'MM/dd/yyyy HH:mm:ss',
									'dd MMMM yyyy hh:mm aaa'
								)
							: '',
						OrderId: icp.SiebelOrderId,
						OrderNumber: null,
						DiceOrderId: null,
						EncryptedBillAccNo: null,
						SystemName: SystemNameEnum.ICP,
						OrderType: mapping.OrderType,
						SubOrderType: mapping.SubOrderType,
						IsTruckRoll: mapping.TruckRoll,
						Product: null
					};

					if (
						orderObj.Status !== 'Completed' &&
						orderObj.Status !== 'Complete'
					) {
						orderObj.ServiceInfo = await this.orderTrackerHelper.getServiceInfo(
							this.idTokenInfo.IdValue,
							this.idTokenInfo.IdType,
							icp.SiebelOrderId
						);
					}

					orderListObj.push(orderObj);
				}
			}
		}

		for (const order of orderHistory) {
			const orderExistInSiebel = diceOrderIds.find(
				orderId => orderId === order.OrderId
			);
			if (!orderExistInSiebel) {
				orderObj = {
					Title: order.OrderType,
					Category: 'HOME',
					SubCategory:
						order.OrderType === OrderTypeEnum.ADD_ONS
							? 'MY ORDER'
							: 'MY APPLICATION',
					Status: order.OrderStatus,
					SiebelOrderStatus: null,
					CreatedDate: format(order.CreatedAt, 'dd MMMM yyyy hh:mm aaa'),
					OrderId: null,
					DiceOrderId: order.OrderId,
					EncryptedBillAccNo: order.BillingAccountNo
						? await encrypt(order.BillingAccountNo)
						: null,
					OrderNumber: null,
					SystemName: order.TargetSystem,
					OrderType: 'N/A',
					SubOrderType: 'N/A',
					IsTruckRoll: false,
					Product: {
						Name: order.ProductName,
						Category: order.OrderCategory
					}
				};

				if (order.OrderStatus !== ProgressStatusEnum.COMPLETED) {
					orderObj.ServiceInfo = {
						Unit: order.Address?.UnitNo,
						Floor: order.Address?.FloorNo,
						BuildingName: order.Address?.BuildingName,
						StreetType: order.Address?.StreetType,
						StreetName: order.Address?.StreetName,
						Section: order.Address?.Section,
						City: order.Address?.City,
						Postcode: order.Address?.Postcode,
						Country: order.Address?.Country
					};
				}

				orderListObj.push(orderObj);
			}
		}

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				OrderList: orderListObj
			}
		};
	}

	async getOrderDetails(req: OrderDetailsReq): Promise<OrderDetailsRes> {
		let technicianDetails: TechnicianDetails = null;
		let caseDetails: CaseDetails = {} as CaseDetails;
		let reportItemList: ReportItemList = [];
		let orderInformation: OrderInformation | null = null;
		let serviceInformation: ServiceInformation | null = null;
		let horizontalDescription = '';
		let dbResult: {
			OrderableTxnHistory: SelectOrderableTxnHistory;
			OrderablePlan: SelectOrderablePlan;
		}[] = [];

		if (req.DiceOrderId) {
			dbResult = await this.db
				.select({
					OrderableTxnHistory: orderableTxnHistoryTableSchema,
					OrderablePlan: orderablePlanTableSchema
				})
				.from(orderableTxnHistoryTableSchema)
				.innerJoin(
					orderablePlanTableSchema,
					eq(
						orderableTxnHistoryTableSchema.PlanId,
						orderablePlanTableSchema.PlanId
					)
				)
				.where(eq(orderableTxnHistoryTableSchema.OrderId, req.DiceOrderId))
				.catch(err => {
					throw new UE_ERROR(
						'An unexpected error occurred.',
						StatusCodeEnum.UE_INTERNAL_SERVER,
						{
							integrationId: this.integrationId,
							response: String(err)
						}
					);
				});
		}

		if (dbResult.length > 0) {
			const orderHistory: SelectOrderableTxnHistory =
				dbResult[0].OrderableTxnHistory;
			orderInformation = {
				OrderId: orderHistory.OrderId,
				OrderPlanName: dbResult[0].OrderablePlan.PlanName,
				CreatedDate: format(orderHistory.CreatedAt, 'dd MMMM yyy'),
				CreatedTime: format(orderHistory.CreatedAt, 'HH:mm a'),
				Source: `Subscription Channel : ${orderHistory.SourceSystem}`
			};

			serviceInformation = {
				Unit: orderHistory.Address?.UnitNo,
				Floor: orderHistory.Address?.FloorNo,
				BuildingName: orderHistory.Address?.BuildingName,
				StreetType: orderHistory.Address?.StreetType,
				StreetName: orderHistory.Address?.StreetName,
				Section: orderHistory.Address?.Section,
				City: orderHistory.Address?.City,
				State: orderHistory.Address?.State,
				Postcode: orderHistory.Address?.Postcode,
				Country: orderHistory.Address?.Country
			};

			reportItemList = await this.getOrderFromDb(req.IsTruckRoll, dbResult[0]);
		} else if (req.OrderId && req.OrderNumber) {
			({ orderInformation, serviceInformation } =
				await this.orderTrackerHelper.setOrderDetailsServiceInfo(
					req.OrderId,
					this.idTokenInfo.IdType,
					this.idTokenInfo.IdValue
				));

			({
				horizontalDescription,
				caseDetails,
				reportItemList,
				technicianDetails
			} = await this.getOrderProgress(req, req.OrderNumber));
		} else {
			throw new UE_ERROR(
				'We are unable to process your request due to missing required parameters. You must provide either OrderId and OrderNumber or DiceOrderId for us to be able to retrieve the order details.',
				StatusCodeEnum.BAD_REQUEST_ERROR
			);
		}

		return {
			IntegrationId: this.integrationId,
			Success: true,
			Code: StatusCodeEnum.OK,
			Response: {
				TicketStatusDescription: horizontalDescription,
				Status:
					reportItemList.length === 0
						? req.ReferenceStatus
						: reportItemList[0].Title,
				CaseDetails: caseDetails,
				ReportStatus: reportItemList,
				TechnicianDetails: technicianDetails,
				OrderDetails: orderInformation,
				ServiceInformation: serviceInformation
			}
		};
	}

	// Digital order
	private async getOrderFromDb(
		isTruckRoll: boolean,
		dbResult: {
			OrderableTxnHistory: SelectOrderableTxnHistory;
			OrderablePlan: SelectOrderablePlan;
		}
	): Promise<ReportItemList> {
		const dbDateTimePattern: string = 'dd/MM/yyyy HH:mm:ss';
		const dbDateTimeFormat: string = 'dd MMMM yyyy, hh:mm a';
		const lovList: SelectTmForceStatusLov[] = await this.db
			.select()
			.from(tmForceStatusLovTableSchema)
			.where(
				eq(
					tmForceStatusLovTableSchema.Category,
					isTruckRoll ? 'HOME' : 'HOME_NO_TRUCKROLL'
				)
			);
		// Order exist in DB
		let lovDetails = undefined;
		const reportItemList: ReportItemList = [];
		// Orders that are not yet reached Siebel
		if (
			dbResult.OrderableTxnHistory.OrderStatus ===
				ProgressStatusEnum.COMPLETED ||
			dbResult.OrderableTxnHistory.OrderStatus === ProgressStatusEnum.CANCELLED
		) {
			const verticalStatus: string =
				dbResult.OrderableTxnHistory.OrderStatus ===
				ProgressStatusEnum.COMPLETED
					? 'ORDER-COMPLETED'
					: 'ORDER-CANCELED';
			lovDetails = lovList.find(lov => lov.Vertical === verticalStatus);
			if (lovDetails)
				reportItemList.push({
					Title: lovDetails.HorizontalValue,
					Description: lovDetails.Description,
					Timestamp: formatDate(
						dbResult.OrderableTxnHistory.UpdatedAt.toDateString(),
						dbDateTimePattern,
						dbDateTimeFormat
					)
				});
		}

		//? Recheck once db structure and data finalized
		const submittedStatus: string =
			dbResult.OrderableTxnHistory.OrderProgress?.find(
				order => order.Status === ProgressStatusEnum.INITIAL
			)
				? 'SUBMITTED-INITIAL'
				: 'SUBMITTED-PROCESSING';

		lovDetails = lovList.find(lov => lov.Vertical === submittedStatus);

		if (lovDetails)
			reportItemList.push({
				Title: lovDetails.HorizontalValue,
				Description: lovDetails.Description,
				Timestamp: formatDate(
					dbResult.OrderableTxnHistory.UpdatedAt.toDateString(),
					dbDateTimePattern,
					dbDateTimeFormat
				)
			});

		return reportItemList;
	}

	// Orders from Siebel
	private async getOrderProgress(
		req: OrderDetailsReq,
		orderNumber: string
	): Promise<{
		horizontalDescription: string;
		caseDetails: CaseDetails;
		reportItemList: ReportItemList;
		technicianDetails: TechnicianDetails;
	}> {
		let technicianDetails: TechnicianDetails = null;
		let caseDetails = {} as CaseDetails;
		let emptyProgressUpdate = true;
		let progressUpdate: Wso2OrderStatusProgress;
		let horizontalDescription = '';
		const reportItemList: ReportItemList = [];
		const setAppointmentIcpList: string[] = [];

		const orderProgress: Wso2TmForceProgressUpdateRes =
			await this.mwIntegration.Wso2RecordIntegration.getWso2TmForceOrderProgressUpdate(
				orderNumber
			);

		if (
			orderProgress.OrderStatusProgress.length > 0 &&
			orderProgress.OrderStatusProgress[0]
		) {
			for (let i = 0; i < orderProgress.OrderStatusProgress.length; i++) {
				progressUpdate = orderProgress.OrderStatusProgress[i];
				pinoLog.info(`PROGRESSUPDATE: ${JSON.stringify(progressUpdate)}`);

				if (
					req.IsTruckRoll &&
					req.ReferenceStatus.toUpperCase() !== 'OPEN' &&
					progressUpdate
				) {
					emptyProgressUpdate = false;

					/** special case : get latest cancelled appointment date in
					 * description sample : reason=Pending Cancel,
					 * Previous appointment on = 07/12/2022 16:00:00)
					 * */
					if (!caseDetails.DefferedAppointmentDate) {
						const cancelledAppointmentDate =
							this.orderTrackerHelper.getCancelledAppointmentDate(
								progressUpdate
							);

						if (cancelledAppointmentDate) {
							caseDetails.DefferedAppointmentDate =
								cancelledAppointmentDate.defferedAppointmentDate;
							caseDetails.DefferedAppointmentTime =
								cancelledAppointmentDate.defferedAppointmentTime;
						}
					}

					// case 1 & 2
					if (req.SystemName === 'NOVA') {
						// case 1: Application Received + Order Submitted (NOVA)
						const applicationReceived =
							await this.orderTrackerHelper.getNovaApplicationReceived(
								progressUpdate.NewStatus,
								progressUpdate.OldStatus,
								progressUpdate.LogDatetime
							);
						if (applicationReceived) {
							reportItemList.push(applicationReceived);

							const orderSubmitted =
								await this.orderTrackerHelper.getNovaOrderSubmitted(
									progressUpdate.NewStatus,
									progressUpdate.OldStatus,
									progressUpdate.LogDatetime
								);
							if (orderSubmitted) {
								reportItemList.push(orderSubmitted);
							}

							continue;
						}

						// case 2 : To Set Appointment (NOVA)
						const setAppointmentNova =
							await this.orderTrackerHelper.getNovaToSetAppointment(
								progressUpdate
							);
						if (setAppointmentNova) {
							reportItemList.push(setAppointmentNova);
							continue;
						}
					}

					if (req.SystemName === 'ICP') {
						// case 2 : To Set Appointment (ICP)
						const setAppointmentIcp =
							await this.orderTrackerHelper.getIcpToSetAppointment(
								progressUpdate,
								setAppointmentIcpList
							);
						if (setAppointmentIcp) {
							reportItemList.push(setAppointmentIcp);
							setAppointmentIcpList.push(progressUpdate.LogDatetime ?? '');
							continue;
						}
					}

					// case 3 : Installation Scheduled (appointment received)
					const installationScheduled =
						await this.orderTrackerHelper.getInstallationScheduled(
							progressUpdate,
							req.SystemName
						);

					if (installationScheduled) {
						reportItemList.push(installationScheduled.ReportItem);
						if (!caseDetails.AppointmentDate) {
							caseDetails.AppointmentDate =
								installationScheduled.AppointmentDate;
							caseDetails.AppointmentTime =
								installationScheduled.AppointmentTime;
						}
						continue;
					}

					// case 4: Unifi Care Crew Assigned & Reassigned
					const unifiCareCrewAssigned =
						await this.orderTrackerHelper.getUnifiCareCrewAssigned(
							progressUpdate
						);
					if (unifiCareCrewAssigned) {
						reportItemList.push(unifiCareCrewAssigned);
						continue;
					}

					const unifiCareCrewReassigned =
						await this.orderTrackerHelper.getUnifiCareCrewReassigned(
							progressUpdate
						);
					if (unifiCareCrewReassigned) {
						reportItemList.push(unifiCareCrewReassigned);
						continue;
					}

					// case 5: On My Way (OMW) || Unifi Care Crew On the Way || Unifi Care Crew Arrival Update
					const onMyWay = await this.orderTrackerHelper.getOnMyWay(
						i,
						reportItemList.length,
						orderProgress,
						progressUpdate
					);
					if (onMyWay) {
						reportItemList.push(onMyWay);
						continue;
					}

					// case 6: On Site
					const onSite = await this.orderTrackerHelper.getOnSite(
						progressUpdate.NewStatus,
						progressUpdate.OldStatus,
						progressUpdate.LogDatetime
					);
					if (onSite) {
						reportItemList.push(onSite);
						continue;
					}

					// case 7: Postpone
					const postpone = await this.orderTrackerHelper.getPostpone(
						req.SystemName,
						caseDetails,
						progressUpdate
					);
					if (postpone) {
						reportItemList.push(postpone);
						continue;
					}

					// case 7.2: Installation Update (technical pre-assigned appointment)
					const technicalPreAssignedAppointment =
						await this.orderTrackerHelper.getTechnicalPreAssignedAppointment(
							progressUpdate
						);
					if (technicalPreAssignedAppointment) {
						reportItemList.push(technicalPreAssignedAppointment.ReportItem);
						if (!caseDetails.AppointmentDate) {
							caseDetails.AppointmentDate =
								technicalPreAssignedAppointment.AppointmentDate;
							caseDetails.AppointmentTime =
								technicalPreAssignedAppointment.AppointmentTime;
						}
						continue;
					}

					// case 8: Activated (complete & activation)
					const activated =
						await this.orderTrackerHelper.getActivated(progressUpdate);
					if (activated) {
						reportItemList.push(activated);
						continue;
					}

					// case 9: Cancelled
					const matchedCancelledStatus = reportItemList.find(
						reportItem => 'Cancelled' === reportItem?.Title
					);
					if (!matchedCancelledStatus) {
						const cancelled =
							await this.orderTrackerHelper.getCancelled(progressUpdate);

						if (cancelled) {
							reportItemList.push(cancelled);
						}
					}

					/** special case: Cancelled
					 * cancelled nova order during pending processing
					 * discovered during testing nova 20/12/2023
					 * */
					if (
						req.ReferenceStatus.toUpperCase() === 'CANCELLED' &&
						((reportItemList.length === 1 &&
							reportItemList[0].Title !== 'Application Cancelled') ||
							reportItemList.length === 0)
					) {
						const cancelledOrderDuringPendingProcessing =
							await this.orderTrackerHelper.getCancelledOrderDuringPendingProcessing();
						if (cancelledOrderDuringPendingProcessing) {
							reportItemList.splice(
								0,
								1,
								...[cancelledOrderDuringPendingProcessing]
							);
						}
					}
				}
			}

			// ICP has no order status progress for application received and order submitted
			// thus, by default we will create the report item for these 2 statuses
			// if the order creation date is less than today
			if (req.SystemName === 'ICP') {
				// case 1: Application Received + Order Submitted (ICP)
				const orderCreationDate =
					await this.orderTrackerHelper.getIcpOrderCreationDate(
						this.idTokenInfo.IdType,
						this.idTokenInfo.IdValue,
						orderNumber
					);
				pinoLog.info(`ICP ORDER CREATION DATE: ${orderCreationDate}`);
				if (orderCreationDate && orderCreationDate <= getMyTimeZoneDate()) {
					pinoLog.info(
						'START Case 1: Application Received + Order Submitted (ICP)'
					);

					const icpApplicationReceived =
						await this.orderTrackerHelper.getIcpApplicationReceived(
							format(orderCreationDate, 'dd/MM/yyyy HH:mm:ss')
						);
					if (icpApplicationReceived) {
						reportItemList.push(icpApplicationReceived);
					}

					const icpOrderSubmitted =
						await this.orderTrackerHelper.getIcpOrderSubmitted(
							format(orderCreationDate, 'dd/MM/yyyy HH:mm:ss')
						);
					if (icpOrderSubmitted) {
						reportItemList.push(icpOrderSubmitted);
					}

					pinoLog.info(
						'END Case 1: Application Received + Order Submitted (ICP)'
					);
				}
			}

			// Retrieve the latest planned start and end date
			caseDetails = await this.orderTrackerHelper.setCaseDetails(
				orderProgress.OrderStatusProgress,
				caseDetails,
				reportItemList[0]
			);

			// Replace {appointment-date} and {appointment-time} with actual date and time for cases that did not have case details info yet during the earlier loop
			for (const report of reportItemList) {
				if (
					report.Description.includes('{appointment-date}') ||
					report.Description.includes('{appointment-time}')
				) {
					if (caseDetails.AppointmentDate && caseDetails.AppointmentTime) {
						pinoLog.info('APPOINTMENTDATE & APPOINTMENTTIME EXIST');
						report.Description = report.Description.replace(
							'{appointment-date}',
							caseDetails.AppointmentDate
						).replace('{appointment-time}', caseDetails.AppointmentTime);
					} else if (
						caseDetails.DefferedAppointmentDate &&
						caseDetails.DefferedAppointmentTime
					) {
						pinoLog.info('DEFFERED EXIST');
						report.Description = report.Description.replace(
							'{appointment-date}',
							caseDetails.DefferedAppointmentDate
						).replace(
							'{appointment-time}',
							caseDetails.DefferedAppointmentTime
						);
					}
				}
			}

			// Technician Details
			const technicianReport =
				await this.orderTrackerHelper.getTechnicianReport(
					orderNumber,
					reportItemList
				);

			if (technicianReport) {
				technicianDetails = technicianReport.technicianDetails;
				caseDetails.AppointmentDate = technicianReport.appointmentDate;
				caseDetails.AppointmentTime = technicianReport.appointmentTime;
			}
		}

		if (emptyProgressUpdate) {
			const progressBySiebelOrderStatus =
				await this.orderTrackerHelper.getProgressBySiebelOrderStatus(
					req.ReferenceStatus.toUpperCase(),
					req.IsTruckRoll
				);
			if (progressBySiebelOrderStatus) {
				reportItemList.push(progressBySiebelOrderStatus);
				horizontalDescription = progressBySiebelOrderStatus.Title;
			}
		}

		if (reportItemList.length > 0 && !emptyProgressUpdate && req.IsTruckRoll) {
			horizontalDescription =
				await this.orderTrackerHelper.getHorizontalDescription(
					reportItemList[0].Title
				);
		}

		return {
			horizontalDescription,
			caseDetails,
			reportItemList,
			technicianDetails
		};
	}

	async updateAppointmentSlot(
		source: string,
		req: AppointmentUpdateReq
	): Promise<BaseResponse> {
		const getWso2RecordIntegration = this.mwIntegration.Wso2RecordIntegration;
		switch (req.Action) {
			case AppointmentActionEnum.ENTRY: {
				await getWso2RecordIntegration.getWso2UpdateCustomerResponse({
					OrderId: req.OrderNumber
				});

				await this.updateAppointmentTxnHistory(req, 'Success', source, null);

				return {
					Success: true,
					Code: StatusCodeEnum.CREATED,
					IntegrationId: this.integrationId,
					Message: 'Customer entry acknowledged successfully.'
				};
			}
			case AppointmentActionEnum.ACK: {
				const wso2Req: Wso2AcknowledgeAppointmentReq = {
					OrderId: req.OrderId,
					SystemName: req.SystemName,
					AppointmentAcknowledge: 'Y'
				};

				await getWso2RecordIntegration.getWso2AcknowledgeAppointment(wso2Req);

				await this.updateAppointmentTxnHistory(req, 'Success', source, null);

				break;
			}
			case AppointmentActionEnum.SET: {
				if (!req.UpdateAppointmentInfo) {
					throw new UE_ERROR(
						'UpdateAppointmentInfo is required for SET action',
						StatusCodeEnum.BAD_REQUEST_ERROR
					);
				}
				const wso2Req: Wso2UpdateAppointmentReq = {
					OrderId: req.OrderNumber,
					SystemName: req.SystemName,
					Action: req.Action,
					Info: {
						NewAppointmentId: req.UpdateAppointmentInfo?.NewAppointmentId,
						NewSlotStart: req.UpdateAppointmentInfo?.NewSlotStart,
						NewSlotEnd: req.UpdateAppointmentInfo?.NewSlotEnd,
						ChangeCounter: '0',
						ChangeReason: '',
						ChangeRemark: '',
						CancelCounter: '0',
						CancelReason: '',
						CancelRemark: ''
					}
				};

				const wso2Res: Wso2UpdateAppointmentRes =
					await getWso2RecordIntegration.getWso2UpdateAppointment(wso2Req);

				const status: string =
					wso2Res.Response.SetInfo.ErrorCode === '0' ? 'Success' : 'Failed';

				const errorMessage: string | null =
					wso2Res.Response.SWIFTInfo.ErrorMessage ?? null;

				await this.updateAppointmentTxnHistory(
					req,
					status,
					source,
					errorMessage
				);

				if (status === 'Failed') {
					throw new UE_ERROR(
						'Set Appointment Failed',
						StatusCodeEnum.WSO2_ERROR,
						{
							integrationId: this.integrationId
						}
					);
				}

				break;
			}
			case AppointmentActionEnum.CHANGE: {
				if (!req.UpdateAppointmentInfo) {
					throw new UE_ERROR(
						'UpdateAppointmentInfo is required for CHANGE action',
						StatusCodeEnum.BAD_REQUEST_ERROR
					);
				}

				const appointmentCounter: SelectAppointmentCounter[] = await this.db
					.select()
					.from(appointmentCounterTableSchema)
					.where(eq(appointmentCounterTableSchema.OrderId, req.OrderId))
					.execute();

				const changeCounter: number =
					appointmentCounter.length === 0
						? 0
						: appointmentCounter[0].ChangeCounter;

				if (changeCounter >= 3) {
					throw new UE_ERROR(
						'Change appointment limit reached.',
						StatusCodeEnum.LIMIT_EXCEEDED_ERROR
					);
				}

				const wso2Req: Wso2UpdateAppointmentReq = {
					OrderId: req.OrderNumber,
					SystemName: req.SystemName,
					Action: req.Action,
					Info: {
						NewAppointmentId: req.UpdateAppointmentInfo?.NewAppointmentId,
						NewSlotStart: req.UpdateAppointmentInfo?.NewSlotStart,
						NewSlotEnd: req.UpdateAppointmentInfo?.NewSlotEnd,
						ChangeCounter: '3',
						ChangeReason: req.UpdateAppointmentInfo?.Reason,
						ChangeRemark: req.UpdateAppointmentInfo.Remark,
						CancelCounter: '0',
						CancelReason: '',
						CancelRemark: ''
					}
				};

				const wso2Res: Wso2UpdateAppointmentRes =
					await getWso2RecordIntegration.getWso2UpdateAppointment(wso2Req);

				const status: string =
					wso2Res.Response.SetInfo.ErrorCode === '0' &&
					wso2Res.Response.CancelInfo.ErrorCode === '0' &&
					wso2Res.Response.SWIFTInfo.ErrorCode === '0'
						? 'Success'
						: 'Failed';

				const errorMessage: string | null =
					wso2Res.Response.SWIFTInfo.ErrorMessage ?? null;

				await this.updateAppointmentTxnHistory(
					req,
					status,
					source,
					errorMessage
				);

				if (status === 'Failed') {
					throw new UE_ERROR(
						'Change Appointment Failed',
						StatusCodeEnum.WSO2_ERROR,
						{
							integrationId: this.integrationId
						}
					);
				}

				break;
			}
			case AppointmentActionEnum.CANCEL: {
				if (!req.UpdateAppointmentInfo) {
					throw new UE_ERROR(
						'UpdateAppointmentInfo is required for CANCEL action',
						StatusCodeEnum.BAD_REQUEST_ERROR
					);
				}

				const appointmentCounter: SelectAppointmentCounter[] = await this.db
					.select()
					.from(appointmentCounterTableSchema)
					.where(eq(appointmentCounterTableSchema.OrderId, req.OrderId))
					.execute();

				const cancelCounter: number =
					appointmentCounter.length === 0
						? 0
						: appointmentCounter[0].CancelCounter;

				if (cancelCounter >= 3) {
					throw new UE_ERROR(
						'Cancel appointment limit reached.',
						StatusCodeEnum.LIMIT_EXCEEDED_ERROR
					);
				}

				const wso2Req: Wso2UpdateAppointmentReq = {
					OrderId: req.OrderNumber,
					SystemName: req.SystemName,
					Action: req.Action,
					Info: {
						NewAppointmentId: req.UpdateAppointmentInfo?.NewAppointmentId,
						NewSlotStart: req.UpdateAppointmentInfo?.NewSlotStart,
						NewSlotEnd: req.UpdateAppointmentInfo?.NewSlotEnd,
						ChangeCounter: '0',
						ChangeReason: '',
						ChangeRemark: '',
						CancelCounter: '1',
						CancelReason: req.UpdateAppointmentInfo?.Reason,
						CancelRemark: req.UpdateAppointmentInfo?.Remark
					}
				};

				const wso2Res: Wso2UpdateAppointmentRes =
					await getWso2RecordIntegration.getWso2UpdateAppointment(wso2Req);

				const status: string =
					wso2Res.Response.CancelInfo.ErrorCode === '0' &&
					wso2Res.Response.SWIFTInfo.ErrorCode === '0'
						? 'Success'
						: 'Failed';

				const errorMessage: string | null =
					wso2Res.Response.SWIFTInfo.ErrorMessage ?? null;

				await this.updateAppointmentTxnHistory(
					req,
					status,
					source,
					errorMessage
				);

				if (status === 'Failed') {
					throw new UE_ERROR(
						'Cancel Appointment Failed',
						StatusCodeEnum.WSO2_ERROR,
						{ integrationId: this.integrationId }
					);
				}

				break;
			}
			case AppointmentActionEnum.POSTPONE: {
				const wso2Req: Wso2TmForcePostponeAppointmentReq = {
					OrderNumber: req.OrderNumber,
					ConfirmPostpone: 'Y'
				};

				const wso2Res: Wso2TmForcePostponeAppointmentRes =
					await getWso2RecordIntegration.getWso2TmForcePostponeAppointment(
						wso2Req
					);

				const status: string =
					wso2Res.ReplyHeader?.ErrCd === '0' ? 'Success' : 'Failed';

				const errorMessage: string | null = wso2Res.ReplyHeader?.ErrMsg ?? null;

				await this.updateAppointmentTxnHistory(
					req,
					status,
					source,
					errorMessage
				);

				if (status === 'Failed') {
					throw new UE_ERROR(
						'TM Force Postpone Appointment Failed',
						StatusCodeEnum.WSO2_ERROR,
						{ integrationId: this.integrationId }
					);
				}

				break;
			}
		}

		return {
			Success: true,
			Code: StatusCodeEnum.CREATED,
			IntegrationId: this.integrationId,
			Message: 'Appointment updated successfully.'
		};
	}

	private async updateAppointmentTxnHistory(
		req: AppointmentUpdateReq,
		status: string,
		source: string,
		errorMessage: string | null
	) {
		await this.db
			.insert(appointmentTxnHistoryTableSchema)
			.values({
				OrderId: req.OrderId,
				Action: req.Action,
				ReserveId: req.UpdateAppointmentInfo?.NewAppointmentId,
				Reason: req.UpdateAppointmentInfo?.Reason ?? '',
				Source: source,
				CurrentApptDate: req.CurrentAppointmentDate,
				UpdatedApptDate: req.UpdateAppointmentInfo?.NewSlotStart,
				Status: status
			})
			.returning();

		const appointmentCounter: SelectAppointmentCounter[] = await this.db
			.select()
			.from(appointmentCounterTableSchema)
			.where(eq(appointmentCounterTableSchema.OrderId, req.OrderId))
			.execute();

		if (appointmentCounter.length === 0) {
			await this.db
				.insert(appointmentCounterTableSchema)
				.values({
					OrderId: req.OrderId,
					ChangeCounter:
						req.Action === AppointmentActionEnum.CHANGE && errorMessage === null
							? 1
							: 0,
					CancelCounter:
						req.Action === AppointmentActionEnum.CANCEL && errorMessage === null
							? 1
							: 0,
					ErrorMessage: errorMessage
				})
				.returning();

			return;
		}

		const changeCounter: number =
			req.Action === AppointmentActionEnum.CHANGE && errorMessage === null
				? appointmentCounter[0].ChangeCounter + 1
				: appointmentCounter[0].ChangeCounter;
		const cancelCounter: number =
			req.Action === AppointmentActionEnum.CANCEL && errorMessage === null
				? appointmentCounter[0].CancelCounter + 1
				: appointmentCounter[0].CancelCounter;

		await this.db
			.update(appointmentCounterTableSchema)
			.set({
				ChangeCounter: changeCounter,
				CancelCounter: cancelCounter,
				ErrorMessage: errorMessage,
				UpdatedAt: sql`now()`
			})
			.where(eq(appointmentCounterTableSchema.OrderId, req.OrderId))
			.returning();
	}

	async validateOrderId(orderId: string): Promise<ValidateOrderIdRes> {
		const wso2Req: Wso2OrderTrackingReq = {
			OrderTracking: {
				SystemName: 'GOER',
				Orderdetails: {
					OrderNo: orderId,
					IdType: this.idTokenInfo.IdType,
					CustomerID: this.idTokenInfo.IdValue
				}
			}
		};

		const wso2Res: Wso2OrderTrackingRes =
			await this.mwIntegration.Wso2RecordIntegration.getWso2OrderTracking(
				wso2Req
			);

		const orderDetail =
			wso2Res.Response.OrderDetails?.ViewOrderDetailResponse?.find(
				order => order.SBLOrderID === orderId
			);

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				SystemName: orderDetail?.System ?? '',
				OrderStatus: orderDetail?.OrderStatus ?? '',
				InstallationActStat: orderDetail?.InstallationActStat ?? '',
				InstallationActTime: orderDetail?.InstallationActTime ?? '',
				SBLOrderID: orderDetail?.SBLOrderID ?? '',
				OrderNumber: orderDetail?.OrderNumber ?? '',
				OrderType: orderDetail?.OrderType ?? '',
				OrderPlanName: orderDetail?.OrderPlanName ?? '',
				ServiceID: orderDetail?.ServiceID ?? '',
				CreatedDate: orderDetail?.CreatedDate ?? ''
			}
		};
	}

	async getTmForceAcceptanceForm(
		orderNumber: string
	): Promise<Wso2TmForceAcceptanceFormRes> {
		return await this.mwIntegration.Wso2RecordIntegration.getWso2TmForceAcceptanceForm(
			orderNumber
		);
	}

	async getAppointmentSlotsByRegion(
		req: AppointmentSlotReq
	): Promise<AppointmentSlotRes> {
		// get earliest and latest start date
		const { earliestStartDate, latestStartDate } =
			this.getEarliestAndLatestStartDate();

		// request appointment slot based on region from WSO2
		const wso2Req: Wso2GetAppointmentSlotReq = {
			SystemName: req.SystemName,
			OrderId: req.OrderNumber,
			RNORegion: req.RNORegion,
			EarliestStartDate: earliestStartDate,
			LatestStartDate: latestStartDate
		};

		const wso2Res: Wso2GetAppointmentSlotRes =
			await this.mwIntegration.Wso2RecordIntegration.getWso2RetrieveAppointmentSlot(
				wso2Req
			);

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				AppointmentSlots: wso2Res.Response.AppointmentSlot
			}
		};
	}

	private getEarliestAndLatestStartDate(): {
		earliestStartDate: string;
		latestStartDate: string;
	} {
		const timeFormat: string = 'HH:mm:ss';
		const dateFormat: string = 'MM/dd/yyyy';
		const currentDate: Date = getMyTimeZoneDate();

		const tenAM: Date = parse('10:00:00', timeFormat, currentDate);
		const two30PM: Date = parse('14:30:00', timeFormat, currentDate);

		const timeNow: string = format(currentDate, timeFormat);
		const timeNowDate: Date = parse(timeNow, timeFormat, currentDate);
		const todayDateInString: string = format(currentDate, dateFormat);
		let earliestStartDate: string;

		if (isBefore(timeNowDate, tenAM) || isEqual(timeNowDate, tenAM)) {
			pinoLog.debug('### now time is < 10:01 AM ###');
			earliestStartDate = `${todayDateInString} 14:00:00`;
		} else if (isAfter(timeNowDate, tenAM) && isBefore(timeNowDate, two30PM)) {
			pinoLog.debug('### now time is 10:01 AM - 12:00 PM ###');
			const tomorrowDate: string = format(
				new Date(currentDate).setDate(currentDate.getDate() + 1),
				dateFormat
			);
			earliestStartDate = `${tomorrowDate} 12:00:00`;
		} else {
			pinoLog.debug('### now time is > 2:30 PM ###');
			const tomorrowDate: string = format(
				new Date(currentDate).setDate(currentDate.getDate() + 1),
				dateFormat
			);
			earliestStartDate = `${tomorrowDate} 14:00:00`;
		}

		const latestStartDate: string = format(
			new Date(currentDate).setDate(currentDate.getDate() + 28),
			`${dateFormat} ${timeFormat}`
		);

		pinoLog.debug(`Earliest Start Date ${earliestStartDate}`);
		pinoLog.debug(`Latest Start Date ${latestStartDate}`);

		return {
			earliestStartDate,
			latestStartDate
		};
	}
}

export default OrderTracker;
