import { format } from 'date-fns';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import {
	LightweightFlagEnum,
	SystemNameEnum
} from '../../../../enum/wso2.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	Wso2OrderReviewReq,
	Wso2OrderReviewRes
} from '../../../../integration/wso2/order/schemas/api/wso2OrderReview.schema';
import type { Wso2ServiceAccountRes } from '../../../../integration/wso2/user/schemas/api/wso2ServiceAccount.schema';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import { getAddOnsOrderId, getMyTimeZoneDate } from '../../../../shared/common';
import { decrypt } from '../../../../shared/encryption/aesGcm';
import type {
	AddOnsAppointmentSlotReq,
	AddOnsAppointmentSlotRes
} from '../schemas/api/addOns.schema';

class AddOns {
	private integrationId: string;
	private mwIntegration: MwIntegration;
	private idTokenInfo: IdTokenInfo;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo) {
		this.integrationId = integrationId;
		this.mwIntegration = new MwIntegration(integrationId);
		this.idTokenInfo = idTokenInfo;
	}

	// for mesh wifi 6
	async getAppointmentSlots(
		req: AddOnsAppointmentSlotReq,
		segment: string
	): Promise<AddOnsAppointmentSlotRes> {
		const decryptedBillAccNo = await decrypt(req.EncryptedBillAccNo);
		const { earliestStartDate, latestStartDate } =
			this.getEarliestAndLatestStartDate();

		let tmExchangeName = '';
		let tmPremiseType = '';
		let tmDPLocation = '';
		let tmAccessTechnology = '';
		let servicePointId = '';

		const wso2NonLightweightServiceAccountRes: Wso2ServiceAccountRes =
			await this.mwIntegration.Wso2UserIntegration.getWso2ServiceAccount(
				{
					idType: this.idTokenInfo.IdType,
					idValue: this.idTokenInfo.IdValue,
					SystemName: SystemNameEnum.NOVA,
					BillingAccountNo: decryptedBillAccNo
				},
				LightweightFlagEnum.NO
			);

		if (
			wso2NonLightweightServiceAccountRes?.Response?.ServiceAccount &&
			wso2NonLightweightServiceAccountRes?.Response.ServiceAccount.length > 0
		) {
			for (const sa of wso2NonLightweightServiceAccountRes.Response
				.ServiceAccount ?? []) {
				for (const moli of sa.ServiceAccountMoli ?? []) {
					servicePointId = sa.ServicePointId ?? '';
					tmExchangeName =
						moli?.['TmCutAssetMgmt-ServiceMeterIntegration']?.TMExchangeName ??
						'';
					tmPremiseType =
						moli?.['TmCutAssetMgmt-ServiceMeterIntegration']?.TMPremiseType ??
						'';
					tmDPLocation =
						moli?.['TmCutAssetMgmt-ServiceMeterIntegration']?.TMDPLocation ??
						'';
					tmAccessTechnology =
						moli?.['TmCutAssetMgmt-ServiceMeterIntegration']
							?.TMAccessTechnology ?? '';
					break;
				}
			}
		}

		const orderId: string = await getAddOnsOrderId('MW6');

		const wso2Req: Wso2OrderReviewReq = {
			OrderReviewRequest: {
				AccountId: '',
				OrderType: 'Modify',
				SegmentGroup: segment,
				TransactionId: orderId,
				OrderId: orderId,
				QueryAppointmentSlotsFlag: 'Y',
				ReservationId: '',
				SystemName: SystemNameEnum.NOVA,
				ExchangeName: tmExchangeName,
				CustomerAccount: {
					AccountId: req.AccountNo,
					ContactId: req.ContactId
				},
				QueryAppointmentSlots: {
					ServicePointID: servicePointId,
					TechnologyType: tmAccessTechnology,
					DPLocation: tmDPLocation,
					PremiseType: tmPremiseType,
					EarliestStartDate: earliestStartDate,
					LatestStartDate: latestStartDate,
					AppointmentProducts: {
						AppointmentProduct: [
							{
								Name: req.DeviceName,
								PartNumber: req.PartNumber,
								Action: 'add'
							}
						]
					}
				}
			}
		};

		const wso2Res: Wso2OrderReviewRes =
			await this.mwIntegration.Wso2OrderIntegration.getWso2OrderReview(wso2Req);

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				OrderId: orderId,
				RNORegion:
					wso2Res.Response.OrderReviewResponse.GetAppointmentSlots.RNORegion,
				AppointmentSlots:
					wso2Res.Response.OrderReviewResponse.GetAppointmentSlots.AppointmentSlot.map(
						slot => {
							return {
								AppointmentId: slot.AppointmentId,
								SlotStart: slot.SlotStart,
								SlotEnd: slot.SlotEnd
							};
						}
					)
			}
		};
	}

	private getEarliestAndLatestStartDate(): {
		earliestStartDate: string;
		latestStartDate: string;
	} {
		const dateFormat: string = 'MM/dd/yyyy HH:mm:ss';
		const currentDate: Date = getMyTimeZoneDate();

		const earliestStartDate: string = format(
			new Date(currentDate).setDate(currentDate.getDate() + 1),
			dateFormat
		);

		const latestStartDate: string = format(
			new Date(currentDate).setDate(currentDate.getDate() + 14),
			dateFormat
		);

		return {
			earliestStartDate,
			latestStartDate
		};
	}
}

export default AddOns;
