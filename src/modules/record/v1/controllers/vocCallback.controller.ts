import { randomUUID } from 'node:crypto';
import { Elysia } from 'elysia';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type VocUserDetailsRes,
	vocUserDetailsReqschema,
	vocUserDetailsResSchema
} from '../schemas/api/survey.schema';
import VocCallback from '../services/vocCallback.service';

export const vocCallbackV1Routes = new Elysia({ prefix: '/callback/survey' })
	.resolve(() => {
		return {
			VocCallback: new VocCallback(randomUUID())
		};
	})
	.post(
		'/voc',
		async (ctx): Promise<VocUserDetailsRes> => {
			return await ctx.VocCallback.getVocTnpsUserDetails(ctx.body);
		},
		{
			detail: {
				description:
					'Retrieve the customer details. This API is applicable for UNIFI-APP user only. Qualtrix will trigger this API to get the customer details after the Voice of Customer (VOC) survey is submitted. <br><br> <b>Backend System: </b> Qualtrix <br> <b>Table:</b> qualtrix_tnps_user_details',
				tags: ['Record', 'Callback']
			},
			body: vocUserDetailsReqschema,
			response: {
				200: vocUserDetailsResSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export default vocCallbackV1Routes;
