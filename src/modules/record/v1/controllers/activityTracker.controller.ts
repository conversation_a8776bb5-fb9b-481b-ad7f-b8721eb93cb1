import { randomUUID } from 'node:crypto';
import { Elysia } from 'elysia';
import { baseHeaderSchema } from '../../../../shared/schemas/api/headers.schema';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type ActivityDetailsRes,
	type ActivityListRes,
	activityDetailsReqSchema,
	activityDetailsResSchema,
	activityListReqSchema,
	activityListResSchema
} from '../schemas/api/activityTracker.schema';
import ActivityTracker from '../services/activityTracker.service';

const activityTrackerV1Routes = new Elysia({ prefix: '/activity-tracker' })
	.resolve(() => {
		return {
			ActivityTracker: new ActivityTracker(randomUUID())
		};
	})
	.get(
		'/history',
		async (ctx): Promise<ActivityListRes> => {
			return await ctx.ActivityTracker.getActivityList(
				ctx.query.AccountNo,
				ctx.query.SystemName
			);
		},
		{
			detail: {
				description:
					'Get user activity list for the past 90 days. <br><br> <b>Backend System: </b> NOVA SIEBEL, ICP, TMFORCE',
				tags: ['Record']
			},
			query: activityListReqSchema,
			response: {
				200: activityListResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.get(
		'/history/details',
		async (ctx): Promise<ActivityDetailsRes> => {
			return await ctx.ActivityTracker.getActivityDetails(ctx.query);
		},
		{
			headers: baseHeaderSchema,
			detail: {
				description:
					'Retrieve activity details based on ticket number. <br><br> <b>Backend System: </b> NOVA SIEBEL, ICP, TMFORCE <br> <b>Table: </b> tm_force_status_lov',
				tags: ['Record']
			},
			query: activityDetailsReqSchema,
			response: {
				200: activityDetailsResSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export default activityTrackerV1Routes;
