import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import { Elysia, t } from 'elysia';
import { SourceEnum } from '../../../../enum/header.enum';
import { getIdTokenInfo } from '../../../../middleware/uaid/util/utils';
import {
	type BaseResponse,
	baseResponseSchema,
	errorBaseResponseSchema
} from '../../../../shared/schemas/api/responses.schema';
import { submitNesSurveyReqSchema } from '../schemas/api/survey.schema';
import SurveyRecord from '../services/survey.service';

export const surveyV1Routes = new Elysia({ prefix: '/survey' })
	.use(bearer())
	.resolve(async ctx => {
		let idTokenInfo = null;
		if (ctx.bearer) {
			idTokenInfo = await getIdTokenInfo(ctx.bearer);
		}
		return {
			SurveyRecord: new SurveyRecord(randomUUID(), idTokenInfo)
		};
	})
	.post(
		'/nes',
		async (ctx): Promise<BaseResponse> => {
			return await ctx.SurveyRecord.submitNesSurvey(
				ctx.body,
				ctx.headers.source,
				ctx.headers.segment
			);
		},
		{
			detail: {
				description:
					'Submit NET Easy Score (NES) survey from Unifi App & Portal. For Post Login, please add the bearer token into the header. <br><br> <b>Backend System: </b> Unknown <br> <b>Table:</b> nes_survey',
				tags: ['Record']
			},
			headers: t.Object({
				'x-api-key': t.String(),
				source: t.Enum(SourceEnum),
				segment: t.String()
			}),
			body: submitNesSurveyReqSchema,
			response: {
				200: baseResponseSchema,
				500: errorBaseResponseSchema
			}
		}
	);
