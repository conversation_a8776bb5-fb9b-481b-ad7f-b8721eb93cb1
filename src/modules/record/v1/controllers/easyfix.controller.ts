import { randomUUID } from 'node:crypto';
import { <PERSON><PERSON> } from 'elysia';
import { wso2CreateSRICPSchema } from '../../../../integration/wso2/record/schemas/api/wso2CreateSRIcp.schema';
import { wso2CreateSRNovaReqSchema } from '../../../../integration/wso2/record/schemas/api/wso2CreateSRNova.schema';
import { wso2EasyfixTnpsSurveyReqSchema } from '../../../../integration/wso2/record/schemas/api/wso2EasyfixTnpsSurvey.schema';
import { wso2NovaCTTSchema } from '../../../../integration/wso2/record/schemas/api/wso2NovaCTT.schema';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type CreateSRICPRes,
	type CreateSRNovaRes,
	type EasyFixActivityListRes,
	type EasyFixCreateNovaTTRes,
	type EasyFixGetNetworkTTRes,
	type SubmitTnpsSurveyRes,
	createSRICPResSchema,
	createSRNovaResSchema,
	easyFixActivityListReqSchema,
	easyFixActivityListResSchema,
	easyfixCreateNovaTTResSchema,
	easyfixGetNetworkTTReqSchema,
	easyfixGetNetworkTTResSchema,
	submitTnpsSurveyResSchema
} from '../schemas/api/easyfix.schema';
import Easyfix from '../services/easyfix.service';

const easyfixV1Routes = new Elysia({ prefix: '/easyfix' })
	.resolve(() => {
		return {
			Easyfix: new Easyfix(randomUUID())
		};
	})
	.get(
		'/activities',
		async (ctx): Promise<EasyFixActivityListRes> => {
			return await ctx.Easyfix.getActivityList(ctx.query);
		},
		{
			detail: {
				description:
					'Get customer activity list by start and end date defined in query for easyfix usage. <br><br> <b>Backend System: </b> NOVA SIEBEL, ICP, TMFORCE',
				tags: ['Record']
			},
			query: easyFixActivityListReqSchema,
			response: {
				200: easyFixActivityListResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.post(
		'/nova/trouble-ticket',
		async (ctx): Promise<EasyFixCreateNovaTTRes> => {
			return await ctx.Easyfix.createNovaTT(ctx.body);
		},
		{
			detail: {
				description:
					'Create a trouble ticket in Nova through WSO2. <br><br> <b>Backend Systems:</b> NOVA',
				tags: ['Record']
			},
			body: wso2NovaCTTSchema,
			response: {
				200: easyfixCreateNovaTTResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.get(
		'/nova/network-trouble-ticket',
		async (ctx): Promise<EasyFixGetNetworkTTRes> => {
			return await ctx.Easyfix.getNetworkTT(
				ctx.query.RequestID,
				ctx.query.ServiceNo,
				ctx.query.ServiceType
			);
		},
		{
			detail: {
				description:
					'Retrieve Network Trouble Ticket from Nova through WSO2.<br><br><b>Backend Systems:</b> NOVA',
				tags: ['Record']
			},
			query: easyfixGetNetworkTTReqSchema,
			response: {
				200: easyfixGetNetworkTTResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.post(
		'/sr/nova',
		async (ctx): Promise<CreateSRNovaRes> => {
			const res = await ctx.Easyfix.createNovaSR(ctx.body);
			ctx.set.status = res.Code;
			return res;
		},
		{
			detail: {
				description:
					'Send a service request to NOVA via WSO2. This endpoint is used for SR creation under EasyFix. <br><br> <b>Backend:</b> NOVA SIEBEL, ICP, TMFORCE',
				tags: ['Record']
			},
			body: wso2CreateSRNovaReqSchema,
			response: {
				201: createSRNovaResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.post(
		'/sr/icp',
		async (ctx): Promise<CreateSRICPRes> => {
			const res = await ctx.Easyfix.createICPSR(ctx.body);
			ctx.set.status = res.Code;
			return res;
		},
		{
			detail: {
				description:
					'Send a service request to ICP via WSO2. <br><br> <b>Backend:</b> NOVA SIEBEL, ICP',
				tags: ['Record']
			},
			body: wso2CreateSRICPSchema,
			response: {
				201: createSRICPResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.post(
		'/survey/tnps',
		async (ctx): Promise<SubmitTnpsSurveyRes> => {
			const res = await ctx.Easyfix.submitTnpsSurvey(ctx.body);
			ctx.set.status = res.Code;
			return res;
		},
		{
			detail: {
				description:
					'Submit TNPS survey from Easyfix. <br><br> <b>Backend System: </b> Unknown',
				tags: ['Record']
			},
			body: wso2EasyfixTnpsSurveyReqSchema,
			response: {
				201: submitTnpsSurveyResSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export default easyfixV1Routes;
