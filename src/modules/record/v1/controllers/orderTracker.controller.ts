import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import { Elysia, t } from 'elysia';
import { getIdTokenInfo } from '../../../../middleware/uaid/util/utils';
import {
	type BaseResponse,
	baseResponseSchema,
	errorBaseResponseSchema
} from '../../../../shared/schemas/api/responses.schema';
import {
	type AppointmentSlotRes,
	type OrderDetailsRes,
	type OrderListRes,
	type ValidateOrderIdRes,
	appointmentSlotReqSchema,
	appointmentSlotResSchema,
	appointmentUpdateReqSchema,
	orderDetailsReqSchema,
	orderDetailsResSchema,
	orderListResSchema,
	tmForceAcceptanceFormReqSchema,
	validateOrderIdReqSchema,
	validateOrderIdResSchema
} from '../schemas/api/orderTracker.schema';
import { default as OrderTracker } from '../services/orderTracker.service';

const orderTrackerV1Routes = new Elysia({ prefix: '/order-tracker' })
	.use(bearer())
	.resolve(async ctx => {
		const idTokenInfo = await getIdTokenInfo(ctx.bearer);
		return {
			OrderTracker: new OrderTracker(randomUUID(), idTokenInfo)
		};
	})
	.get(
		'/orderable/validate',
		async (ctx): Promise<ValidateOrderIdRes> => {
			return await ctx.OrderTracker.validateOrderId(ctx.query.OrderId);
		},
		{
			detail: {
				description:
					'Validate order id to check if it is existing. This API is for mini order tracker. <br><br> <b>Backend System: </b> NOVA SIEBEL',
				tags: ['Record']
			},
			query: validateOrderIdReqSchema,
			response: {
				200: validateOrderIdResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.get(
		'/orderable/tmf-acceptance-form',
		async (ctx): Promise<string> => {
			const tmfAcceptanceForm = await ctx.OrderTracker.getTmForceAcceptanceForm(
				ctx.query.OrderNumber
			);
			// ctx.set.headers['content-type'] = tmfAcceptanceForm.ContentType;
			// ctx.set.headers['content-disposition'] =
			// 	tmfAcceptanceForm.ContentDisposition;
			const base64Encoded = Buffer.from(tmfAcceptanceForm.Body).toString(
				'base64'
			);
			return base64Encoded;
		},
		{
			detail: {
				description:
					'Get tm force acceptance form for the given order number. <br><br> <b>Backend System: </b> TMFORCE',
				tags: ['Record']
			},
			query: tmForceAcceptanceFormReqSchema,
			response: {
				200: t.String(),
				500: errorBaseResponseSchema
			}
		}
	)
	.get(
		'/orderable/appointment-slots',
		async (ctx): Promise<AppointmentSlotRes> => {
			return await ctx.OrderTracker.getAppointmentSlotsByRegion(ctx.query);
		},
		{
			detail: {
				description:
					'Get appointment slots by region by the given order number. <br><br> <b>Backend System: </b> SWIFT',
				tags: ['Record']
			},
			query: appointmentSlotReqSchema,
			response: {
				200: appointmentSlotResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.get(
		'/non-orderable/history',
		async (ctx): Promise<OrderListRes> => {
			return await ctx.OrderTracker.getNonOrderableHistory();
		},
		{
			detail: {
				description:
					'Get user non-orderable / SLOF order history list for the past 90 days. <br><br> <b>Table: </b> non_orderable_txn_history',
				tags: ['Record']
			},
			response: {
				200: orderListResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.get(
		'/orderable/history',
		async (ctx): Promise<OrderListRes> => {
			return await ctx.OrderTracker.getOrderableHistory();
		},
		{
			detail: {
				description:
					'Get user orderable order history list for the past 90 days. <br><br> <b>Backend System: </b> NOVA, ICP <br> <b>Table: </b> orderable_txn_history',
				tags: ['Record']
			},
			response: {
				200: orderListResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.get(
		'/orderable/history/details',
		async (ctx): Promise<OrderDetailsRes> => {
			return await ctx.OrderTracker.getOrderDetails(ctx.query);
		},
		{
			detail: {
				description:
					"Retrieve order details based on order number. If there's no DigitalOrderID, set the ReferenceNo as empty string. <br><br> <b>Backend System: </b> NOVA SIEBEL, ICP, TMFORCE <br> <b>Table: </b> tm_force_status_lov, orderable_txn_history",
				tags: ['Record']
			},
			query: orderDetailsReqSchema,
			response: {
				200: orderDetailsResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.post(
		'/orderable/appointment',
		async (ctx): Promise<BaseResponse> => {
			const res = await ctx.OrderTracker.updateAppointmentSlot(
				ctx.headers.source ?? '',
				ctx.body
			);
			ctx.set.status = res.Code;
			return res;
		},
		{
			detail: {
				description:
					'Update appointment slot by order id or order number. <br><br> <b>Backend System: </b> SWIFT <br> <b>Table: </b> appointment_txn_history',
				tags: ['Record']
			},
			body: appointmentUpdateReqSchema,
			response: {
				201: baseResponseSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export default orderTrackerV1Routes;
