import {
	differenceInMinutes,
	format,
	isAfter,
	isValid,
	parse,
	set,
	subDays,
	subHours
} from 'date-fns';
import { eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getCache, setCache } from '../../../../config/cache.config';
import { getDbInstance } from '../../../../config/db.config';
import { envConfig } from '../../../../config/env.config';
import { pinoLog } from '../../../../config/pinoLog.config';
import { CacheKeyEnum } from '../../../../enum/cacheKey.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	Wso2OrderTrackingReq,
	Wso2OrderTrackingRes
} from '../../../../integration/wso2/record/schemas/api/wso2OrderTracking.schema';
import type {
	Wso2OrderStatusProgress,
	Wso2TmForceProgressUpdateRes
} from '../../../../integration/wso2/record/schemas/api/wso2TmForceGetProgressUpdate.schema';
import type {
	Wso2TmForceTechinicianDetailsReq,
	Wso2TmForceTechinicianDetailsRes
} from '../../../../integration/wso2/record/schemas/api/wso2TmForceTechinicianDetails.schema';
import { formatDate, getMyTimeZoneDate } from '../../../../shared/common';
import {
	type SelectOrderableTxnHistory,
	orderableTxnHistoryTableSchema
} from '../../../order/v1/schemas/db/orderable.schema';
import type {
	CaseDetails,
	OrderDetailsServiceInfo,
	OrderInformation,
	ReportItem,
	ReportItemList,
	ServiceInformation,
	TechnicianDetails
} from '../schemas/api/orderTracker.schema';
import {
	type SelectTmForceStatusLov,
	tmForceStatusLovTableSchema
} from '../schemas/db/tmForceLov.schema';

class OrderTrackerHelper {
	private db: NodePgDatabase;
	private mwIntegration: MwIntegration;
	private validActivityTypes = [
		'Installation',
		'Modify Internal Premise',
		'Site Installation & CPE Installation',
		'CPE Installation',
		'Activate/De-activate Features',
		'Speed Limit Configuration at Streamyx port'
	];
	private logDateTimePattern = 'dd/MM/yyyy HH:mm:ss';
	private logDateTimeFormat = 'dd MMMM yyyy, hh:mm a';
	private dateFormat = 'dd MMMM yyyy';
	private timeFormat = 'hh:mm a';
	private plannedPattern = 'MM/dd/yyyy HH:mm:ss';
	private truckRollLovList: SelectTmForceStatusLov[] = [];
	private nonTruckRollLovList: SelectTmForceStatusLov[] = [];

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.mwIntegration = new MwIntegration(integrationId);
	}

	async getServiceInfo(
		idValue: string,
		idType: string,
		orderId: string
	): Promise<ServiceInformation> {
		const dbResult: SelectOrderableTxnHistory[] = await this.db
			.select()
			.from(orderableTxnHistoryTableSchema)
			.where(eq(orderableTxnHistoryTableSchema.OrderId, orderId));

		if (dbResult.length > 0) {
			return {
				Unit: dbResult[0].Address?.UnitNo,
				Floor: dbResult[0].Address?.FloorNo,
				BuildingName: dbResult[0].Address?.BuildingName,
				StreetType: dbResult[0].Address?.StreetType,
				StreetName: dbResult[0].Address?.StreetName,
				Section: dbResult[0].Address?.Section,
				City: dbResult[0].Address?.City,
				State: dbResult[0].Address?.State,
				Postcode: dbResult[0].Address?.Postcode,
				Country: dbResult[0].Address?.Country
			};
		}

		const wso2Req: Wso2OrderTrackingReq = {
			OrderTracking: {
				SystemName: 'GOER',
				Orderdetails: {
					CustomerID: idValue,
					IdType: idType,
					OrderNo: orderId
				}
			}
		};
		const wso2Res: Wso2OrderTrackingRes =
			await this.mwIntegration.Wso2RecordIntegration.getWso2OrderTracking(
				wso2Req
			);

		if (wso2Res.Response.OrderDetails?.ViewOrderDetailResponse) {
			const orderDetail =
				wso2Res.Response.OrderDetails.ViewOrderDetailResponse.find(
					orderDetail => orderDetail !== null
				);
			if (orderDetail) {
				return {
					Unit:
						orderDetail?.HouseUnitLot ??
						orderDetail?.BAHouseUnitLot ??
						undefined,
					Floor: orderDetail?.Level ?? orderDetail?.BALevel ?? undefined,
					BuildingName:
						orderDetail?.BuildingName ??
						orderDetail?.BABuildingName ??
						undefined,
					StreetType:
						orderDetail?.StreetType ?? orderDetail?.BAStreetType ?? undefined,
					StreetName:
						orderDetail?.StreetName ?? orderDetail?.BAStreetName ?? undefined,
					Section: orderDetail?.Section ?? orderDetail?.BASection ?? undefined,
					City: orderDetail?.City ?? orderDetail?.BACity ?? undefined,
					State: orderDetail?.State ?? orderDetail?.BAState ?? undefined,
					Postcode:
						orderDetail?.Postcode ?? orderDetail?.BAPostCode ?? undefined,
					Country: orderDetail?.Country ?? orderDetail?.BACountry ?? undefined,
					RNORegion: orderDetail?.ServiceRegion ?? undefined
				};
			}
		}

		return null;
	}

	async setOrderDetailsServiceInfo(
		orderId: string,
		idType: string,
		idValue: string
	): Promise<OrderDetailsServiceInfo> {
		let orderInformation: OrderInformation | null = null;
		let serviceInfo: ServiceInformation | null = null;
		const orderTrackingReq: Wso2OrderTrackingReq = {
			OrderTracking: {
				SystemName: 'GOER',
				Orderdetails: {
					CustomerID: idValue,
					IdType: idType,
					OrderNo: orderId
				}
			}
		};

		const orderTrackingRes: Wso2OrderTrackingRes =
			await this.mwIntegration.Wso2RecordIntegration.getWso2OrderTracking(
				orderTrackingReq
			);
		if (orderTrackingRes.Response.OrderDetails?.ViewOrderDetailResponse) {
			const order =
				orderTrackingRes.Response.OrderDetails?.ViewOrderDetailResponse?.find(
					response => response !== null
				);
			if (order) {
				const createdDatePattern: string = order.CreatedDate
					? isValid(
							parse(order.CreatedDate, "yyyy-MM-dd'T'HH:mm:ssXXX", new Date())
						)
						? "yyyy-MM-dd'T'HH:mm:ssXXX"
						: 'MM/dd/yyyy HH:mm:ss'
					: 'MM/dd/yyyy HH:mm:ss';
				orderInformation = {
					OrderId: orderId,
					OrderPlanName: order.OrderPlanName ?? '-',
					CreatedDate: order.CreatedDate
						? formatDate(order.CreatedDate, createdDatePattern, this.dateFormat)
						: '-',
					CreatedTime: order.CreatedDate
						? formatDate(order.CreatedDate, createdDatePattern, this.timeFormat)
						: '-',
					Source: 'Subscription Channel : Retails'
				};
				serviceInfo = {
					Unit: order.BAHouseUnitLot ?? undefined,
					Floor: order.BALevel ?? undefined,
					BuildingName: order.BABuildingName ?? undefined,
					StreetType: order.BAStreetType ?? undefined,
					StreetName: order.BAStreetName ?? undefined,
					Section: order.BASection ?? undefined,
					City: order.BACity ?? undefined,
					State: order.BAState ?? undefined,
					Postcode: order.BAPostCode ?? undefined,
					Country: order.BACountry ?? undefined,
					RNORegion: order.ServiceRegion ?? undefined,
					ServiceID: order.ServiceID ?? undefined,
					TermAndCondition: `${
						envConfig().PUBLIC_PDF_DOMAIN
					}/sites/default/files/page/assets/documents/mobileunifi/termsandconditions.pdf`
				};
			}
		}
		return {
			orderInformation: orderInformation,
			serviceInformation: serviceInfo
		};
	}

	formattedTimestamp(logDatetime: string | null): string {
		return formatDate(
			logDatetime,
			this.logDateTimePattern,
			this.logDateTimeFormat
		);
	}

	async getTruckRollLovList(): Promise<SelectTmForceStatusLov[]> {
		if (this.truckRollLovList.length === 0) {
			// Store in redis for 24 hrs
			const cacheName = CacheKeyEnum.LOV_TRUCKROLL_LIST;
			const cache = await getCache(cacheName);
			if (cache) {
				this.truckRollLovList = JSON.parse(cache) as SelectTmForceStatusLov[];
			} else {
				const lovList: SelectTmForceStatusLov[] = await this.db
					.select()
					.from(tmForceStatusLovTableSchema)
					.where(eq(tmForceStatusLovTableSchema.Category, 'HOME'));
				await setCache(cacheName, JSON.stringify(lovList), 86400);
				this.truckRollLovList = lovList;
			}
		}
		return this.truckRollLovList;
	}

	async getNonTruckRollLovList(): Promise<SelectTmForceStatusLov[]> {
		if (this.nonTruckRollLovList.length === 0) {
			// Store in redis for 24 hrs
			const cacheName = CacheKeyEnum.LOV_NON_TRUCKROLL_LIST;
			const cache = await getCache(cacheName);
			if (cache) {
				this.nonTruckRollLovList = JSON.parse(
					cache
				) as SelectTmForceStatusLov[];
			} else {
				const lovList: SelectTmForceStatusLov[] = await this.db
					.select()
					.from(tmForceStatusLovTableSchema)
					.where(eq(tmForceStatusLovTableSchema.Category, 'HOME_NO_TRUCKROLL'));
				await setCache(cacheName, JSON.stringify(lovList), 86400);
				this.nonTruckRollLovList = lovList;
			}
		}
		return this.nonTruckRollLovList;
	}

	// Case 1: Application Received (NOVA)
	async getNovaApplicationReceived(
		newStatus: string | null,
		oldStatus: string | null,
		logDateTime: string | null
	): Promise<ReportItem | undefined> {
		if (newStatus === 'Pending Processing' && oldStatus === '') {
			const truckRollLovList: SelectTmForceStatusLov[] =
				await this.getTruckRollLovList();
			const lovStatus = truckRollLovList.find(
				lov => lov.Vertical === 'ORDER-COMPLETED'
			);
			if (lovStatus) {
				return {
					Title: lovStatus.HorizontalValue,
					Description: lovStatus.Description,
					Timestamp: this.formattedTimestamp(logDateTime)
				};
			}
		}
	}

	// Case 1: Order Submitted (NOVA)
	async getNovaOrderSubmitted(
		newStatus: string | null,
		oldStatus: string | null,
		logDateTime: string | null
	): Promise<ReportItem | undefined> {
		if (newStatus === 'Pending Processing' && oldStatus === '') {
			const truckRollLovList: SelectTmForceStatusLov[] =
				await this.getTruckRollLovList();
			const lovStatus = truckRollLovList.find(
				lov => lov.Vertical === 'SUBMITTED-PROCESSING'
			);
			if (lovStatus) {
				return {
					Title: lovStatus.HorizontalValue,
					Description: lovStatus.Description,
					Timestamp: this.formattedTimestamp(logDateTime)
				};
			}
		}
	}

	// Case 2: To Set Appointment (NOVA)
	async getNovaToSetAppointment(
		progressUpdate: Wso2OrderStatusProgress
	): Promise<ReportItem | undefined> {
		if (
			(progressUpdate.NewStatus === 'Unscheduled' &&
				(progressUpdate.OldStatus === 'Unscheduled' ||
					progressUpdate.OldStatus === '')) ||
			(progressUpdate.NewStatus === 'Unscheduled' &&
				progressUpdate.OldStatus === 'Scheduled' &&
				progressUpdate.Description !== 'Reappointment Release Slot') ||
			progressUpdate.NewStatus === 'Propose Reappt'
		) {
			pinoLog.info('START Case 2 : To Set Appointment (NOVA)');
			const truckRollLovList: SelectTmForceStatusLov[] =
				await this.getTruckRollLovList();
			const lovStatus = truckRollLovList.find(
				lov => lov.Vertical === 'REQUEST-APPOINTMENT'
			);
			if (lovStatus) {
				return {
					Title: lovStatus.HorizontalValue,
					Description: lovStatus.Description,
					Timestamp: this.formattedTimestamp(progressUpdate.LogDatetime)
				};
			}
			pinoLog.info('END Case 2 : To Set Appointment (NOVA)');
		}
	}

	// Case 1: Application Received (ICP)
	async getIcpApplicationReceived(
		logDateTime: string | null
	): Promise<ReportItem | undefined> {
		const lovList: SelectTmForceStatusLov[] = await this.getTruckRollLovList();
		const lovStatus = lovList.find(lov => lov.Vertical === 'ORDER-COMPLETED');
		if (lovStatus) {
			return {
				Title: lovStatus.HorizontalValue,
				Description: lovStatus.Description,
				Timestamp: this.formattedTimestamp(logDateTime)
			};
		}
	}

	// Case 1: Order Submitted (ICP)
	async getIcpOrderSubmitted(
		logDateTime: string | null
	): Promise<ReportItem | undefined> {
		const lovList: SelectTmForceStatusLov[] = await this.getTruckRollLovList();
		const lovStatus = lovList.find(
			lov => lov.Vertical === 'SUBMITTED-PROCESSING'
		);
		if (lovStatus) {
			return {
				Title: lovStatus.HorizontalValue,
				Description: lovStatus.Description,
				Timestamp: this.formattedTimestamp(logDateTime)
			};
		}
	}

	async getIcpOrderCreationDate(
		idType: string,
		idValue: string,
		orderNumber: string
	): Promise<Date | null> {
		const today: Date = getMyTimeZoneDate();
		const dateFormat: string = 'MM/dd/yyyy';
		const wso2OrderTrackingReq: Wso2OrderTrackingReq = {
			OrderTracking: {
				OrderList: {
					CustomerID: idValue,
					IdType: idType,
					StartDateTime: format(
						new Date(today).setDate(today.getDate() - 90),
						dateFormat
					),
					EndDateTime: format(
						new Date(today).setDate(today.getDate() + 1),
						dateFormat
					)
				}
			}
		};
		const wso2OrderTrackingRes =
			await this.mwIntegration.Wso2RecordIntegration.getWso2OrderTracking(
				wso2OrderTrackingReq
			);
		if (
			wso2OrderTrackingRes.Response.OrderList?.[
				'TmOrderEntry-OrdersIntegration-ICP'
			]
		) {
			const orderICP = wso2OrderTrackingRes.Response.OrderList[
				'TmOrderEntry-OrdersIntegration-ICP'
			].find(order => order.SiebelOrderId === orderNumber);
			if (orderICP?.CreationDate) {
				const orderICPCreationDate = parse(
					orderICP?.CreationDate,
					this.plannedPattern,
					new Date()
				);
				return orderICPCreationDate;
			}
		}
		return null;
	}

	// Case 2 : To Set Appointment (ICP)
	async getIcpToSetAppointment(
		progressUpdate: Wso2OrderStatusProgress,
		uniqueICPAppointmentDate: string[]
	): Promise<ReportItem | undefined> {
		pinoLog.info('START SHOULDPROCESSAPPOINTMENTSETTING');
		// Case 1: Generic unscheduled status update
		const isGenericUnscheduled: string | boolean =
			progressUpdate.NewStatus === 'Unscheduled' &&
			(progressUpdate.OldStatus || progressUpdate.OldStatus === 'Unscheduled');

		// Case 2: Scheduled to unscheduled transition
		const isScheduledToUnscheduled: boolean =
			progressUpdate.NewStatus === 'Unscheduled' &&
			progressUpdate.OldStatus === 'Scheduled' &&
			progressUpdate.Description !== 'Reappointment Release Slot' &&
			!progressUpdate.Description?.includes('null');

		// Case 3: Returned to propose reappointment transition
		const isReturnedToPropose: boolean | undefined =
			progressUpdate.NewStatus === 'Propose Reappt' &&
			progressUpdate.OldStatus === 'Returned' &&
			progressUpdate.Description?.includes('Update Service Item Status') &&
			(progressUpdate.ActivityType?.includes('Confirmation Call') ||
				progressUpdate.ActivityType?.includes(
					'Follow-up with customer (Returned Order scenario)'
				));

		if (
			isGenericUnscheduled ||
			isScheduledToUnscheduled ||
			isReturnedToPropose
		) {
			const isAppointmentListEmpty = uniqueICPAppointmentDate.find(
				date => date.length !== 0 && date === progressUpdate.LogDatetime
			);
			if (!isAppointmentListEmpty) {
				const lovList: SelectTmForceStatusLov[] =
					await this.getTruckRollLovList();
				const lovStatus = lovList.find(
					lov => lov.Vertical === 'REQUEST-APPOINTMENT'
				);
				if (lovStatus)
					return {
						Title: lovStatus.HorizontalValue,
						Description: lovStatus.Description,
						Timestamp: this.formattedTimestamp(progressUpdate.LogDatetime)
					};
			}
		}

		pinoLog.info('END getIcpToSetAppointment');
	}

	// Case 3 : Installation Scheduled (appointment received)
	async getInstallationScheduled(
		progressUpdate: Wso2OrderStatusProgress,
		systemName: string
	): Promise<
		| {
				ReportItem: ReportItem;
				AppointmentDate: string;
				AppointmentTime: string;
		  }
		| undefined
	> {
		if (this.shouldProcessInstallationSchedule(progressUpdate, systemName)) {
			const timestamp: string = this.formattedTimestamp(
				progressUpdate.LogDatetime
			);
			const appointmentDateTime: Date | null =
				progressUpdate.Description?.includes('New:')
					? parse(
							progressUpdate.Description.split('New:')[1].trim(),
							this.logDateTimePattern,
							new Date()
						)
					: progressUpdate.PlannedStart
						? parse(
								progressUpdate.PlannedStart,
								this.plannedPattern,
								new Date()
							)
						: null;

			if (appointmentDateTime) {
				const appointmentDate: string = format(
					appointmentDateTime,
					this.dateFormat
				);
				const appointmentTime: string = format(
					appointmentDateTime,
					this.timeFormat
				);
				const lovList: SelectTmForceStatusLov[] =
					await this.getTruckRollLovList();
				if (
					this.check4HoursBeforeInstallationTime(appointmentDateTime) ||
					progressUpdate.CustomerResponse === 'Yes'
				) {
					const lovStatus = lovList.find(
						lov => lov.Vertical === 'INSTALLATION-SCHEDULE'
					);
					if (lovStatus) {
						const description = lovStatus.Description.replace(
							'{appointment-date}',
							appointmentDate
						).replace('{appointment-time}', appointmentTime);
						return {
							ReportItem: {
								Title: lovStatus.HorizontalValue,
								Description: description,
								Timestamp: timestamp
							},
							AppointmentDate: appointmentDate,
							AppointmentTime: appointmentTime
						};
					}
				} else {
					const lovStatus = lovList.find(
						lov => lov.Vertical === 'TO-CONFIRM-APPOINTMENT'
					);
					if (lovStatus) {
						const description = lovStatus.Description.replace(
							'{appointment-date}',
							format(appointmentDateTime, this.dateFormat)
						);
						return {
							ReportItem: {
								Title: lovStatus.HorizontalValue,
								Description: description,
								Timestamp: timestamp
							},
							AppointmentDate: appointmentDate,
							AppointmentTime: appointmentTime
						};
					}
				}
			}
		}
	}

	shouldProcessInstallationSchedule(
		progressUpdate: Wso2OrderStatusProgress,
		systemName: string
	): boolean {
		pinoLog.info('START SHOULDPROCESSINSTALLATIONSCHEDULE');
		const isValidOldStatus = (status: string): boolean => {
			const validStatuses = ['Scheduled', 'Unscheduled', 'Propose Reappt'];
			return validStatuses.includes(status);
		};

		const isExcludedDescription = (description: string): boolean => {
			const excludedPhrases = [
				'reason=Missed Appointment',
				'Appointment Assign Slot',
				'UpdateServiceItem - Update Service Item Status'
			];
			return excludedPhrases.some(phrase => description.includes(phrase));
		};
		const isValidSystemDescription = (
			systemName: string,
			description: string
		): boolean => {
			if (systemName.toUpperCase() === 'NOVA') {
				return description !== 'UpdateStatus - Update Service Item Status';
			}
			if (systemName.toUpperCase() === 'ICP') {
				return !description.includes('Update Service Item');
			}
			return false;
		};

		pinoLog.info('END SHOULDPROCESSINSTALLATIONSCHEDULE');
		return (
			(progressUpdate.NewStatus?.includes('Scheduled') ?? false) &&
			isValidOldStatus(progressUpdate.OldStatus ?? '') &&
			!isExcludedDescription(progressUpdate.Description ?? '') &&
			isValidSystemDescription(systemName, progressUpdate.Description ?? '')
		);
	}

	// Checks if current time is after 4 PM on the day before the appointment
	checkAfter4PMPreviousDay(appointmentDate: Date): boolean {
		const currentTime: Date = getMyTimeZoneDate();

		// Calculate 4 PM on the day before appointment
		const cutoffTime: Date = set(
			subDays(appointmentDate, 1), // Get previous day
			{
				hours: 16,
				minutes: 0,
				seconds: 0,
				milliseconds: 0
			}
		);

		return isAfter(currentTime, cutoffTime);
	}

	// Check before 4 hours installation time
	check4HoursBeforeInstallationTime(appointmentDate: Date): boolean {
		const currentTime: Date = getMyTimeZoneDate();

		// Calculate the cutoff time (4 hours before the appointment time)
		const cutoffTime: Date = subHours(appointmentDate, 4);

		// Check if the current time is after the cutoff time
		return isAfter(currentTime, cutoffTime);
	}

	// Case 4: Unifi Care Crew Assigned
	async getUnifiCareCrewAssigned(
		progressUpdate: Wso2OrderStatusProgress
	): Promise<ReportItem | undefined> {
		const isValidStatus: boolean =
			progressUpdate.NewStatus === 'Assigned' &&
			progressUpdate.OldStatus === 'Not Started';

		const isValidActivity =
			progressUpdate.ActivityType &&
			(this.validActivityTypes.includes(progressUpdate.ActivityType) ||
				progressUpdate.ActivityType.includes('Perform Service Testing'));

		const isValidDescription =
			progressUpdate.Description &&
			(progressUpdate.Description.includes('Auto Order Assign') ||
				progressUpdate.Description.includes('Manual Assign'));

		if (isValidStatus && isValidActivity && isValidDescription) {
			const lovList: SelectTmForceStatusLov[] =
				await this.getTruckRollLovList();
			const lovStatus = lovList.find(
				lov => lov.Vertical === 'TECHNICIAN-ASSIGNED'
			);
			if (lovStatus) {
				return {
					Title: lovStatus.HorizontalValue,
					Description: lovStatus.Description,
					Timestamp: this.formattedTimestamp(progressUpdate.LogDatetime)
				};
			}
		}
	}

	// Case 4: Unifi Care Crew Reassigned
	async getUnifiCareCrewReassigned(
		progressUpdate: Wso2OrderStatusProgress
	): Promise<ReportItem | undefined> {
		const isValidStatus: boolean =
			progressUpdate.NewStatus === 'Assigned' &&
			progressUpdate.OldStatus === 'Assigned';

		const isValidActivity =
			progressUpdate.ActivityType &&
			(this.validActivityTypes.includes(progressUpdate.ActivityType) ||
				progressUpdate.ActivityType.includes('Perform Swap Number'));

		const isValidDescription =
			progressUpdate.Description &&
			(progressUpdate.Description.includes('Auto Order Assign') ||
				progressUpdate.Description.includes('Manual Assign'));

		if (isValidStatus && isValidActivity && isValidDescription) {
			const lovList: SelectTmForceStatusLov[] =
				await this.getTruckRollLovList();
			const lovStatus = lovList.find(
				lov => lov.Vertical === 'TECHNICIAN-REASSIGNED'
			);
			if (lovStatus) {
				return {
					Title: lovStatus.HorizontalValue,
					Description: lovStatus.Description,
					Timestamp: this.formattedTimestamp(progressUpdate.LogDatetime)
				};
			}
		}
	}

	/**
	 * case 5: On My Way (OMW) || Unifi Care Crew On the Way || Unifi Care Crew Arrival Update
	 * case 5.1: OMW - discovered during testing (Unifi Care Crew On the Way)
	 * case 5.2 OMW - discovered during testing (Unifi Care Crew On the Way)
	 * case 5.3 OMW - description contains keyword "ETTA" as per
	 * discussion with TMFORCE team
	 * */
	async getOnMyWay(
		currentIndex: number,
		reportItemListSize: number,
		orderProgress: Wso2TmForceProgressUpdateRes,
		progressUpdate: Wso2OrderStatusProgress
	): Promise<ReportItem | undefined> {
		if (
			progressUpdate.NewStatus === 'On My Way' &&
			(progressUpdate.OldStatus === 'On My Way' ||
				progressUpdate.OldStatus === 'Scheduled')
		) {
			const lovList: SelectTmForceStatusLov[] =
				await this.getTruckRollLovList();
			const timestamp: string = this.formattedTimestamp(
				progressUpdate.LogDatetime
			);

			if (progressUpdate.Description?.includes('ETTA')) {
				const { vertical, formattedDate, formattedTime } =
					this.getAppointmentDetails(
						currentIndex,
						reportItemListSize,
						orderProgress,
						progressUpdate
					);

				const lovStatus = lovList.find(lov => lov.Vertical === vertical);
				if (lovStatus) {
					const description: string = lovStatus.Description.replace(
						'{ETTA-DATE}',
						`${formattedDate} at ${formattedTime}`
					);
					return {
						Title: lovStatus.HorizontalValue,
						Description: description,
						Timestamp: timestamp
					};
				}
			}

			if (
				progressUpdate.Description?.includes(
					'UpdateServiceItem - Update Service Item Status'
				)
			) {
				const lovStatus = lovList.find(lov => lov.Vertical === 'ON-THE-WAY');
				if (lovStatus) {
					return {
						Title: lovStatus.HorizontalValue,
						Description: lovStatus.Description,
						Timestamp: timestamp
					};
				}
			}
		}
	}

	private getAppointmentDetails(
		index: number,
		reportItemListSize: number,
		orderProgress: Wso2TmForceProgressUpdateRes,
		progressUpdate: Wso2OrderStatusProgress
	): {
		vertical: string;
		formattedDate: string;
		formattedTime: string;
	} {
		const regex: RegExp = /\d{2}\/\d{2}\/\d{4} \d{2}:\d{2}:\d{2}/;
		const ettaDateTime: string | undefined =
			progressUpdate.Description?.match(regex)?.at(0);
		let appointmentDateTime: string | null = null;
		if (ettaDateTime) {
			appointmentDateTime =
				orderProgress.OrderStatusProgress.slice(index)
					.find(item => item.Description?.includes('Appointment:: New:'))
					?.Description?.split('Appointment:: New:')?.[1] ?? null;
		}

		if (reportItemListSize === 0 && progressUpdate.PlannedStart) {
			appointmentDateTime = progressUpdate.PlannedStart;
		}

		const difference: number =
			ettaDateTime && appointmentDateTime
				? differenceInMinutes(
						parse(ettaDateTime, this.logDateTimePattern, new Date()),
						parse(appointmentDateTime, this.logDateTimePattern, new Date())
					)
				: 0;

		const vertical =
			difference > 15
				? 'ARRIVAL-TIME-UPDATE-LATE'
				: difference >= 0 && difference <= 15
					? 'ARRIVAL-TIME-UPDATE'
					: 'ARRIVAL-TIME-UPDATE-EARLY';

		const formattedDate: string = ettaDateTime
			? formatDate(ettaDateTime, this.logDateTimePattern, this.dateFormat)
			: '';
		const formattedTime: string = ettaDateTime
			? formatDate(ettaDateTime, this.logDateTimePattern, this.timeFormat)
			: '';

		return { vertical, formattedDate, formattedTime };
	}

	// Case 6: On Site
	async getOnSite(
		newStatus: string | null,
		oldStatus: string | null,
		logDateTime: string | null
	): Promise<ReportItem | undefined> {
		pinoLog.info('START Case 6: On Site');
		if (newStatus === 'On Site' && oldStatus === 'On My Way') {
			const lovList: SelectTmForceStatusLov[] =
				await this.getTruckRollLovList();
			const lovStatus = lovList.find(lov => lov.Vertical === 'ON-SITE');
			pinoLog.info('END Case 6: On Site');
			if (lovStatus) {
				return {
					Title: lovStatus.HorizontalValue,
					Description: lovStatus.Description,
					Timestamp: this.formattedTimestamp(logDateTime)
				};
			}
		}
	}

	// Case 7: Postpone
	async getPostpone(
		systemName: string,
		caseDetails: CaseDetails,
		progressUpdate: Wso2OrderStatusProgress
	): Promise<ReportItem | undefined> {
		const isCustomerRelatedDescription: boolean | undefined =
			progressUpdate.Description?.includes('Customer') ||
			progressUpdate.Description?.includes('Building Maintenance');

		const isValidActivity: boolean = [
			'Traveling/On Site',
			'Verify to Proceed',
			'Installation'
		].includes(progressUpdate.ActivityType ?? '');

		const isValidIcpDescription: boolean = [
			'Pending Customer',
			'Pending Technical'
		].includes(progressUpdate.Description ?? '');

		const lovList: SelectTmForceStatusLov[] = await this.getTruckRollLovList();

		const timestamp: string = this.formattedTimestamp(
			progressUpdate.LogDatetime
		);
		// case 7.4: To Confirm Appointment (confirmation to postpone)
		// remove this case due to new progress status flow in UA (09/04/2025)
		// if (
		// 	progressUpdate.NewStatus === 'Proposed Return' &&
		// 	progressUpdate.OldStatus === 'Assigned' &&
		// 	progressUpdate.Description?.includes('Customer')
		// ) {
		// 	const lovStatus = lovList.find(
		// 		lov => lov.Vertical === 'PROPOSAL-POSTPONE-APPOINTMENT'
		// 	);
		// 	if (lovStatus) {
		// 		return {
		// 			Title: lovStatus.HorizontalValue,
		// 			Description: lovStatus.Description,
		// 			Timestamp: timestamp
		// 		};
		// 	}
		// }

		if (progressUpdate.NewStatus === 'Returned') {
			// case 7: General Postpone
			if (
				progressUpdate.OldStatus === 'Proposed Return' &&
				progressUpdate.Description &&
				progressUpdate.Description !== 'Update Order' &&
				isValidActivity
			) {
				// Customer Postpone
				if (isCustomerRelatedDescription) {
					const lovStatus = lovList.find(
						lov => lov.Vertical === 'APPOINTMENT-POSTPONE-CUSTOMER'
					);
					if (lovStatus) {
						pinoLog.info({
							caseDetails,
							message: 'IM IN APPOINTMENT-POSTPONE-CUSTOMER'
						});
						const description: string =
							caseDetails.AppointmentDate && caseDetails.AppointmentTime
								? lovStatus.Description.replace(
										'{appointment-date}',
										caseDetails.AppointmentDate
									).replace('{appointment-time}', caseDetails.AppointmentTime)
								: lovStatus.Description;

						return {
							Title: lovStatus.HorizontalValue,
							Description: description,
							Timestamp: timestamp
						};
					}
				}

				// Technical Postpone
				if (
					!isCustomerRelatedDescription &&
					progressUpdate.Description.includes('Technical')
				) {
					const lovStatus = lovList.find(
						lov => lov.Vertical === 'APPOINTMENT-POSTPONE-TECHNICAL'
					);
					if (lovStatus) {
						return {
							Title: lovStatus.HorizontalValue,
							Description: lovStatus.Description,
							Timestamp: timestamp
						};
					}
				}
			}

			// case 7.1: Technical Postpone
			if (
				progressUpdate.OldStatus === 'Assigned' &&
				((systemName === 'NOVA' && isValidActivity) ||
					(systemName === 'ICP' && isValidIcpDescription))
			) {
				const lovStatus = lovList.find(
					lov => lov.Vertical === 'APPOINTMENT-POSTPONE-TECHNICAL'
				);

				if (lovStatus) {
					return {
						Title: lovStatus.HorizontalValue,
						Description: lovStatus.Description,
						Timestamp: timestamp
					};
				}
			}

			// case 7.3: Installation Update (post-technical postpone)
			if (
				progressUpdate.OldStatus === 'Returned' &&
				progressUpdate.Description?.includes('ETTC')
			) {
				const lovStatus = lovList.find(
					lov => lov.Vertical === 'TECHNICAL-APPOINTMENT-UPDATE'
				);
				if (lovStatus) {
					const date = progressUpdate.Description?.match('d{2}/d{2}/d{4}');
					const description = lovStatus.Description.replace(
						'{ETTC-DATE}',
						date ? date[0] : ''
					);
					return {
						Title: lovStatus.HorizontalValue,
						Description: description,
						Timestamp: timestamp
					};
				}
			}
		}
	}

	// Case 7.2: Installation Update (technical pre-assigned appointment)
	async getTechnicalPreAssignedAppointment(
		progressUpdate: Wso2OrderStatusProgress
	): Promise<
		| {
				ReportItem: ReportItem;
				AppointmentDate: string;
				AppointmentTime: string;
		  }
		| undefined
	> {
		if (
			progressUpdate.OldStatus === 'Propose Reappt' &&
			(progressUpdate.NewStatus === 'Scheduled' ||
				progressUpdate.NewStatus === 'Returned')
		) {
			const lovList: SelectTmForceStatusLov[] =
				await this.getTruckRollLovList();
			const lovStatus = lovList.find(
				lov => lov.Vertical === 'TECHNICAL-PREDEFINED-APPOINTMENT'
			);
			const appointmentDateTime: string | null =
				progressUpdate.Description?.includes('Appointment:: New:')
					? progressUpdate.Description.split('Appointment:: New:')[1]
					: null;
			if (lovStatus && appointmentDateTime) {
				const description: string = lovStatus.Description.replace(
					'{appointment-date}',
					appointmentDateTime
				);

				return {
					ReportItem: {
						Title: lovStatus.HorizontalValue,
						Description: description,
						Timestamp: this.formattedTimestamp(progressUpdate.LogDatetime)
					},
					AppointmentDate: formatDate(
						appointmentDateTime,
						this.logDateTimePattern,
						this.dateFormat
					),
					AppointmentTime: formatDate(
						appointmentDateTime,
						this.logDateTimePattern,
						this.timeFormat
					)
				};
			}
		}
	}

	// Case 8: Activated (complete & activation)
	async getActivated(
		progressUpdate: Wso2OrderStatusProgress
	): Promise<ReportItem | undefined> {
		pinoLog.info('START Case 8: Activated (complete & activation)');
		const isValidActivity =
			progressUpdate.ActivityType &&
			(this.validActivityTypes.includes(progressUpdate.ActivityType) ||
				progressUpdate.ActivityType.includes('Perform Service Testing'));

		if (
			progressUpdate.NewStatus === 'Done' &&
			progressUpdate.Description !== 'Create activity' &&
			isValidActivity
		) {
			const lovList: SelectTmForceStatusLov[] =
				await this.getTruckRollLovList();
			const lovStatus = lovList.find(lov => lov.Vertical === 'ACTIVATED');
			if (lovStatus) {
				return {
					Title: lovStatus.HorizontalValue,
					Description: lovStatus.Description,
					Timestamp: this.formattedTimestamp(progressUpdate.LogDatetime)
				};
			}
		}
	}

	// Case 9: Cancelled
	async getCancelled(
		progressUpdate: Wso2OrderStatusProgress
	): Promise<ReportItem | undefined> {
		pinoLog.info('START SHOULDPROCESSCANCELLATION');
		// Case 1: Installation-related cancellation
		const isInstallationCancelled: boolean =
			progressUpdate.NewStatus === 'Cancelled' &&
			['Installation', 'Modify Internal Premise'].includes(
				progressUpdate.ActivityType ?? ''
			);

		// Case 2: ICP Cancellation
		const isICPActivityCancelled: boolean =
			progressUpdate.NewStatus === 'Cancelled' &&
			progressUpdate.OldStatus === 'Pending Processing' &&
			progressUpdate.ActivityType?.length === 0;

		// Case 3: Order cancellation during processing
		const isOrderCancelled: boolean | undefined =
			progressUpdate.NewStatus === 'Cancelled' &&
			['Open', 'Processing', 'Pending Processing'].includes(
				progressUpdate.OldStatus ?? ''
			) &&
			progressUpdate.Description?.includes('Cancel Order');

		if (isInstallationCancelled || isICPActivityCancelled || isOrderCancelled) {
			const lovList: SelectTmForceStatusLov[] =
				await this.getTruckRollLovList();
			const lovStatus = lovList.find(lov => lov.Vertical === 'ORDER-CANCELED');
			if (lovStatus) {
				return {
					Title: lovStatus.HorizontalValue,
					Description: lovStatus.Description,
					Timestamp: this.formattedTimestamp(progressUpdate.LogDatetime)
				};
			}
		}
	}

	// Case 9: Cancelled Appointment Date (Special Case)
	getCancelledAppointmentDate(progressUpdate: Wso2OrderStatusProgress):
		| {
				defferedAppointmentDate: string;
				defferedAppointmentTime: string;
		  }
		| undefined {
		if (
			progressUpdate.Description?.includes('Previous appointment on = ') &&
			!progressUpdate.Description?.includes('value reset only') &&
			!progressUpdate.Description?.includes('Release Slot')
		) {
			const regex = /\d{2}\/\d{2}\/\d{4} \d{2}:\d{2}:\d{2}/;
			const match = progressUpdate.Description?.match(regex);
			if (match?.[0]) {
				const defferedAppointmentDate: string = formatDate(
					match[0],
					this.logDateTimePattern,
					this.dateFormat
				);
				const defferedAppointmentTime: string = formatDate(
					match[0],
					this.logDateTimePattern,
					this.timeFormat
				);

				return {
					defferedAppointmentDate,
					defferedAppointmentTime
				};
			}
		}
	}

	// Case 9: Cancelled Order during pending processing (Nova)
	async getCancelledOrderDuringPendingProcessing(): Promise<
		ReportItem | undefined
	> {
		const lovList: SelectTmForceStatusLov[] = await this.getTruckRollLovList();
		const lovStatus = lovList.find(lov => lov.Vertical === 'ORDER-CANCELED');
		if (lovStatus) {
			return {
				Title: lovStatus.HorizontalValue,
				Description: lovStatus.Description,
				Timestamp: '-'
			};
		}
	}

	async setCaseDetails(
		orderProgress: Wso2OrderStatusProgress[],
		caseDetails: CaseDetails,
		reportItem?: ReportItem
	): Promise<CaseDetails> {
		if (
			reportItem?.Title &&
			('Installation Postpone' === reportItem?.Title ||
				'Installation Update' === reportItem?.Title ||
				'To Set Appointment' === reportItem?.Title)
		) {
			Object.assign(caseDetails, {
				AppointmentDate: undefined,
				AppointmentTime: undefined,
				AppointmentEndDate: undefined,
				AppointmentEndTime: undefined
			});
			return caseDetails;
		}

		const firstEntryWithAppointmentInfo =
			orderProgress
				.filter(progressEntry => progressEntry !== null)
				.filter(
					progressEntry =>
						progressEntry.PlannedStart && progressEntry.PlannedStart !== ''
				)
				.filter(
					progressEntry =>
						progressEntry.PlannedEnd && progressEntry.PlannedEnd !== ''
				)[0] || null;

		if (firstEntryWithAppointmentInfo) {
			caseDetails.AppointmentDate = formatDate(
				firstEntryWithAppointmentInfo.PlannedStart,
				this.plannedPattern,
				this.dateFormat
			);
			caseDetails.AppointmentTime = formatDate(
				firstEntryWithAppointmentInfo.PlannedStart,
				this.plannedPattern,
				this.timeFormat
			);
			caseDetails.AppointmentEndDate = formatDate(
				firstEntryWithAppointmentInfo.PlannedEnd,
				this.plannedPattern,
				this.dateFormat
			);
			caseDetails.AppointmentEndTime = formatDate(
				firstEntryWithAppointmentInfo.PlannedEnd,
				this.plannedPattern,
				this.timeFormat
			);
		}

		return caseDetails;
	}

	async getTechnicianReport(
		referenceNo: string,
		reportItemList: ReportItemList
	): Promise<
		| {
				technicianDetails: TechnicianDetails;
				appointmentDate: string;
				appointmentTime: string;
		  }
		| undefined
	> {
		const technicianReport = [
			'Unifi Care Crew Assigned',
			'Unifi Care Crew On the Way',
			'Unifi Care Crew On-Site',
			'Unifi Care Crew Arrival Update',
			'Unifi Care Crew Reassigned'
		].includes(reportItemList.at(0)?.Title ?? '');

		if (technicianReport) {
			const technicianRequest: Wso2TmForceTechinicianDetailsReq = {
				TechnicianDetailsRequest: {
					TTNumber: '',
					OrderNumber: referenceNo
				}
			};
			const wso2TechnicianDetails: Wso2TmForceTechinicianDetailsRes =
				await this.mwIntegration.Wso2RecordIntegration.getWso2TmForceTechinicianDetails(
					technicianRequest
				);
			if (wso2TechnicianDetails.Response?.TechnicianDetail) {
				const technician = wso2TechnicianDetails.Response.TechnicianDetail;
				return {
					technicianDetails: {
						ProfilePicture: technician?.TechnicianPhoto ?? '',
						TechnicianName: technician?.TechnicianName ?? '',
						PhoneNumber: technician?.TechnicianPhoneNumber ?? '',
						Latitude: technician?.Latitude ?? '',
						Longitude: technician?.Longitude ?? '',
						ETTA: technician?.ETTA ?? ''
					},
					appointmentDate: formatDate(
						technician?.OrderAppointmentDate ?? technician?.ETTA,
						this.plannedPattern,
						this.dateFormat
					),
					appointmentTime: formatDate(
						technician?.OrderAppointmentDate ?? technician?.ETTA,
						this.plannedPattern,
						this.timeFormat
					)
				};
			}
		}
	}

	async getProgressBySiebelOrderStatus(
		referenceStatus: string,
		isTruckRoll: boolean
	): Promise<ReportItem | undefined> {
		pinoLog.info('START ORDERPROGRESS EMPTY');
		if (isTruckRoll) {
			const lovList: SelectTmForceStatusLov[] =
				await this.getTruckRollLovList();
			const statusMapping =
				referenceStatus === 'OPEN' ||
				referenceStatus === 'PENDING' ||
				referenceStatus === 'PROCESSING'
					? 'SUBMITTED-PROCESSING'
					: referenceStatus === 'CANCELLED'
						? 'ORDER-CANCELED'
						: referenceStatus === 'COMPLETE' || referenceStatus === 'COMPLETED'
							? 'ACTIVATED'
							: 'ORDER-COMPLETED';
			const lovStatus = lovList.find(lov => lov.Vertical === statusMapping);
			if (lovStatus) {
				return {
					Title: lovStatus.HorizontalValue,
					Description: lovStatus.Description,
					Timestamp: '-'
				};
			}
		} else {
			const lovList: SelectTmForceStatusLov[] =
				await this.getNonTruckRollLovList();
			const STATUS_MAPPING: Record<string, string> = {
				OPEN: 'PENDING',
				COMPLETE: 'COMPLETED'
			};
			const verticalStatus = (): string => {
				return STATUS_MAPPING[referenceStatus] || referenceStatus;
			};
			const lovStatus = lovList.find(lov => lov.Vertical === verticalStatus());
			if (lovStatus) {
				return {
					Title: lovStatus.HorizontalValue,
					Description: lovStatus.Description,
					Timestamp: '-'
				};
			}
		}
		pinoLog.info('END ORDERPROGRESS EMPTY');
	}

	async getHorizontalDescription(title: string): Promise<string> {
		const lovList: SelectTmForceStatusLov[] = await this.getTruckRollLovList();
		const lovStatus = lovList.find(lov => lov.HorizontalValue === title);
		if (lovStatus) {
			return lovStatus.HorizontalDescription;
		}
		return '';
	}
}

export default OrderTrackerHelper;
