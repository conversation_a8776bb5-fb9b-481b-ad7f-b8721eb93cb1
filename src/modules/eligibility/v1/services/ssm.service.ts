import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { IdTypeEnum } from '../../../../enum/user.enum';
import {
	LightweightFlagEnum,
	SystemNameEnum
} from '../../../../enum/wso2.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	Wso2SSMInfoReq,
	Wso2SSMInfoRes
} from '../../../../integration/wso2/eligibility/schemas/api/wso2SSMInfo.schema';
import type {
	Wso2CustomerAccountReq,
	Wso2CustomerAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2CustomerAccount.schema';
import { UE_ERROR } from '../../../../middleware/error';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import { BRNPatterns } from '../../../../shared/common';
import type { SSMEligibilityRes } from '../schemas/api/ssm.schema';

class SSMEligibility {
	private integrationId: string;
	private mwIntegration: MwIntegration;
	private idTokenInfo: IdTokenInfo;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo) {
		this.integrationId = integrationId;
		this.mwIntegration = new MwIntegration(integrationId);
		this.idTokenInfo = idTokenInfo;
	}

	async getSSMEligibility(): Promise<SSMEligibilityRes> {
		const wso2CAReq: Wso2CustomerAccountReq = {
			idType: this.idTokenInfo.IdType,
			idValue: this.idTokenInfo.IdValue
		};
		const wso2CARes: Wso2CustomerAccountRes =
			(await this.mwIntegration.Wso2UserIntegration.getWso2CustomerAccount(
				wso2CAReq,
				LightweightFlagEnum.NO
			)) as Wso2CustomerAccountRes;

		if (
			wso2CARes.Response?.CustomerAccounts &&
			wso2CARes.Response?.CustomerAccounts?.length > 0
		) {
			const res: SSMEligibilityRes = {
				Success: true,
				Code: StatusCodeEnum.OK,
				IntegrationId: this.integrationId,
				Response: {
					IsBRNCustomer: this.idTokenInfo.IdType === IdTypeEnum.BRN,
					IsExistingCustomer: true,
					CustomerAccounts:
						wso2CARes.Response?.CustomerAccounts?.filter(
							ca => ca?.SystemName === SystemNameEnum.NOVA
						) ?? []
				}
			};
			return res;
		}

		let brnIdType = 'BRN Others';
		if (this.idTokenInfo.IdType === IdTypeEnum.BRN) {
			const matchedPattern = BRNPatterns.find(p =>
				p.pattern.test(this.idTokenInfo.IdType)
			);
			if (
				matchedPattern?.format === 'BRN ROB 1' ||
				matchedPattern?.format === 'BRN ROB 2'
			) {
				brnIdType = 'BRN ROB';
			}
		} else if (this.idTokenInfo.IdType === IdTypeEnum.NON_BRN) {
			brnIdType = 'CRN';
		}

		const wso2SSMInfoReq: Wso2SSMInfoReq = {
			CheckSSMInfoRequest: {
				CheckSSMInfoReq: {
					SupportingDocumentFlag: 'Y',
					Platform: 'DICE-NOVA',
					LoginId: 'DICE_USER',
					EaiId: Math.random().toString(36).slice(2),
					SSMType: brnIdType,
					SSMId: this.idTokenInfo.IdType
				}
			}
		};

		const wso2SSMInfoRes: Wso2SSMInfoRes =
			await this.mwIntegration.Wso2EligibilityIntegration.getWso2SSMInfo(
				wso2SSMInfoReq
			);

		if (wso2SSMInfoRes?.faultMessage && wso2SSMInfoRes?.faultMessage) {
			throw new UE_ERROR(
				wso2SSMInfoRes?.faultMessage.detailedText,
				StatusCodeEnum.NOT_ACCEPTABLE_ERROR,
				{
					integrationId: this.integrationId,
					response: wso2SSMInfoRes
				}
			);
		}

		const res: SSMEligibilityRes = {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				IsBRNCustomer: this.idTokenInfo.IdType === IdTypeEnum.BRN,
				SSMCheckStatus:
					wso2SSMInfoRes?.CheckSSMInfoResponse?.CheckSSMInfoRes.SSMStatus ?? ''
			}
		};
		return res;
	}
}

export default SSMEligibility;
