import { and, eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import { qualtrixTnpsUserDetailsTableSchema } from '../../../record/v1/schemas/db/qualtrixTnpsUserDetails.schema';
import type { VocEligibilityRes } from '../schemas/api/survey.schema';

class SurveyEligibility {
	private integrationId: string;
	private db: NodePgDatabase;
	private idTokenInfo: IdTokenInfo;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo) {
		this.integrationId = integrationId;
		this.db = getDbInstance();
		this.idTokenInfo = idTokenInfo;
	}
	async getVocEligibility(): Promise<VocEligibilityRes> {
		const queryResult = await this.db
			.select()
			.from(qualtrixTnpsUserDetailsTableSchema)
			.where(
				and(
					eq(
						qualtrixTnpsUserDetailsTableSchema.IdType,
						this.idTokenInfo.IdType
					),
					eq(
						qualtrixTnpsUserDetailsTableSchema.IdValue,
						this.idTokenInfo.IdValue
					)
				)
			)
			.groupBy(qualtrixTnpsUserDetailsTableSchema.SubmissionDate)
			.limit(1)
			.execute()
			.catch(err => {
				console.error(err);
				return null;
			});

		if (queryResult === null || queryResult.length === 0) {
			return {
				Success: true,
				Code: StatusCodeEnum.OK,
				IntegrationId: this.integrationId,
				Response: {
					IsParticipant: 'N',
					SurveyUrl: null,
					Email: null,
					Eligibility: 'N'
				}
			};
		}

		const [result] = queryResult;
		const eligible = result.SubmissionStatus === null ? 'Y' : 'N';
		const isParticipant = result ? 'Y' : 'N';
		const email = result?.Email ? result.Email : null;

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				IsParticipant: isParticipant,
				SurveyUrl:
					'https://tmvoc.qualtrics.com/jfe/form/SV_ekSHCO6sDUrcXxY?cust_id=',
				Email: email,
				Eligibility: eligible
			}
		};
	}
}

export default SurveyEligibility;
