import { AddOnsRequestCategoryEnum } from '../../../../enum/addOns.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import type { Wso2ServiceAccountRes } from '../../../../integration/wso2/user/schemas/api/wso2ServiceAccount.schema';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import { decrypt } from '../../../../shared/encryption/aesGcm';
import AddOnsHelper from '../helpers/addOns.helper';
import type {
	AddOnsEligibilityReq,
	AddOnsEligibilityRes
} from '../schemas/api/addOns.schema';

class AddOnsEligibility {
	private integrationId: string;
	private addOnsHelper: AddOnsHelper;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo) {
		this.integrationId = integrationId;
		this.addOnsHelper = new AddOnsHelper(integrationId, idTokenInfo);
	}
	/**
	 * Main entry point for Add-Ons eligibility checks by category.
	 */
	async getAddOnsEligibility(
		req: AddOnsEligibilityReq
	): Promise<AddOnsEligibilityRes> {
		const decryptedBillAccNo: string = await decrypt(req.EncryptedBillAccNo);
		// Fetch service account details
		const wso2SARes: Wso2ServiceAccountRes =
			await this.addOnsHelper.fetchServiceAccount(decryptedBillAccNo);

		// Get High-Speed Internet (HSI) Moli
		const hsiMoli = this.addOnsHelper.getHighSpeedInternetMoli(wso2SARes);
		if (!hsiMoli)
			return await this.addOnsHelper.createErrorResponse('BROADBAND');

		// Perform common eligibility checks
		const eligibilityError =
			await this.addOnsHelper.performCommonEligibilityChecks(
				req,
				decryptedBillAccNo,
				hsiMoli
			);
		if (eligibilityError) return eligibilityError;

		// Check open order in Siebel
		const openOrderSiebel =
			await this.addOnsHelper.checkOpenOrderInSiebel(decryptedBillAccNo);
		if (openOrderSiebel) {
			return openOrderSiebel;
		}

		// Check for open order in DB
		const openOrderDb =
			await this.addOnsHelper.checkOpenOrderInDb(decryptedBillAccNo);
		if (openOrderDb) {
			return openOrderDb;
		}

		// Perform category-specific eligibility checks
		switch (req.Category) {
			case AddOnsRequestCategoryEnum.SMART_DEVICE:
				return await this.addOnsHelper.getEligibleSmartDevice(
					req,
					decryptedBillAccNo,
					hsiMoli
				);
			case AddOnsRequestCategoryEnum.SMART_HOME:
				return await this.addOnsHelper.getEligibleSmartHome(
					req,
					decryptedBillAccNo,
					hsiMoli
				);
			case AddOnsRequestCategoryEnum.MESH_WIFI:
				return await this.addOnsHelper.handleMeshWifi(hsiMoli);
			case AddOnsRequestCategoryEnum.UPB:
				return await this.addOnsHelper.handleUpb(wso2SARes, hsiMoli);
			case AddOnsRequestCategoryEnum.BLACKNUT:
				return await this.addOnsHelper.handleBlacknut(
					decryptedBillAccNo,
					hsiMoli,
					wso2SARes
				);
			case AddOnsRequestCategoryEnum.TV_PACK:
				return await this.addOnsHelper.handleTvPack(wso2SARes);
			default:
				return {
					Success: true,
					Code: StatusCodeEnum.OK,
					IntegrationId: this.integrationId,
					Response: {
						IsEligible: false,
						Reason: `There is no eligibility check available for ${req.Category}.`,
						ReasonCode: 'CATEGORY-NOT-FOUND',
						CTAButtonText: null
					}
				};
		}
	}
}

export default AddOnsEligibility;
