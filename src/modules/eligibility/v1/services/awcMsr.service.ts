import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { IdTypeEnum } from '../../../../enum/user.enum';
import {
	LightweightFlagEnum,
	SystemNameEnum
} from '../../../../enum/wso2.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	Wso2CustomerProfileCheckReq,
	Wso2CustomerProfileCheckRes
} from '../../../../integration/wso2/eligibility/schemas/api/wso2CustomerProfileCheck.schema';
import type {
	Wso2CustomerAccountReq,
	Wso2CustomerAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2CustomerAccount.schema';
import type {
	Wso2ServiceAccountReq,
	Wso2ServiceAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2ServiceAccount.schema';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import { BRNPatterns } from '../../../../shared/common';
import { decrypt } from '../../../../shared/encryption/aesGcm';
import type {
	AWCMSREligibilityRes,
	CSAWCMSREligibilityReq,
	CsProductDetails,
	DmsAWCMSREligibilityReq
} from '../schemas/api/awcMsr.schema';

class AwcMsrEligibility {
	private integrationId: string;
	private mwIntegration: MwIntegration;
	private idTokenInfo: IdTokenInfo;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo) {
		this.integrationId = integrationId;
		this.mwIntegration = new MwIntegration(integrationId);
		this.idTokenInfo = idTokenInfo;
	}

	async getDmsAwcMsrEligibility(
		req: DmsAWCMSREligibilityReq
	): Promise<AWCMSREligibilityRes> {
		const wso2ProfileCheckReq: Wso2CustomerProfileCheckReq = {
			CustomerProfileCheckRequest: {
				SystemName: SystemNameEnum.NOVA,
				IdType: this.idTokenInfo.IdType,
				IdValue: this.idTokenInfo.IdValue
			}
		};

		// AWC Check
		const wso2ProfileCheckRes: Wso2CustomerProfileCheckRes =
			await this.mwIntegration.Wso2EligibilityIntegration.getWso2CustomerProfileCheck(
				wso2ProfileCheckReq
			);
		const isSubmitToSlof = wso2ProfileCheckRes.Response === null;
		const isAWCCheckPassed =
			wso2ProfileCheckRes.Response?.CustomerProfileCheckResponse !== null;

		// MSR Check
		let isMSRCheckPassed = true;
		const wso2CAReq: Wso2CustomerAccountReq = {
			idType: this.idTokenInfo.IdType,
			idValue: this.idTokenInfo.IdValue
		};
		const wso2CARes: Wso2CustomerAccountRes =
			await this.mwIntegration.Wso2UserIntegration.getWso2CustomerAccount(
				wso2CAReq,
				LightweightFlagEnum.YES
			);

		for (const ca of wso2CARes.Response?.CustomerAccounts ?? []) {
			if (ca?.SystemName === SystemNameEnum.NOVA) {
				for (const ba of ca?.BillingAccounts ?? []) {
					const wso2SAReq: Wso2ServiceAccountReq = {
						idType: this.idTokenInfo.IdType,
						idValue: this.idTokenInfo.IdValue,
						BillingAccountNo: ba?.AccountNumber ?? '',
						SystemName: SystemNameEnum.NOVA
					};

					const wso2SARes: Wso2ServiceAccountRes =
						(await this.mwIntegration.Wso2UserIntegration.getWso2ServiceAccount(
							wso2SAReq,
							LightweightFlagEnum.NO
						)) as Wso2ServiceAccountRes;

					const dmsProducts = wso2SARes?.Response?.ServiceAccount?.find(sa => {
						return sa?.ServiceAccountMoli?.find(moli => {
							return moli?.ServiceAccountOli?.find(oli => {
								return (
									oli?.ProductName?.toLowerCase().includes(
										'digital marketing solutions'
									) &&
									(oli.ProductName.toLowerCase().includes('starter pack') ||
										oli.ProductName.toLowerCase().includes('standard pack') ||
										oli.ProductName.toLowerCase().includes('premium pack') ||
										(req.PurchaseType === 'Paid' &&
											oli?.ProductName?.toLowerCase().includes(
												'freemium pack'
											) &&
											oli.Status === 'Active') ||
										(req.PurchaseType === 'Free' &&
											oli?.ProductName?.toLowerCase().includes(
												'freemium pack'
											)))
								);
							});
						});
					});

					if (dmsProducts) {
						isMSRCheckPassed = false;
					}
				}
			}
		}

		const res: AWCMSREligibilityRes = {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				IsMSRCheckPassed: isMSRCheckPassed,
				IsAWCCheckPassed: isAWCCheckPassed,
				IsSubmitToSlof: isSubmitToSlof
			}
		};

		return res;
	}

	async getCyberSecurityAwcMsrEligibility(
		req: CSAWCMSREligibilityReq
	): Promise<AWCMSREligibilityRes> {
		let brnIdType = 'BRN Others';
		let isAWCCheckPassed = false;
		let isMSRCheckPassed = false;
		let isSubmitToSlof = false;
		const csProductQuantity: number =
			req.ProductDetails?.reduce((total: number, product: CsProductDetails) => {
				return total + product.CyberSecurityPackQuantity;
			}, 0) ?? 0;
		let subscribedCsProductQuantity = 0;

		if (this.idTokenInfo.IdType === IdTypeEnum.BRN) {
			const matchedPattern = BRNPatterns.find(p =>
				p.pattern.test(this.idTokenInfo.IdValue)
			);
			if (
				matchedPattern?.format === 'BRN ROB 1' ||
				matchedPattern?.format === 'BRN ROB 2'
			) {
				brnIdType = 'BRN ROB';
			} else if (matchedPattern?.format === 'BRN ROC') {
				brnIdType = 'BRN ROC';
			} else {
				brnIdType = matchedPattern?.format ?? 'BRN Others';
			}
		}

		const wso2ProfileCheckReq: Wso2CustomerProfileCheckReq = {
			CustomerProfileCheckRequest: {
				SystemName: SystemNameEnum.NOVA,
				IdType: brnIdType,
				IdValue: this.idTokenInfo.IdValue
			}
		};

		// AWC Check
		const wso2ProfileCheckRes: Wso2CustomerProfileCheckRes =
			await this.mwIntegration.Wso2EligibilityIntegration.getWso2CustomerProfileCheck(
				wso2ProfileCheckReq
			);

		if (
			wso2ProfileCheckRes.Response &&
			wso2ProfileCheckRes.Response.CustomerProfileCheckResponse !== null
		) {
			const customerProfileCheckResponse =
				wso2ProfileCheckRes.Response.CustomerProfileCheckResponse;
			isAWCCheckPassed =
				customerProfileCheckResponse.CustomerCheckOkFlag === 'Y' &&
				(customerProfileCheckResponse.BlacklistType === 'N' ||
					customerProfileCheckResponse.BlacklistType == null) &&
				(customerProfileCheckResponse.FraudIndicator === 'N' ||
					customerProfileCheckResponse.FraudIndicator == null);
			isSubmitToSlof = customerProfileCheckResponse.CustomerCheckOkFlag === 'N';
		} else {
			isSubmitToSlof = true;
		}

		// MSR Check
		const decryptedBillAccNo: string = await decrypt(req.EncryptedBillAccNo);
		const wso2SAReq: Wso2ServiceAccountReq = {
			idType: this.idTokenInfo.IdType,
			idValue: this.idTokenInfo.IdValue,
			BillingAccountNo: decryptedBillAccNo,
			SystemName: SystemNameEnum.NOVA
		};

		const wso2SARes: Wso2ServiceAccountRes =
			(await this.mwIntegration.Wso2UserIntegration.getWso2ServiceAccount(
				wso2SAReq,
				LightweightFlagEnum.NO
			)) as Wso2ServiceAccountRes;

		for (const sa of wso2SARes?.Response?.ServiceAccount ?? []) {
			for (const moli of sa?.ServiceAccountMoli ?? []) {
				for (const oli of moli?.ServiceAccountOli ?? []) {
					if (oli) {
						const isCustomerSubscribedToCS = req.ProductDetails?.find(
							product => {
								return (
									product.CyberSecurityPackPlanName === oli.ProductName &&
									oli.Status === 'Active'
								);
							}
						);

						if (isCustomerSubscribedToCS) {
							subscribedCsProductQuantity += Number(oli.Quantity ?? 0);
						}
					}
				}
			}
		}

		const totalCsProductQuantity: number =
			csProductQuantity + subscribedCsProductQuantity;
		if (totalCsProductQuantity < 100) {
			isMSRCheckPassed = true;
		}

		const res: AWCMSREligibilityRes = {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				IsMSRCheckPassed: isMSRCheckPassed,
				IsAWCCheckPassed: isAWCCheckPassed,
				IsSubmitToSlof: isSubmitToSlof
			}
		};

		return res;
	}
}

export default AwcMsrEligibility;
