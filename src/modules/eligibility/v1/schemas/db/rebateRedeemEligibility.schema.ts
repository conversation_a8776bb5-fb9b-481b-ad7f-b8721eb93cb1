import {
	boolean,
	integer,
	pgTable,
	text,
	timestamp
} from 'drizzle-orm/pg-core';

export const rebateTableSchema = pgTable('rebate_redeem_eligibility', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	TTNumber: text('tt_number').notNull(),
	CustomerName: text('customer_name').notNull(),
	CustomerId: text('customer_id').notNull(),
	AssetContactMobile: text('asset_contact_mobile'),
	ReportedByContactMobile: text('reported_by_contact_mobile'),
	BillingAccountNumber: text('billing_account_number').notNull(),
	BillingAccountName: text('billing_account_name').notNull(),
	RebateDescription: text('rebate_description'),
	RebateRedeemed: boolean('rebate_redeemed').notNull(),
	RedeemedDate: timestamp('redeemed_date', { mode: 'date' })
		.defaultNow()
		.notNull(),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectRebate = typeof rebateTableSchema.$inferSelect;
