import { type Static, t } from 'elysia';
import { wso2CustomerAccountsObjSchema } from '../../../../../integration/wso2/user/schemas/api/wso2CustomerAccount.schema';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const ssmEligibilityResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			IsBRNCustomer: t.<PERSON>(),
			IsExistingCustomer: t.Optional(t.<PERSON>()),
			SSMCheckStatus: t.Optional(t.String({ examples: ['VALID'] })),
			CustomerAccounts: wso2CustomerAccountsObjSchema
		})
	},
	{ description: "Customer's eligibility successfully retrieved." }
);

export type SSMEligibilityRes = Static<typeof ssmEligibilityResSchema>;
