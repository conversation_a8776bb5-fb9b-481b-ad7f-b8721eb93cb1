import { type Static, t } from 'elysia';
import { AddOnsRequestCategoryEnum } from '../../../../../enum/addOns.enum';
import { SystemNameEnum } from '../../../../../enum/wso2.enum';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const addOnsEligibilityReqSchema = t.Object({
	EncryptedBillAccNo: t.String({
		example: ['fh2490348gh9=43tqhjuwg9i8ht9qh3e0=-']
	}),
	SystemName: t.Enum(SystemNameEnum),
	Category: t.Enum(AddOnsRequestCategoryEnum),
	ServiceId: t.String()
});

export type AddOnsEligibilityReq = Static<typeof addOnsEligibilityReqSchema>;

export const addOnsEligibilityResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			IsEligible: t.<PERSON>an(),
			Reason: t.Nullable(t.String()),
			ReasonCode: t.Nullable(t.String()),
			CTAButtonText: t.Nullable(t.String()),
			TransactionId: t.Optional(t.Number()),
			SmartDevice: t.Optional(
				t.Object({
					RemainingCount: t.Number({
						description:
							'Total remaining number of eligible purchases for smart devices'
					}),
					TotalInContractCount: t.Number({
						description:
							'Total number of smart devices purchased during the contract period'
					}),
					IsBundleAllowed: t.Boolean({
						description:
							'Flag to indicate if purchasing bundled smart devices is allowed'
					}),
					IsSingleAllowed: t.Boolean({
						description:
							'Flag to indicate if purchasing a single smart device is allowed'
					})
				})
			),
			SmartHome: t.Optional(
				t.Object({
					RemainingCount: t.Number({
						description:
							'Total remaining number of eligible purchases for smart home devices'
					})
				})
			),
			MeshWifi: t.Optional(
				t.Object({
					PurchasedMeshWifiCount: t.Number({
						description: 'Total number of purchased mesh WiFi devices'
					}),
					PurchasedMeshWifi6Count: t.Number({
						description: 'Total number of purchased mesh WiFi 6 devices'
					}),
					PurchasedComboBoxCount: t.Number({
						description: 'Total number of purchased Mesh WiFi 6 combo boxes'
					}),
					MaxAllowedComboBoxCount: t.Number({
						description: 'Maximum number of allowed Mesh WiFi combo boxes'
					}),
					RemainingCount: t.Number({
						description:
							'Total remaining number of eligible purchases for mesh WiFi'
					})
				})
			),
			CloudGaming: t.Optional(
				t.Object({
					RemainingCount: t.Number({
						description:
							'Total remaining number of eligible purchases for cloud gaming'
					}),
					RemainingGamepad: t.Number({
						description:
							'Total remaining number of eligible purchases for gamepad controllers'
					})
				})
			),
			UPB: t.Optional(
				t.Object({
					RemainingCount: t.Number({
						description: 'Total remaining number of eligible purchases for UPB'
					})
				})
			),
			TvPack: t.Optional(
				t.Object({
					IsSubscriptionAllowed: t.Boolean({
						description:
							'Flag to indicate if subscription is allowed for TV pack'
					}),
					IsUpgradeAllowed: t.Boolean({
						description: 'Flag to indicate if upgrade is allowed for TV pack'
					})
				})
			)
		})
	},
	{
		description:
			"Customer's add ons subscription eligibility successfully retrieved."
	}
);

export type AddOnsEligibilityRes = Static<typeof addOnsEligibilityResSchema>;

const reportingParams = t.Object({
	category: t.Enum(AddOnsRequestCategoryEnum),
	serviceId: t.String(),
	decryptedBillAccNo: t.String(),
	serviceStartDate: t.Nullable(t.String()),
	cbpr: t.Nullable(t.String())
});

export type ReportingParams = Static<typeof reportingParams>;
