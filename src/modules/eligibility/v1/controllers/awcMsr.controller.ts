import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import Elysia from 'elysia';
import { getIdTokenInfo } from '../../../../middleware/uaid/util/utils';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type AWCMSREligibilityRes,
	awcMsrEligibilityResSchema,
	csAwcMsrEligibilityReqSchema,
	dmsAwcMsrEligibilityReqSchema
} from '../schemas/api/awcMsr.schema';
import AwcMsrEligibility from '../services/awcMsr.service';

const awcMsrV1Routes = new Elysia({ prefix: '/awc-msr' })
	.use(bearer())
	.resolve(async ctx => {
		const idTokenInfo = await getIdTokenInfo(ctx.bearer);
		return {
			AwcMsrEligibility: new AwcMsrEligibility(randomUUID(), idTokenInfo)
		};
	})
	.get(
		'/sme/dms',
		async (ctx): Promise<AWCMSREligibilityRes> => {
			return await ctx.AwcMsrEligibility.getDmsAwcMsrEligibility(ctx.query);
		},
		{
			detail: {
				description:
					"Check customer's eligibility by AWC and MSR information. This API is only available for SME under the DMS feature. <br><br> <b>Backend System:</b> NOVA SIEBEL",
				tags: ['Eligibility']
			},
			query: dmsAwcMsrEligibilityReqSchema,
			response: {
				200: awcMsrEligibilityResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.post(
		'/sme/cyber-security',
		async (ctx): Promise<AWCMSREligibilityRes> => {
			return await ctx.AwcMsrEligibility.getCyberSecurityAwcMsrEligibility(
				ctx.body
			);
		},
		{
			detail: {
				description:
					"Check customer's eligibility by AWC and MSR information. This API is only available for SME under the Cyber Security feature. <br><br> <b>Backend System:</b> NOVA SIEBEL",
				tags: ['Eligibility']
			},
			body: csAwcMsrEligibilityReqSchema,
			response: {
				200: awcMsrEligibilityResSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export default awcMsrV1Routes;
