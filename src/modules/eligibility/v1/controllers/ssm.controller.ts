import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import Elysia from 'elysia';
import { getIdTokenInfo } from '../../../../middleware/uaid/util/utils';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type SSMEligibilityRes,
	ssmEligibilityResSchema
} from '../schemas/api/ssm.schema';
import SSMEligibility from '../services/ssm.service';

const ssmV1Routes = new Elysia({ prefix: '/ssm' })
	.use(bearer())
	.resolve(async ctx => {
		const idTokenInfo = await getIdTokenInfo(ctx.bearer);
		return {
			SSMEligibility: new SSMEligibility(randomUUID(), idTokenInfo)
		};
	})
	.get(
		'/sme',
		async (ctx): Promise<SSMEligibilityRes> => {
			return await ctx.SSMEligibility.getSSMEligibility();
		},
		{
			detail: {
				description:
					"Check customer's eligibility by SSM information. This API is only available for SME. <br><br> <b>Backend System:</b> NOVA SIEBEL",
				tags: ['Eligibility']
			},
			response: {
				200: ssmEligibilityResSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export default ssmV1Routes;
