import { differenceInDays, differenceInMonths, parse } from 'date-fns';
import { and, eq, or } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { AddOnsRequestCategoryEnum } from '../../../../enum/addOns.enum';
import { OrderTypeEnum, ProgressStatusEnum } from '../../../../enum/order.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { IdTypeEnum } from '../../../../enum/user.enum';
import {
	LightweightFlagEnum,
	SystemNameEnum
} from '../../../../enum/wso2.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	Wso2QueryHardSoftBundleReq,
	Wso2QueryHardSoftBundleRes
} from '../../../../integration/wso2/eligibility/schemas/api/wso2QueryHardSoftBundle.schema';
import type {
	Wso2DeviceBundleReq,
	Wso2DeviceBundleRes
} from '../../../../integration/wso2/eligibility/schemas/api/wso2RetrieveBundleDevice.schema';
import type {
	Wso2OrderTrackingReq,
	Wso2OrderTrackingRes
} from '../../../../integration/wso2/record/schemas/api/wso2OrderTracking.schema';
import type {
	Wso2ConciseCustInfoReq,
	Wso2ConciseCustInfoRes
} from '../../../../integration/wso2/user/schemas/api/wso2ConciseCustInfo.schema';
import type { Wso2CustomerAccountRes } from '../../../../integration/wso2/user/schemas/api/wso2CustomerAccount.schema';
import type {
	Wso2ServiceAccountMoli,
	Wso2ServiceAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2ServiceAccount.schema';
import { UE_ERROR } from '../../../../middleware/error';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import {
	getMyTimeZoneDate,
	novaBillingProfile
} from '../../../../shared/common';
import type { NovaIcpBillingProfile } from '../../../../shared/schemas/api/novaBillingProfile.schema';
import { addonsCatalogueTableSchema } from '../../../catalogue/v1/schemas/db/addOnsCatalogue.schema';
import { orderableTxnHistoryTableSchema } from '../../../order/v1/schemas/db/orderable.schema';
import type {
	AddOnsEligibilityReq,
	AddOnsEligibilityRes,
	ReportingParams
} from '../schemas/api/addOns.schema';
import {
	type AddOnsReportingInfoData,
	addOnsReportingTableSchema
} from '../schemas/db/addOnsReporting.schema';
import {
	type SelectErrorMessages,
	errorMessagesTableSchema
} from '../schemas/db/errorMessages.schema';

class AddOnsHelper {
	private integrationId: string;
	private mwIntegration: MwIntegration;
	private db: NodePgDatabase;
	private idTokenInfo: IdTokenInfo;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo) {
		this.integrationId = integrationId;
		this.mwIntegration = new MwIntegration(integrationId);
		this.db = getDbInstance();
		this.idTokenInfo = idTokenInfo;
	}

	/**
	 * Performs common eligibility checks (e.g., open orders, platform, quota).
	 */
	async performCommonEligibilityChecks(
		req: AddOnsEligibilityReq,
		decryptedBillAccNo: string,
		hsiMoli: Wso2ServiceAccountMoli
	): Promise<AddOnsEligibilityRes | null> {
		const paymentRecord = await this.hasGoodPaymentRecord(
			req.ServiceId,
			decryptedBillAccNo,
			req.Category
		);

		if (paymentRecord?.reasonCode) {
			return await this.createErrorResponse(paymentRecord.reasonCode, {
				category: req.Category,
				serviceId: req.ServiceId,
				decryptedBillAccNo,
				serviceStartDate: hsiMoli.StartDate ?? null,
				cbpr: paymentRecord.cbpr
			});
		}

		// Check if the category requires base eligibility checks
		if (this.requiresBaseEligibilityCheck(req.Category)) {
			// Perform additional checks (e.g., platform, quota, installation order)
			const baseEligibilityError = await this.validateBaseEligibility(
				req,
				decryptedBillAccNo,
				hsiMoli
			);
			if (baseEligibilityError) return baseEligibilityError;
		}

		return null; // No errors
	}

	/**
	 * Checks if the category requires base eligibility checks.
	 * @param category - The category to check (e.g., Smart Device, Smart Home, Mesh Wifi, UPB).
	 */
	private requiresBaseEligibilityCheck(
		category: AddOnsRequestCategoryEnum
	): boolean {
		return [
			AddOnsRequestCategoryEnum.SMART_DEVICE,
			AddOnsRequestCategoryEnum.SMART_HOME,
			AddOnsRequestCategoryEnum.MESH_WIFI,
			AddOnsRequestCategoryEnum.UPB
		].includes(category);
	}

	/**
	 * Validates base eligibility (platform, quota, installation order).
	 */
	private async validateBaseEligibility(
		req: AddOnsEligibilityReq,
		decryptedBillAccNo: string,
		hsiMoli: Wso2ServiceAccountMoli
	): Promise<AddOnsEligibilityRes | null> {
		// Check if the platform is FTTH
		if (!this.isFtthPlatform(hsiMoli)) {
			return this.createErrorResponse('PLATFORM', {
				category: req.Category,
				serviceId: req.ServiceId,
				decryptedBillAccNo,
				serviceStartDate: hsiMoli.StartDate ?? null,
				cbpr: null
			});
		}

		// Check quota
		const deviceQuota = this.checkAvailableQuota(hsiMoli);
		if (deviceQuota.reasonCode) {
			return this.createErrorResponse(deviceQuota.reasonCode, {
				category: req.Category,
				serviceId: req.ServiceId,
				decryptedBillAccNo,
				serviceStartDate: hsiMoli.StartDate ?? null,
				cbpr: null
			});
		}

		// Check for recent installation orders
		if (this.hasRecentInstallationOrder(hsiMoli)) {
			return this.createErrorResponse('OPEN-ORDER-NI', {
				category: req.Category,
				serviceId: req.ServiceId,
				decryptedBillAccNo,
				serviceStartDate: hsiMoli.StartDate ?? null,
				cbpr: null
			});
		}

		return null; // No errors
	}

	/**
	 * Checks if the customer has an open order in siebel.
	 */
	async checkOpenOrderInSiebel(
		billingAccountNo: string
	): Promise<AddOnsEligibilityRes | null> {
		const wso2Req: Wso2OrderTrackingReq = {
			OrderTracking: {
				OrderList: {
					CustomerID: this.idTokenInfo.IdValue,
					IdType: this.idTokenInfo.IdType
				}
			}
		};

		const wso2Res: Wso2OrderTrackingRes =
			await this.mwIntegration.Wso2RecordIntegration.getWso2OrderTracking(
				wso2Req
			);

		if (
			wso2Res.Response.OrderList?.['TmOrderEntry-OrdersIntegration-NOVA'] &&
			wso2Res.Response.OrderList?.['TmOrderEntry-OrdersIntegration-NOVA']
				.length > 0
		) {
			const hasOpenOrder: boolean = wso2Res.Response.OrderList?.[
				'TmOrderEntry-OrdersIntegration-NOVA'
			].some(
				novaOrders =>
					novaOrders.BillingAccountNumber === billingAccountNo &&
					novaOrders.Status !== 'Completed' &&
					novaOrders.Status !== 'Cancelled'
			);

			if (hasOpenOrder) {
				return await this.createErrorResponse('OPEN-ORDER');
			}
		}

		return null;
	}

	/**
	 * Checks if the customer has an open order in DB.
	 */
	async checkOpenOrderInDb(
		billingAccountNo: string
	): Promise<AddOnsEligibilityRes | null> {
		const dbRes = await this.db
			.select()
			.from(orderableTxnHistoryTableSchema)
			.where(
				and(
					eq(orderableTxnHistoryTableSchema.BillingAccountNo, billingAccountNo),
					or(
						eq(
							orderableTxnHistoryTableSchema.OrderStatus,
							ProgressStatusEnum.SUBMITTED
						),
						eq(
							orderableTxnHistoryTableSchema.OrderStatus,
							ProgressStatusEnum.INPROGRESS
						)
					),
					eq(orderableTxnHistoryTableSchema.OrderType, OrderTypeEnum.ADD_ONS)
				)
			)
			.execute();

		if (dbRes.length > 0) {
			return await this.createErrorResponse('OPEN-ORDER');
		}

		return null;
	}

	/**
	 * Fetches service account details.
	 */
	async fetchServiceAccount(
		decryptedBillAccNo: string
	): Promise<Wso2ServiceAccountRes> {
		const wso2SARes =
			await this.mwIntegration.Wso2UserIntegration.getWso2ServiceAccount(
				{
					idType: this.idTokenInfo.IdType,
					idValue: this.idTokenInfo.IdValue,
					BillingAccountNo: decryptedBillAccNo,
					SystemName: SystemNameEnum.NOVA
				},
				LightweightFlagEnum.NO
			);

		if (wso2SARes?.Response?.ServiceAccount?.length === 0) {
			throw new UE_ERROR(
				'You have no access to this service',
				StatusCodeEnum.UNAUTHORIZED_ERROR,
				{
					integrationId: this.integrationId,
					response: null
				}
			);
		}

		return wso2SARes;
	}

	/**
	 * Retrieves High-Speed Internet Moli.
	 */
	getHighSpeedInternetMoli(
		wso2SARes: Wso2ServiceAccountRes
	): Wso2ServiceAccountMoli | null {
		const hsiMoli =
			wso2SARes?.Response?.ServiceAccount?.flatMap(
				sa => sa.ServiceAccountMoli ?? []
			).find(
				moli =>
					moli.Status === 'Active' &&
					(moli.ProductName?.includes('Residential High Speed Internet') ||
						moli.ProductName?.includes('Business High Speed Internet'))
			) ?? null;

		return hsiMoli;
	}

	async getEligibleSmartDevice(
		req: AddOnsEligibilityReq,
		decryptedBillAccNo: string,
		hsiMoli: Wso2ServiceAccountMoli
	): Promise<AddOnsEligibilityRes> {
		const maxSmartDevice = 2;
		let totalInContract = 0;
		let totalSmartDevice = 0;
		let isInContractPeriod = false;
		let isInCommitmentPeriod = false;
		let isSmartDeviceBundle = false;
		// TODO: move to database
		const upgradePlan = [
			'unifi 30Mbps - Upgrade 100Mbps (Try Me) Ultimate',
			'unifi 30Mbps - Upgrade 100Mbps (Try Me) VAR',
			'unifi 30Mbps - Upgrade 100Mbps (Extra Exclusive Promo) VAR',
			'unifi 30Mbps - Upgrade 100Mbps (Try Me)',
			'unifi 30Mbps - Upgrade 100Mbps (Try Me Premium)',
			'unifi 30Mbps - Upgrade 100Mbps (Exclusive Promo unifi Plus Box)',
			'unifi 30Mbps - Upgrade 100Mbps (Exclusive Promo)',
			'unifi 30Mbps - Upgrade 100Mbps Ultimate Extraordinary',
			'unifi 30Mbps - Upgrade 100Mbps Premium Extraordinary'
		];
		const reportingParam: ReportingParams = {
			category: req.Category,
			serviceId: req.ServiceId,
			decryptedBillAccNo,
			serviceStartDate: hsiMoli.StartDate ?? null,
			cbpr: 'A'
		};

		const citizenship = await this.isMalaysianCitizen(
			req.Category,
			req.ServiceId,
			decryptedBillAccNo,
			hsiMoli.StartDate ?? null
		);
		if (citizenship) {
			return citizenship;
		}

		// Plan compatibility
		const isUpgradePlan = upgradePlan.some(planName =>
			hsiMoli.ProdPromName?.includes(planName)
		);

		if (isUpgradePlan) {
			return await this.createErrorResponse('GENERAL', reportingParam);
		}

		// Service Tenure
		// Served a minimum of 6 months service tenure with Unifi Home subscriptions
		const serviceStartDateDifference = this.getStartDateDifference(
			hsiMoli.StartDate ?? ''
		);

		if (
			serviceStartDateDifference &&
			serviceStartDateDifference.differenceInMonths <= 6
		) {
			return await this.createErrorResponse('TENURE-SMD', reportingParam);
		}

		for (const oli of hsiMoli.ServiceAccountOli ?? []) {
			if (
				oli?.ProductName?.includes('Device/Service') &&
				oli.Type === 'Commitment'
			) {
				// 4. Number of eligible smart devices per contract:
				// 		- 1 Smart device while contract is active.
				// 		- Existing customers can purchase a maximum of one (1) smart device under one Unifi account.
				// 		- Existing customer can add another device (1) once they have completed their contract for their current smart device add-on (24/36 months contract)
				if (oli.SmartDeviceFlag === 'Y') {
					++totalSmartDevice;
					const contractPeriod = oli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
						'TmAssetMgmt-AssetXaIntegration'
					]?.find(asset => asset.Name === 'Contract Period');

					if (oli.StartDate) {
						const contractStartDate = new Date(oli.StartDate);
						const currentDate = getMyTimeZoneDate();

						// Calculate the contract end date
						const contractEndDate = new Date(contractStartDate);
						contractEndDate.setMonth(
							contractEndDate.getMonth() + Number(contractPeriod?.Value ?? 24)
						);

						const contract6Month = new Date(contractStartDate);
						contract6Month.setMonth(contractEndDate.getMonth() + 6);

						isInCommitmentPeriod = currentDate <= contract6Month;
						if (isInCommitmentPeriod) {
							return await this.createErrorResponse(
								'ACTIVE-SMD',
								reportingParam
							);
						}

						// Check if the current date is before the contract end date
						isInContractPeriod = currentDate <= contractEndDate;
						if (isInContractPeriod) {
							++totalInContract;
						}
					}
				}
			}

			if (
				oli?.ProductName?.includes('Device/Service') &&
				oli.Type === 'Service'
			) {
				// Bundle plan (PS5 & TV bundle)
				// Can add up until 2 Smart devices within contract is active (more than 6 months tenure)
				// Existing customer with Bundle Smart Device still within contract, is not allowed to add any new smart device (single or bundle) until contract ends.
				isSmartDeviceBundle =
					oli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
						'TmAssetMgmt-AssetXaIntegration'
					]?.some(
						device =>
							device.Value?.includes('PlayStation 5 + SHARP') ||
							device.Value?.includes('PlayStation 5 + LG')
					) || false;
			}

			// 5. Check eligible speed
			// (Broadband speed => 100 Mbps (Residential High-Speed Internet))
			// (Broadband speed => 30 Mbps (Business High-Speed Internet))
			if (oli?.Type === 'Speed') {
				const downloadSpeed = oli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
					'TmAssetMgmt-AssetXaIntegration'
				]?.find(asset => asset.Name === 'Download Speed');
				const speedInNumber = Number(
					(downloadSpeed?.Value ?? '0').replaceAll(' Kbps', '')
				);

				const isSpeedInvalid =
					this.idTokenInfo.IdType === IdTypeEnum.BRN ||
					this.idTokenInfo.IdType === IdTypeEnum.NON_BRN
						? speedInNumber < 35840
						: speedInNumber < 112640;

				if (isSpeedInvalid) {
					return await this.createErrorResponse('SPEED-SMD', reportingParam);
				}
			}
		}

		// Existing customer with Bundle Smart Device still within contract,
		// is not allowed to add any new smart device (single or bundle) until contract ends.
		if (
			(isSmartDeviceBundle && totalInContract === 1) ||
			totalInContract >= maxSmartDevice
		) {
			return await this.createErrorResponse('LIMIT-SMD', reportingParam);
		}

		// 1. Existing customer with one Single smart device within device contract > 6 months is allowed to add another new Single Smart Device But not allowed to add new Bundle Smart Device.
		// 2. Existing customer with one Single smart device within device contract <= 6 months is not allowed to add on any new smart device (single or bundle)
		// 3. Existing customer with Bundle Smart Device still within contract, is not allowed to add any new smart device (single or bundle) until contract ends.
		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				IsEligible: true,
				Reason: null,
				ReasonCode: null,
				CTAButtonText: null,
				SmartDevice: {
					RemainingCount: maxSmartDevice - totalSmartDevice,
					TotalInContractCount: totalInContract,
					IsBundleAllowed: totalInContract === 0,
					IsSingleAllowed:
						!isSmartDeviceBundle ||
						(isSmartDeviceBundle && totalInContract === 0)
				}
			}
		};
	}

	async getEligibleSmartHome(
		req: AddOnsEligibilityReq,
		decryptedBillAccNo: string,
		hsiMoli: Wso2ServiceAccountMoli
	): Promise<AddOnsEligibilityRes> {
		const reportingParam: ReportingParams = {
			category: req.Category,
			serviceId: req.ServiceId,
			decryptedBillAccNo,
			serviceStartDate: hsiMoli.StartDate ?? null,
			cbpr: 'A' // Hardcoded as 'A' since it passes the early payment record check
		};

		const citizenship = await this.isMalaysianCitizen(
			req.Category,
			req.ServiceId,
			decryptedBillAccNo,
			hsiMoli.StartDate ?? null
		);
		if (citizenship) {
			return citizenship;
		}

		// Add on up to maximum of five (5) Smart Home Packs/devices per Unifi Home service subscription
		let smartHomeLimit = 5;

		const smartHomeList = await this.db
			.select()
			.from(addonsCatalogueTableSchema)
			.where(
				eq(
					addonsCatalogueTableSchema.Category,
					AddOnsRequestCategoryEnum.SMART_HOME
				)
			);

		// Service Tenure
		// Served a minimum of 6 months service tenure with Unifi Home subscriptions
		const serviceStartDateDifference = this.getStartDateDifference(
			hsiMoli.StartDate ?? ''
		);

		if (
			serviceStartDateDifference &&
			serviceStartDateDifference.differenceInMonths <= 6
		) {
			return await this.createErrorResponse('TENURE-SH', reportingParam);
		}
		for (const oli of hsiMoli.ServiceAccountOli ?? []) {
			if (
				oli?.ProductName?.includes('Device/Service') &&
				oli.Type === 'Service'
			) {
				const leasingPeriod = oli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
					'TmAssetMgmt-AssetXaIntegration'
				]?.find(asset => asset.Name === 'Leasing Period');

				const deviceName = oli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
					'TmAssetMgmt-AssetXaIntegration'
				]?.find(asset => asset.Name === 'Device Name');

				const isSmartHome = smartHomeList.some(
					sh => sh.Name === deviceName?.Value
				);

				if (oli.StartDate && isSmartHome) {
					--smartHomeLimit;
					const contractStartDate = new Date(oli.StartDate);
					const currentDate = getMyTimeZoneDate();

					// Calculate the contract end date
					const contractEndDate = new Date(contractStartDate);
					contractEndDate.setMonth(
						contractEndDate.getMonth() + Number(leasingPeriod?.Value ?? 24)
					);

					const contract6Month = new Date(contractStartDate);
					contract6Month.setMonth(contractEndDate.getMonth() + 6);

					// Check if the current date is before the contract end date
					if (currentDate <= contractEndDate) {
						return await this.createErrorResponse('ACTIVE-SH', reportingParam);
					}
				}
			}

			// Check eligible speed (30Mbps – 2Gbps)
			if (oli?.Type === 'Speed') {
				const downloadSpeed = oli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
					'TmAssetMgmt-AssetXaIntegration'
				]?.find(asset => asset.Name === 'Download Speed');

				const speedInNumber = Number(
					(downloadSpeed?.Value ?? '0').replaceAll(' Kbps', '')
				);

				if (speedInNumber < 35840) {
					return await this.createErrorResponse('SPEED-SH', reportingParam);
				}
			}
		}

		if (smartHomeLimit === 0) {
			return await this.createErrorResponse('LIMIT-SH', reportingParam);
		}

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				IsEligible: true,
				Reason: null,
				ReasonCode: null,
				CTAButtonText: null,
				SmartHome: {
					RemainingCount: smartHomeLimit
				}
			}
		};
	}

	async handleMeshWifi(
		hsiMoli: Wso2ServiceAccountMoli
	): Promise<AddOnsEligibilityRes> {
		// Maximum 5 mesh per service, and maximum 2 mesh combobox out of 5 mesh
		const meshLimit = 5;
		const maxComboBox = 2;
		let totalMeshWifi6 = 0;
		let totalMeshWifi = 0;
		let isRG6Combo = false;

		for (const oli of hsiMoli.ServiceAccountOli ?? []) {
			if (
				oli?.ProductName?.includes('Device/Service') &&
				oli.Type === 'Service'
			) {
				const meshWifi = oli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
					'TmAssetMgmt-AssetXaIntegration'
				]?.find(
					asset =>
						asset.Value?.toLowerCase().includes('mesh wifi') &&
						!asset.Value?.toLowerCase().includes('mesh wifi 6')
				);

				totalMeshWifi += meshWifi ? Number(oli.Quantity) : 0;
			}

			// Check eligible speed (>= 100Mbps)
			if (oli?.Type === 'Speed') {
				const downloadSpeed = oli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
					'TmAssetMgmt-AssetXaIntegration'
				]?.find(asset => asset.Name === 'Download Speed');
				const speedInNumber = Number(
					(downloadSpeed?.Value ?? '0').replaceAll(' Kbps', '')
				);

				if (speedInNumber < 112640) {
					return this.createErrorResponse('SPEED-MESH');
				}
			}

			// Check RG Version and RG Mode
			if (oli.ProductName?.includes('Wi-Fi (RG)') && oli.Type === 'Equipment') {
				if (
					oli.TMRGVersion?.includes('RG6') &&
					oli.TMRGMode?.includes('Combo')
				) {
					isRG6Combo = true;
				}
			}

			// * info: how to identify ala carte and bundle devices?
			// Starter Pack (Bundle) - Recurring Charge (RC)
			// Premium Pack (Bundle) - Recurring Charge(RC)
			// Ala Carte - One time charge (OTC)
			if (
				oli.ProductName?.includes('MESH Wi-Fi 6 OTC') ||
				oli.ProductName?.includes('MESH Wi-Fi 6 RC')
			) {
				++totalMeshWifi6;
			}
		}

		const totalRemainingMesh = meshLimit - totalMeshWifi - totalMeshWifi6;
		if (totalRemainingMesh === 0) {
			return this.createErrorResponse('LIMIT-MESH');
		}

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				IsEligible: true,
				Reason: null,
				ReasonCode: null,
				CTAButtonText: null,
				MeshWifi: {
					PurchasedMeshWifiCount: totalMeshWifi,
					PurchasedMeshWifi6Count: isRG6Combo ? 0 : totalMeshWifi6,
					PurchasedComboBoxCount: isRG6Combo ? totalMeshWifi6 : 0,
					MaxAllowedComboBoxCount: isRG6Combo ? maxComboBox : 0,
					RemainingCount: totalRemainingMesh
				}
			}
		};
	}

	async handleUpb(
		wso2SARes: Wso2ServiceAccountRes,
		hsiMoli: Wso2ServiceAccountMoli
	): Promise<AddOnsEligibilityRes> {
		const unifiPlayTvMoli = this.getUnifiPlayTvMoli(wso2SARes);
		if (unifiPlayTvMoli === null) {
			return this.createErrorResponse('UNIFI-TV');
		}
		// Maximum 3 UPB per service.
		let totalUpb = 0;
		const upbLimit = 3;

		for (const oli of hsiMoli.ServiceAccountOli ?? []) {
			// Check eligible speed (>= 100Mbps)
			if (oli?.Type === 'Speed') {
				const downloadSpeed = oli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
					'TmAssetMgmt-AssetXaIntegration'
				]?.find(asset => asset.Name === 'Download Speed');

				const speedInNumber = Number(
					(downloadSpeed?.Value ?? '0').replaceAll(' Kbps', '')
				);

				if (speedInNumber < 112640) {
					return this.createErrorResponse('SPEED-UPB');
				}
			}
		}

		totalUpb +=
			unifiPlayTvMoli.ServiceAccountOli?.filter(oli =>
				oli.ProductName?.includes('unifi Plus Box Add On')
			).length ?? 0;

		if (totalUpb >= 3) {
			return this.createErrorResponse('LIMIT-UPB');
		}

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				IsEligible: upbLimit - totalUpb > 0,
				Reason: null,
				ReasonCode: null,
				CTAButtonText: null,
				UPB: {
					RemainingCount: upbLimit - totalUpb
				}
			}
		};
	}

	async handleBlacknut(
		billingAccountNo: string,
		hsiMoli: Wso2ServiceAccountMoli,
		wso2SARes: Wso2ServiceAccountRes
	): Promise<AddOnsEligibilityRes> {
		const productName =
			wso2SARes?.Response?.ServiceAccount?.find(sa => sa.Status === 'Active')
				?.ProductName ?? null;

		if (productName === null) {
			return this.createErrorResponse('RETRY');
		}

		const maxGamepadController = 2;
		const maxBlacknut = 1;
		const deviceQuota = this.checkAvailableQuota(hsiMoli);
		if (deviceQuota.reasonCode) {
			return this.createErrorResponse(deviceQuota.reasonCode);
		}

		for (const oli of hsiMoli.ServiceAccountOli ?? []) {
			if (
				oli?.ProductName?.includes('Device/Service') &&
				oli.Type === 'Service'
			) {
				if (
					oli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
						'TmAssetMgmt-AssetXaIntegration'
					]?.some(device => device.Value?.includes('Blacknut'))
				) {
					return this.createErrorResponse('LIMIT-CG');
				}
			}
		}

		// Maximum gamepad controller is 2
		// Service need to have Cloud Gaming plan (Device bundle) to add Cloud Gaming addon
		// Customer only eligible to purchase 1 Cloud Gaming plan per Billing email address (refer Matrix 1)
		let targetBillingEmailAddress = null;
		const billingAccounts: {
			billingAccountNo: string;
			billingEmail: string | null;
		}[] = [];
		const wso2CARes: Wso2CustomerAccountRes =
			await this.mwIntegration.Wso2UserIntegration.getWso2CustomerAccount(
				{
					idType: this.idTokenInfo.IdType,
					idValue: this.idTokenInfo.IdValue
				},
				LightweightFlagEnum.YES
			);

		for (const ca of wso2CARes.Response?.CustomerAccounts ?? []) {
			if (ca.SystemName === SystemNameEnum.NOVA) {
				for (const ba of ca.BillingAccounts ?? []) {
					if (ba.AccountNumber) {
						const emailBillTo =
							ba?.InvoiceProfileIntegration?.EmailBillTo ?? null;
						if (ba.AccountNumber === billingAccountNo)
							targetBillingEmailAddress = emailBillTo;

						if (ba.AccountNumber !== billingAccountNo) {
							billingAccounts.push({
								billingAccountNo: ba.AccountNumber,
								billingEmail: emailBillTo
							});
						}
					}
				}
			}
		}

		for (const ba of billingAccounts) {
			const wso2SARes = await this.fetchServiceAccount(ba.billingAccountNo);
			const hsiMoli = this.getHighSpeedInternetMoli(wso2SARes);
			if (
				hsiMoli?.ServiceAccountOli?.some(
					oli =>
						oli?.ProductName?.includes('Device/Service') &&
						oli.Type === 'Service' &&
						!oli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
							'TmAssetMgmt-AssetXaIntegration'
						]?.some(
							device =>
								device.Name === 'Device Name' &&
								device.Value?.includes('Blacknut')
						)
				) &&
				ba.billingEmail === targetBillingEmailAddress
			) {
				return this.createErrorResponse('EMAIL-CG');
			}
		}

		const wso2DeviceBundleReq: Wso2DeviceBundleReq = {
			DeviceBundleRetrieveRequest: {
				TmDeviceBundle: {
					TMActiveFlag: 'Y',
					TMBundleName: productName
				}
			}
		};

		const wso2DeviceBundleRes: Wso2DeviceBundleRes =
			await this.mwIntegration.Wso2EligibilityIntegration.getWso2DeviceBundle(
				wso2DeviceBundleReq
			);
		const hasBlacknutBundle =
			wso2DeviceBundleRes?.Response?.DeviceBundleRetrieveResponse?.TmDeviceBundle.some(
				bundle => bundle.TMDeviceName?.toLowerCase() === 'blacknut'
			);
		if (!hasBlacknutBundle) {
			return this.createErrorResponse('GENERAL');
		}

		// The maximum number of gamepad controllers is 2
		// The maximum number of Blacknut devices is 1
		// If the number of total devices is greater than 7, the maximum number of gamepad controllers is 1
		// If the number of total devices is greater than 8, the maximum number of gamepad controllers is 0
		// The priority is given to the number of blacknut devices
		const remainingQuotaAfterBlacknut =
			deviceQuota.availableQuota - maxBlacknut;
		const remainingGamepad =
			remainingQuotaAfterBlacknut >= maxGamepadController
				? maxGamepadController
				: remainingQuotaAfterBlacknut === 1
					? 1
					: 0;

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				IsEligible: true,
				Reason: null,
				ReasonCode: null,
				CTAButtonText: null,
				CloudGaming: {
					RemainingCount: maxBlacknut,
					RemainingGamepad: remainingGamepad
				}
			}
		};
	}

	async handleTvPack(
		wso2SARes: Wso2ServiceAccountRes
	): Promise<AddOnsEligibilityRes> {
		let hasTvPack = false;
		let hasContentDiscount = false;
		let hasHardBundle = false;
		let hasNonUpgradableTvPack = false;
		let isInInstallationPeriod = false;
		let pmeNumber: string | null = null;
		for (const sa of wso2SARes?.Response?.ServiceAccount ?? []) {
			pmeNumber = sa?.ProductPartNumber ?? null;

			for (const moli of sa.ServiceAccountMoli ?? []) {
				if (moli.ProductName?.includes('High Speed Internet')) {
					if (this.hasRecentInstallationOrder(moli)) {
						isInInstallationPeriod = true;
					}
				}

				if (moli.ProductName?.includes('unifi TV')) {
					for (const oli of moli.ServiceAccountOli ?? []) {
						if (oli.Status === 'Active') {
							if (oli.Type === 'HyppTV Package') {
								// Check for TV pack
								hasTvPack = true;

								// Check for non upgradeable tv pack
								if (oli.ProductName?.toLowerCase().includes('staff')) {
									hasNonUpgradableTvPack = true;
								}
							}

							// Check for content discount
							if (
								oli?.Type === 'Content Discount' &&
								oli.ProductName?.toLowerCase().includes('content discount')
							) {
								hasContentDiscount = true;
							}
						}
					}
				}
			}
		}

		if (pmeNumber) {
			const wso2QueryHardSoftBundleReq: Wso2QueryHardSoftBundleReq = {
				pmeNumber
			};

			const wso2QueryHardSoftBundleRes: Wso2QueryHardSoftBundleRes =
				await this.mwIntegration.Wso2EligibilityIntegration.getWso2QueryHardSoftBundle(
					wso2QueryHardSoftBundleReq
				);

			// Check if any item in the datalist satisfies the hard bundle condition
			hasHardBundle = wso2QueryHardSoftBundleRes.datalist.some(
				data => (data.oliMinQty ?? 0) >= 1
			);
		}

		// Determine eligibility
		const isSubscriptionAllowed =
			!hasTvPack &&
			!isInInstallationPeriod &&
			!hasHardBundle &&
			!hasContentDiscount;

		const isUpgradeAllowed =
			hasTvPack &&
			!hasNonUpgradableTvPack &&
			!isInInstallationPeriod &&
			!hasHardBundle &&
			!hasContentDiscount;

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				IsEligible: isSubscriptionAllowed && isUpgradeAllowed,
				Reason: null,
				ReasonCode: null,
				CTAButtonText: null,
				TvPack: {
					IsSubscriptionAllowed: isSubscriptionAllowed,
					IsUpgradeAllowed: isUpgradeAllowed
				}
			}
		};
	}

	/**
	 * Checks if the platform is FTTH.
	 */
	private isFtthPlatform(hsiMoli: Wso2ServiceAccountMoli): boolean {
		return (
			hsiMoli?.[
				'TmCutAssetMgmt-ServiceMeterIntegration'
			]?.TMAccessTechnology?.toUpperCase() === 'FTTH'
		);
	}

	/**
	 * Checks if there is a recent installation order within 14 days.
	 */
	private hasRecentInstallationOrder(hsiMoli: Wso2ServiceAccountMoli): boolean {
		return (hsiMoli.ServiceAccountOli ?? []).some(oli => {
			const startDateDifference = this.getStartDateDifference(
				oli.StartDate ?? ''
			);
			return startDateDifference && startDateDifference.differenceInDays <= 14;
		});
	}

	/**
	 * Checks if the service account is active and has a Unifi Play TV.
	 */
	private getUnifiPlayTvMoli(
		wso2ServiceAccounts: Wso2ServiceAccountRes
	): Wso2ServiceAccountMoli | null {
		for (const sa of wso2ServiceAccounts?.Response?.ServiceAccount ?? []) {
			if (sa.Status === 'Active') {
				for (const moli of sa.ServiceAccountMoli ?? []) {
					if (
						moli.Status === 'Active' &&
						(moli.ProductPartNumber?.includes('PR007670') ||
							moli.ProductPartNumber?.includes('PR000200'))
					) {
						return moli;
					}
				}
			}
		}
		return null;
	}

	/**
	 * Checks if the billing account has a good payment record
	 * (e.g., CBPR is A, No advance payment)
	 */
	private async hasGoodPaymentRecord(
		serviceId: string,
		billingAccountNo: string,
		category: AddOnsRequestCategoryEnum
	): Promise<{
		reasonCode: string;
		cbpr: string | null;
	} | null> {
		const wso2Req: Wso2ConciseCustInfoReq = {
			requestHeader: {
				requestId: `XE-${Math.random().toString(36).slice(2)}`,
				eventName: 'evOXECONCISECustInfo'
			},
			kciRequest: {
				customerId: '',
				serviceNo: '',
				serviceId: serviceId,
				account: billingAccountNo
			}
		};
		const wso2Res: Wso2ConciseCustInfoRes =
			await this.mwIntegration.Wso2UserIntegration.getWso2ConciseCustInfo(
				wso2Req
			);

		if (
			wso2Res.kciResponse === null ||
			wso2Res.kciResponse.kciresponseData === null ||
			wso2Res.kciResponse.kciresponseData.length === 0
		) {
			return {
				reasonCode: 'PAYMENT-CONCISE-EMPTY',
				cbpr: null
			};
		}

		const conciseResponse = wso2Res.kciResponse.kciresponseData
			.filter(item => item !== null) // Remove nulls
			.find(() => true); // Pick the first element

		if (conciseResponse && 'Y' === conciseResponse.advancepaymentflag) {
			return {
				reasonCode: 'PAYMENT-ADVANCE',
				cbpr: conciseResponse.cpbr
			};
		}

		if (
			conciseResponse &&
			conciseResponse.cpbr !== 'A' &&
			(category === AddOnsRequestCategoryEnum.SMART_DEVICE ||
				category === AddOnsRequestCategoryEnum.SMART_HOME)
		) {
			const reasonCode =
				category === AddOnsRequestCategoryEnum.SMART_DEVICE
					? 'PAYMENT-SMD'
					: 'PAYMENT-SH';

			return {
				reasonCode,
				cbpr: conciseResponse.cpbr
			};
		}

		return null;
	}

	private getStartDateDifference(
		startDate: string
	): { differenceInDays: number; differenceInMonths: number } | null {
		if (!startDate || Number.isNaN(Date.parse(startDate))) {
			return null;
		}

		const currentDate = getMyTimeZoneDate();
		const completedOrderDate = parse(
			startDate,
			'yyyy-MM-dd HH:mm:ss',
			currentDate
		);

		return {
			differenceInDays: differenceInDays(currentDate, completedOrderDate),
			differenceInMonths: differenceInMonths(currentDate, completedOrderDate)
		};
	}

	private async isMalaysianCitizen(
		category: AddOnsRequestCategoryEnum,
		serviceId: string,
		decryptedBillAccNo: string,
		serviceStartDate: string | null
	): Promise<AddOnsEligibilityRes | null> {
		if (this.idTokenInfo.IdType === IdTypeEnum.PASSPORT) {
			await this.createErrorResponse('GENERAL', {
				category,
				serviceId,
				decryptedBillAccNo,
				serviceStartDate,
				cbpr: null
			});
		}

		return null;
	}

	private checkAvailableQuota(wso2ServiceAccountMoli: Wso2ServiceAccountMoli): {
		availableQuota: number;
		reasonCode: string | null;
	} {
		const quotaLimit = 10;
		const totalDevices =
			wso2ServiceAccountMoli.ServiceAccountOli?.reduce(
				(count, oli) =>
					count + (oli?.ProductName?.includes('Device/Service') ? 1 : 0),
				0
			) ?? 0;

		return {
			availableQuota: quotaLimit - totalDevices,
			reasonCode: totalDevices >= quotaLimit ? 'LIMIT-ADDONS' : null
		};
	}

	async createErrorResponse(
		reasonCode: string,
		reportingParam?: ReportingParams
	): Promise<AddOnsEligibilityRes> {
		const error = await this.getErrorMessage(reasonCode);
		if (reportingParam) {
			const reportingDataParams = {
				category: reportingParam.category,
				idType: this.idTokenInfo.IdType,
				idValue: this.idTokenInfo.IdValue,
				billingAccountNo: reportingParam.decryptedBillAccNo,
				serviceId: reportingParam.serviceId,
				serviceStartDate: reportingParam.serviceStartDate,
				remarks: error.Message,
				cbpr: reportingParam.cbpr
			};
			const transactionId =
				await this.generateReportingData(reportingDataParams);
			return {
				Success: true,
				Code: StatusCodeEnum.OK,
				IntegrationId: this.integrationId,
				Response: {
					IsEligible: false,
					Reason: error.Message,
					ReasonCode: error.Code,
					TransactionId: transactionId,
					CTAButtonText: error.CTAButtonText
				}
			};
		}

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				IsEligible: false,
				Reason: error.Message,
				ReasonCode: error.Code,
				CTAButtonText: error.CTAButtonText
			}
		};
	}

	private async getErrorMessage(code: string): Promise<SelectErrorMessages> {
		const [errorMessage] = await this.db
			.select()
			.from(errorMessagesTableSchema)
			.where(eq(errorMessagesTableSchema.Code, code))
			.execute();

		if (errorMessage) {
			return errorMessage;
		}

		//  return generic error message if specific one is not found
		return {
			Id: 0,
			Code: code,
			Title: 'Try again later',
			Message:
				"We're temporarily unable to retrieve your plan information. Please try again in a few minutes.",
			CTAButtonText: 'Try Again Later',
			CTAUrl: null,
			CreatedAt: getMyTimeZoneDate(),
			UpdatedAt: getMyTimeZoneDate()
		};
	}

	private async generateReportingData(params: {
		category: AddOnsRequestCategoryEnum;
		idType: string;
		idValue: string;
		billingAccountNo: string;
		serviceId: string;
		serviceStartDate: string | null;
		remarks: string;
		cbpr: string | null;
	}) {
		const billingProfile: NovaIcpBillingProfile = await novaBillingProfile(
			this.integrationId,
			params.billingAccountNo
		);

		const data: AddOnsReportingInfoData = {
			IdType: params.idType,
			IdValue: params.idValue,
			BillingAccountNo: params.billingAccountNo,
			BillingName: billingProfile.AccountName,
			BillingEmail: billingProfile.AccountEmail,
			BillingContactNo: billingProfile.AccountContactNo,
			LoginId: params.serviceId,
			ServiceStartDate: params.serviceStartDate,
			CBPR: params.cbpr
		};

		const [transaction] = await this.db
			.insert(addOnsReportingTableSchema)
			.values({
				IdType: params.idType,
				IdValue: params.idValue,
				OrderStatus: params.remarks === null ? 'Not Submitted' : 'Failed',
				Category: params.category,
				Remarks: params.remarks,
				InfoData: data,
				ErrorMessage:
					billingProfile.AccountName === 'N/A'
						? 'Unable to retrieve billing details'
						: null
			})
			.returning();

		return transaction.Id;
	}
}

export default AddOnsHelper;
