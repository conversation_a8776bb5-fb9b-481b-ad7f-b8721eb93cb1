import Elysia from 'elysia';
import addOnsV1Routes from './v1/controllers/addOns.controller';
import awcMsrV1Routes from './v1/controllers/awcMsr.controller';
import fsuV1Routes from './v1/controllers/fsu.controller';
import rebateV1Routes from './v1/controllers/rebate.controller';
import srV1Routes from './v1/controllers/sr.controller';
import ssmV1Routes from './v1/controllers/ssm.controller';
import surveyV1Routes from './v1/controllers/survey.controller';

export const privateEligibilityV1Routes = new Elysia({
	prefix: '/v1/eligibility'
})
	.use(fsuV1Routes)
	.use(addOnsV1Routes)
	.use(srV1Routes)
	.use(awcMsrV1Routes)
	.use(ssmV1Routes)
	.use(rebateV1Routes)
	.use(surveyV1Routes);
