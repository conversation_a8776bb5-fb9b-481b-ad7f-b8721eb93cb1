import { type Static, t } from 'elysia';
import { wso2CustomerAccountsInArraySchema } from '../../../../../integration/wso2/user/schemas/api/wso2CustomerAccount.schema';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const awcMsrEligibilityResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			IsMSRCheckPassed: t.<PERSON>(),
			IsAWCCheckPassed: t.<PERSON>(),
			IsSubmitToSlof: t.<PERSON>()
		})
	},
	{ description: "Customer's eligibility successfully retrieved." }
);

export type AWCMSREligibilityRes = Static<typeof awcMsrEligibilityResSchema>;

export const awcMsrReqSchema = t.Object({
	OrderId: t.String()
});

export type AWCMSRReq = Static<typeof awcMsrReqSchema>;

export const awcMsrResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Any()
	},
	{ description: "Customer's eligibility successfully retrieved." }
);

export type AWCMSRRes = Static<typeof awcMsrResSchema>;

export const customerAccountReqSchema = t.Object({
	OrderId: t.String()
});

export type CustomerAccountReq = Static<typeof customerAccountReqSchema>;

export const customerAccountResSchema = t.Object({
	...baseResponseSchema.properties,
	Response: t.Object({
		CustomerAccounts: t.Array(wso2CustomerAccountsInArraySchema)
	})
});

export type CustomerAccountRes = Static<typeof customerAccountResSchema>;
