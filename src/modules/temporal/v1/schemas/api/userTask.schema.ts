import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const userTaskResSchema = t.Object({
	...baseResponseSchema.properties,
	Response: t.Object({
		Action: t.String({ examples: ['ADDED'] }),
		Message: t.String({ examples: ['User Task successfully created.'] })
	})
});

export type UserTaskAddRes = Static<typeof userTaskResSchema>;

// Request Schemas
export const addUserTaskReqSchema = t.Object({
	OrderId: t.String({ examples: ['UNFH-123456789'] }),
	WorkflowId: t.String({ examples: ['UNFH-123456789-AddressMatching'] }),
	UserTaskId: t.String(),
	TaskType: t.String({ examples: ['GraniteAddressMatching'] }),
	IsOrderable: t.<PERSON>({ examples: [true] })
});
export type AddUserTaskReq = Static<typeof addUserTaskReqSchema>;

export const triggerUserTaskReqSchema = t.Object({
	WorkflowId: t.String({ example: 'UNFH-123456789-AddressMatching' })
});
export type TriggerUserTaskReq = Static<typeof triggerUserTaskReqSchema>;
