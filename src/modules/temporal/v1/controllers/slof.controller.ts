import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import Elysia from 'elysia';
import { getIdTokenInfo } from '../../../../middleware/uaid/util/utils';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type CreateSlofOrderPostLoginRes,
	type CreateSlofOrderPreLoginRes,
	createSlofOrderPostLoginReqSchema,
	createSlofOrderPostLoginResSchema,
	createSlofOrderPreLoginReqSchema,
	createSlofOrderPreLoginResSchema
} from '../schemas/api/slof.schema';
import Slof from '../services/slof.service';

export const protectedSlofsV1Routes = new Elysia({ prefix: '/slof/public' })
	.resolve(() => {
		return {
			SlofPrelogin: new Slof(randomUUID())
		};
	})
	.post(
		'',
		async (ctx): Promise<CreateSlofOrderPreLoginRes> => {
			const orderDetailRes = await ctx.SlofPrelogin.createSlofOrderPrelogin(
				ctx.body
			);

			ctx.set.status = orderDetailRes.Code;

			return orderDetailRes;
		},
		{
			body: createSlofOrderPreLoginReqSchema,
			response: {
				200: createSlofOrderPreLoginResSchema,
				500: errorBaseResponseSchema,
				404: errorBaseResponseSchema
			},
			detail: {
				description:
					'Create Slof Non Orderable Order | Prelogin <br><br> <b>Table:</b> NonOrderableTxnHistory',
				tags: ['Temporal']
			}
		}
	)
	.use(bearer())
	.resolve(async ctx => {
		const idTokenInfo = await getIdTokenInfo(ctx.bearer);
		return {
			SlofPostlogin: new Slof(randomUUID(), idTokenInfo)
		};
	});

export const privateSlofsV1Routes = new Elysia({ prefix: '/slof' })
	.resolve(() => {
		return {
			SlofPrelogin: new Slof(randomUUID())
		};
	})
	.use(bearer())
	.resolve(async ctx => {
		const idTokenInfo = await getIdTokenInfo(ctx.bearer);
		return {
			SlofPostlogin: new Slof(randomUUID(), idTokenInfo)
		};
	})
	.post(
		'',
		async (ctx): Promise<CreateSlofOrderPostLoginRes> => {
			const orderDetailRes = await ctx.SlofPostlogin.createSlofOrderPostlogin(
				ctx.body
			);

			ctx.set.status = orderDetailRes.Code;

			return orderDetailRes;
		},
		{
			body: createSlofOrderPostLoginReqSchema,
			response: {
				200: createSlofOrderPostLoginResSchema,
				500: errorBaseResponseSchema,
				404: errorBaseResponseSchema
			},
			detail: {
				description:
					'Create Slof Non Orderable Order <br><br> | Postlogin <b>Table:</b> NonOrderableTxnHistory',
				tags: ['Temporal'],
				security: [{ bearerAuth: [] }]
			}
		}
	);
