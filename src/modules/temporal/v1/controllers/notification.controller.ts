import { randomUUID } from 'node:crypto';
import { Elysia } from 'elysia';
import {
	type BaseResponse,
	baseResponseSchema,
	errorBaseResponseSchema
} from '../../../../shared/schemas/api/responses.schema';
import { mailReqSchema } from '../schemas/api/notification.schema';
import Notification from '../services/notification.service';

const notificationV1Routes = new Elysia({ prefix: '/notification' })
	.resolve(() => ({
		Notification: new Notification(randomUUID())
	}))
	.post(
		'/email',
		async (ctx): Promise<BaseResponse> => {
			return ctx.Notification.sendEmail(ctx.body);
		},
		{
			body: mailReqSchema,
			response: {
				200: baseResponseSchema,
				500: errorBaseResponseSchema,
				404: baseResponseSchema
			},
			detail: {
				description:
					'Send Email Notification  <br><br> <b>Backend System:</b> UNKNOWN',
				tags: ['Temporal']
			}
		}
	);

export default notificationV1Routes;
