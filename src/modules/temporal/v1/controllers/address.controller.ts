import { randomUUID } from 'node:crypto';
import Elysia from 'elysia';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type AddressCheckNigRes,
	addressCheckNigReqSchema,
	addressCheckNigResSchema
} from '../schemas/api/address.schema';
import Address from '../services/address.service';

const addressV1Routes = new Elysia({ prefix: '/address' })
	.resolve(() => ({
		Address: new Address(randomUUID())
	}))
	.get(
		'/nig-check',
		async (ctx): Promise<AddressCheckNigRes> => {
			return await ctx.Address.addressCheckNig(ctx.query.OrderId);
		},
		{
			query: addressCheckNigReqSchema,
			response: {
				200: addressCheckNigResSchema,
				500: errorBaseResponseSchema,
				404: errorBaseResponseSchema
			},
			detail: {
				description: 'Address Check Nig <br><br> <b>Target System:</b> Granite',
				tags: ['Temporal']
			}
		}
	);

export default addressV1Routes;
