import { randomUUID } from 'node:crypto';
import <PERSON><PERSON> from 'elysia';
import {
	type BaseResponse,
	baseResponseSchema,
	errorBaseResponseSchema
} from '../../../../shared/schemas/api/responses.schema';
import {
	type AddOnsEligibilityRes,
	addOnsEligibilityResSchema
} from '../../../eligibility/v1/schemas/api/addOns.schema';
import {
	type CheckStockByListRes,
	checkStockByListResSchema
} from '../../../order/v1/schemas/api/checkStockByList.schema';
import { addOnOrderReqSchema } from '../schemas/api/addons.schema';
import {
	type GetNovaStatusRes,
	type OrderDetailsRes,
	getNovaStatusReqSchema,
	getNovaStatusResSchema,
	orderDetailsBodyReqSchema,
	orderDetailsResSchema,
	orderUpdateReqSchema,
	updateOrderDetailsReqSchema
} from '../schemas/api/order.schema';
import AddOns from '../services/addon.service';
import Orderable from '../services/order.service';

const ordersV1Routes = new Elysia({ prefix: '/order' })
	.resolve(() => {
		const orderable = new Orderable(randomUUID());
		return {
			Orderable: orderable,
			AddOns: new AddOns(randomUUID(), orderable)
		};
	})
	.post(
		'/details',
		async (ctx): Promise<OrderDetailsRes> => {
			const orderDetailRes = await ctx.Orderable.getOrderDetails(ctx.body);

			ctx.set.status = orderDetailRes.Code;

			return orderDetailRes;
		},
		{
			body: orderDetailsBodyReqSchema,
			response: {
				200: orderDetailsResSchema,
				500: errorBaseResponseSchema,
				404: errorBaseResponseSchema
			},
			detail: {
				description:
					'Get Order Details for Temporal <br><br> <b>Table:</b> orderable_txn_history',
				tags: ['Temporal']
			}
		}
	)
	.put(
		'/status',
		async (ctx): Promise<BaseResponse> => {
			return await ctx.Orderable.updateOrderStatus(ctx.body);
		},
		{
			body: orderUpdateReqSchema,
			response: {
				200: baseResponseSchema,
				500: errorBaseResponseSchema,
				404: baseResponseSchema
			},
			detail: {
				description:
					'Update Order from Temporal workflow <br><br> <b>Table:</b> orderable_txn_history',
				tags: ['Temporal']
			}
		}
	)
	.put(
		'/order-update',
		async (ctx): Promise<BaseResponse> => {
			return await ctx.Orderable.updateOrderDetails(ctx.body);
		},
		{
			body: updateOrderDetailsReqSchema,
			response: {
				200: baseResponseSchema,
				500: errorBaseResponseSchema,
				404: baseResponseSchema
			},
			detail: {
				description:
					'Update Order from Temporal workflow <br><br> <b>Table:</b> orderable_txn_history',
				tags: ['Temporal']
			}
		}
	)
	.post(
		'/addon/check-stock',
		async (ctx): Promise<CheckStockByListRes> => {
			return await ctx.AddOns.checkStock(ctx.body);
		},
		{
			body: addOnOrderReqSchema,
			response: {
				200: checkStockByListResSchema,
				500: errorBaseResponseSchema,
				404: baseResponseSchema
			},
			detail: {
				description:
					'Check stock availability by SKU item number from MMAG. This API checks the stock status of a given item.<br><br><b>Backend:</b> MMAG',
				tags: ['Temporal']
			}
		}
	)
	.post(
		'/addon/eligibility',
		async (ctx): Promise<AddOnsEligibilityRes> => {
			return await ctx.AddOns.checkEligibility(ctx.body);
		},
		{
			body: addOnOrderReqSchema,
			response: {
				200: addOnsEligibilityResSchema,
				500: errorBaseResponseSchema,
				404: baseResponseSchema
			},
			detail: {
				description:
					"Get customer's addons subscription eligibility. <br><br> <b>Backend System:</b> NOVA SIEBEL & OMG",
				tags: ['Temporal']
			}
		}
	)
	.get(
		'/nova-order-status',
		async (ctx): Promise<GetNovaStatusRes> => {
			return await ctx.Orderable.getOmgNovaOrderStatusByOrderId(ctx.query);
		},
		{
			query: getNovaStatusReqSchema,
			response: {
				200: getNovaStatusResSchema,
				500: errorBaseResponseSchema,
				404: baseResponseSchema
			},
			detail: {
				description:
					'Get the status of an order from the NOVA system. <br><br> <b>Backend System:</b> NOVA',
				tags: ['Temporal']
			}
		}
	);

export default ordersV1Routes;
