import { randomUUID } from 'node:crypto';
import Elysia from 'elysia';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type AWCMSRRes,
	type CustomerAccountRes,
	awcMsrReqSchema,
	awcMsrResSchema,
	customerAccountReqSchema,
	customerAccountResSchema
} from '../schemas/api/account.schema';
import Account from '../services/account.service';

const accountV1Routes = new Elysia({ prefix: '/account' })
	.resolve(() => ({
		Account: new Account(randomUUID())
	}))
	.get(
		'/profile',
		async (ctx): Promise<CustomerAccountRes> => {
			return await ctx.Account.getCustomerAccount(ctx.query);
		},
		{
			detail: {
				description:
					'Customer Account check. <br><br> <b>Backend System:</b> NOVA SIEBEL',
				tags: ['Temporal']
			},
			query: customerAccountReqSchema,
			response: {
				200: customerAccountResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.get(
		'/awc-msr-check',
		async (ctx): Promise<AWCMSRRes> => {
			return await ctx.Account.checkProfileAwcMsr(ctx.query);
		},
		{
			detail: {
				description:
					'Customer AWC and SSM check. <br><br> <b>Backend System:</b> Concise',
				tags: ['Temporal']
			},
			query: awcMsrReqSchema,
			response: {
				200: awcMsrResSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export default accountV1Routes;
