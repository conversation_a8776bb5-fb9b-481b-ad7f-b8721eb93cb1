import type { AddOnsRequestCategoryEnum } from '../../../../enum/addOns.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { UE_ERROR } from '../../../../middleware/error';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import { encrypt } from '../../../../shared/encryption/aesGcm';
import type { BaseResponse } from '../../../../shared/schemas/api/responses.schema';
import type {
	AddOnsEligibilityReq,
	AddOnsEligibilityRes
} from '../../../eligibility/v1/schemas/api/addOns.schema';
import AddOnsEligibility from '../../../eligibility/v1/services/addOns.service';
import type { CheckStockByListRes } from '../../../order/v1/schemas/api/checkStockByList.schema';
import type {
	SelectCustomerOrder,
	SelectOrderableTxnHistory
} from '../../../order/v1/schemas/db/orderable.schema';
import StockOrder from '../../../order/v1/services/stock.service';
import type { AddOnOrderReq } from '../schemas/api/addons.schema';
import type Order from './order.service';

class AddOns {
	private order: Order;
	private stockOrder: StockOrder;
	private integrationId: string;

	constructor(integrationId: string, order: Order) {
		this.integrationId = integrationId;
		this.order = order;
		this.stockOrder = new StockOrder(integrationId);
	}

	private validateOrderTxn(
		orderTxn: SelectOrderableTxnHistory
	): BaseResponse | null {
		if (!orderTxn) {
			return {
				Success: false,
				Code: StatusCodeEnum.NOT_FOUND_ERROR,
				IntegrationId: this.integrationId,
				Message: 'Order not found'
			};
		}

		if (!orderTxn.OrderData.DeviceList) {
			return {
				Success: false,
				Code: StatusCodeEnum.NOT_FOUND_ERROR,
				IntegrationId: this.integrationId,
				Message: 'Device list not found'
			};
		}

		return null;
	}

	async checkStock(body: AddOnOrderReq): Promise<CheckStockByListRes> {
		const [orderTxn]: SelectOrderableTxnHistory[] =
			await this.order.getOrderableTxnByOrderId(body.OrderId);
		const validationError: BaseResponse | null =
			this.validateOrderTxn(orderTxn);
		if (validationError) {
			throw new UE_ERROR(
				'Error Validate Order Txn',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId }
			);
		}

		const partnerIds: { PartnerIds: string[] } = {
			PartnerIds: orderTxn.OrderData.DeviceList.map(
				(d: Record<string, unknown>) => d.PartnerId
			).filter(Boolean) as string[]
		};

		const checkStockByListRes: CheckStockByListRes =
			await this.stockOrder.getDeviceStockStatusByList(partnerIds);

		if (!checkStockByListRes.Success) {
			throw new UE_ERROR(
				'Error Checking Stock Status',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId }
			);
		}

		orderTxn.OrderData = {
			...orderTxn.OrderData,
			CheckStockByListRes: checkStockByListRes.Response
		};
		await this.order.updateOrderableTxn(orderTxn);

		return checkStockByListRes;
	}

	async checkEligibility(body: AddOnOrderReq): Promise<AddOnsEligibilityRes> {
		const [orderTxn]: SelectOrderableTxnHistory[] =
			await this.order.getOrderableTxnByOrderId(body.OrderId);

		const validationError: BaseResponse | null =
			this.validateOrderTxn(orderTxn);
		if (validationError) {
			throw new UE_ERROR(
				'Error Validate Order Txn',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId }
			);
		}

		if (!orderTxn.BillingAccountNo) {
			throw new UE_ERROR(
				'Billing account number not found',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		if (!orderTxn.CustomerId) {
			throw new UE_ERROR(
				'Customer ID not found',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		const addOnsEligibilityReq: AddOnsEligibilityReq = {
			EncryptedBillAccNo: await encrypt(orderTxn.BillingAccountNo),
			SystemName: orderTxn.OrderData.SystemName,
			Category: orderTxn.OrderCategory as AddOnsRequestCategoryEnum,
			ServiceId: orderTxn.OrderData.ServiceId
		};

		const [orderCust]: SelectCustomerOrder[] =
			await this.order.getOrderCustByCustId(orderTxn.CustomerId);
		if (!orderCust) {
			throw new UE_ERROR(
				'Customer record not found',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		const addOnsEligibility = new AddOnsEligibility(this.integrationId, {
			IdType: orderCust.IdType,
			IdValue: orderCust.IdValue
		} as IdTokenInfo);
		const addOnsEligibilityRes: AddOnsEligibilityRes =
			await addOnsEligibility.getAddOnsEligibility(addOnsEligibilityReq);

		orderTxn.OrderData = {
			...orderTxn.OrderData,
			AddOnsEligibilityRes: addOnsEligibilityRes.Response
		};
		await this.order.updateOrderableTxn(orderTxn);

		return addOnsEligibilityRes;
	}
}

export default AddOns;
