import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import {
	DemandAction,
	DemandStatus,
	TemporalOrderStatus
} from '../../../../enum/temporal.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	Wso2CreateTroikaRes,
	Wso2QueryTroikaRes,
	Wso2UpdateTroikaRes
} from '../../../../integration/wso2/troika/schemas/api/troika';
import { UE_ERROR } from '../../../../middleware/error';
import { getMyTimeZoneDate } from '../../../../shared/common';
import type { BaseResponse } from '../../../../shared/schemas/api/responses.schema';
import type {
	OrderableAddress,
	SelectCustomerOrder,
	SelectOrderableTxnHistory
} from '../../../order/v1/schemas/db/orderable.schema';
import type {
	CreateTroikaReq,
	CreateTroikaRes,
	QueryTroikaDemandStatusReq,
	UpdateTroikaNbaReq,
	UpdateTroikaNbaRes,
	UpdateTroikaReq
} from '../schemas/api/demand.schema';
import Order from './order.service';

class Demand {
	private integrationId: string;
	private mwIntegaration: MwIntegration;
	private order: Order;

	private UNIFI_DIGITAL_SOLUTION = 'UNIFI Digital Solution';
	private UNIFI_PORTAL = 'UNIFIPORTAL';
	private USER_ID = '********';

	constructor(integrationId: string) {
		this.integrationId = integrationId;
		this.mwIntegaration = new MwIntegration(integrationId);
		this.order = new Order(integrationId);
	}

	async createTroikaTicket(request: CreateTroikaReq): Promise<CreateTroikaRes> {
		let response = null;

		const orderTxn: SelectOrderableTxnHistory = await this.getOrderTxn(
			request.OrderId
		);

		if (orderTxn.OrderData.TroikaTicketCreation?.success) {
			response = orderTxn.OrderData.TroikaTicketCreation.resource;
		} else {
			if (orderTxn.CustomerId === null) {
				throw new UE_ERROR(
					'Order is without customer information',
					StatusCodeEnum.BAD_REQUEST_ERROR,
					{ integrationId: this.integrationId }
				);
			}

			const [orderCustomer]: SelectCustomerOrder[] =
				await this.order.getOrderCustByCustId(orderTxn.CustomerId);

			// Get Address Data
			const address: OrderableAddress | null = orderTxn.Address;
			if (!address) {
				throw new UE_ERROR(
					'Order is without address information',
					StatusCodeEnum.BAD_REQUEST_ERROR,
					{ integrationId: this.integrationId }
				);
			}

			const wso2CreateTroikaRes: Wso2CreateTroikaRes =
				await this.mwIntegaration.Wso2TroikaIntegration.createTroikaDemand({
					addressId: address.AddressId || '',
					buildingName: address.BuildingName || '',
					city: address.City || '',
					companyName: this.UNIFI_DIGITAL_SOLUTION || '',
					creatorGroup: this.UNIFI_DIGITAL_SOLUTION || '',
					creatorGroupCategory: this.UNIFI_DIGITAL_SOLUTION || '',
					creatorId: this.USER_ID || '',
					creatorName: this.UNIFI_DIGITAL_SOLUTION || '',
					customerId: orderCustomer.CustomerId || '',
					customerIdType: orderCustomer.IdType || '',
					customerName: orderCustomer.FullName || '',
					customerSegment:
						orderTxn.OrderSegment.toLowerCase() === 'consumer'
							? 'RESIDENTIAL'
							: orderTxn.OrderSegment.toLowerCase() === 'sme'
								? 'BUSINESSS'
								: '',
					demandId: orderTxn.OrderId || '',
					exchangeId: orderTxn.OrderData.ReadNig?.MAIN_DP.split('_')[0] || '',
					floorNo: address.FloorNo || '',
					lat:
						address.Latitude ||
						orderTxn.OrderData.SmartMapInstallationAddress?.Location?.Lat,
					lon:
						address.Longitude ||
						orderTxn.OrderData.SmartMapInstallationAddress?.Location?.Lon,
					neId: orderTxn.OrderData.ReadNig?.MAIN_DP || null,
					postCode: address.Postcode || '',
					section: address.Section || '',
					sourceSystem: this.UNIFI_PORTAL,
					state: address.State || '',
					streetName: address.StreetName || '',
					streetType: address.StreetType || '',
					ticketCategory: request.DemandCategory || '',
					unitId: address.UnitNo || '',
					outsideRadius: 'N',
					dpLat:
						orderTxn.OrderData.PortAndService?.CrmResource?.latitudePremise,
					dpLon:
						orderTxn.OrderData.PortAndService?.CrmResource?.longitudePremise,
					premiseCategory: orderTxn.Address?.PremiseCategory || '',
					hybridTagging: orderTxn.OrderData.ReadNig?.HYBRID || 'N',
					segmentType: orderTxn.OrderData.ProductDetails?.MarketType,
					addressServiceCategory:
						orderTxn.OrderData.ReadNig?.ADDR_SERVICE_CATEGORY || ''
				});
			if (wso2CreateTroikaRes.success) {
				response = wso2CreateTroikaRes.resource;
				orderTxn.OrderData.TroikaTicketCreation = wso2CreateTroikaRes;
				orderTxn.OrderData.DemandDetails = {
					...orderTxn.OrderData.DemandDetails,
					...response,
					DemandStatus: DemandStatus.SUBMITTED
				};

				await this.order.updateOrderableTxn(orderTxn);
			} else {
				throw new UE_ERROR(
					'Create Troika demand throw error',
					StatusCodeEnum.WSO2_ERROR,
					{ integrationId: this.integrationId }
				);
			}
		}

		return {
			Success: true,
			IntegrationId: this.integrationId,
			Code: StatusCodeEnum.ACCEPTED,
			Response: response
		};
	}

	async updateTroikaTicket(request: UpdateTroikaReq): Promise<BaseResponse> {
		const orderTxn: SelectOrderableTxnHistory = await this.getOrderTxn(
			request.OrderId
		);

		const troikaId = orderTxn.OrderData.DemandDetails?.troikaId;
		if (!troikaId) {
			throw new UE_ERROR(
				'Troika Id / Demand details not found',
				StatusCodeEnum.BAD_REQUEST_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		const wso2UpdateTroikaRes: Wso2UpdateTroikaRes =
			await this.mwIntegaration.Wso2TroikaIntegration.updateTroikaDemand({
				troikaId: troikaId,
				demandId: orderTxn.OrderId,
				demandAction: request.DemandAction,
				requestorId: this.USER_ID,
				remark: request.Remark,
				fileName: request.FileName,
				attachment: request.Attachment
			});

		if (wso2UpdateTroikaRes.success) {
			if (request.DemandAction === DemandAction.CANCEL) {
				orderTxn.OrderData.DemandDetails.demandStatus = DemandStatus.CANCEL;
				orderTxn.OrderData.DemandDetails.cancellationReason = request.Remark;
			} else if (request.DemandAction === DemandAction.REMARK) {
				orderTxn.OrderData.DemandDetails.flFeedback = request.Remark;
				orderTxn.OrderData.DemandDetails.pflCount =
					(orderTxn.OrderData.DemandDetails.pflCount || 0) + 1;
				orderTxn.OrderData.DemandDetails.demandStatus = DemandStatus.INPROGRESS;
			}

			await this.order.updateOrderableTxn(orderTxn);
		} else {
			throw new UE_ERROR(
				'Update Troika demand throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return {
			Success: true,
			IntegrationId: this.integrationId,
			Code: StatusCodeEnum.ACCEPTED
		};
	}

	async troikaNbaUpdate(
		request: UpdateTroikaNbaReq
	): Promise<UpdateTroikaNbaRes> {
		const orderTxn: SelectOrderableTxnHistory = await this.getOrderTxn(
			request.demandId
		);

		orderTxn.OrderData.DemandDetails = {
			...orderTxn.OrderData.DemandDetails,
			...request
		};

		return {
			NBAUpdate: {
				RespHeader: {
					DateTimeResp: getMyTimeZoneDate().toISOString(),
					ErrorCode: '1',
					ErrorMsg: 'NBA updated successfully'
				}
			}
		};
	}

	async getOrderTxn(orderId: string): Promise<SelectOrderableTxnHistory> {
		const [orderTxn]: SelectOrderableTxnHistory[] =
			await this.order.getOrderableTxnByOrderId(orderId);
		if (!orderTxn) {
			throw new UE_ERROR('Order not found', StatusCodeEnum.BAD_REQUEST_ERROR, {
				integrationId: this.integrationId
			});
		}

		if (orderTxn.OrderStatus === TemporalOrderStatus.ORDER_CANCELED) {
			throw new UE_ERROR(
				'Order is Cancelled',
				StatusCodeEnum.BAD_REQUEST_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return orderTxn;
	}

	async queryTroikaDemandStatus(
		request: QueryTroikaDemandStatusReq
	): Promise<BaseResponse> {
		const orderTxn: SelectOrderableTxnHistory = await this.getOrderTxn(
			request.OrderId
		);
		const troikaId = orderTxn.OrderData.DemandDetails?.troikaId;
		if (!troikaId) {
			throw new UE_ERROR(
				'Troika Id / Demand details not found',
				StatusCodeEnum.BAD_REQUEST_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		const wso2QueryTroikaDemandStatusRes: Wso2QueryTroikaRes =
			await this.mwIntegaration.Wso2TroikaIntegration.queryTroikaDemandStatus({
				troikaId: troikaId,
				demandId: orderTxn.OrderId
			});

		if (!wso2QueryTroikaDemandStatusRes.success) {
			throw new UE_ERROR(
				'Query Troika demand status throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		orderTxn.OrderData.DemandDetails = {
			...orderTxn.OrderData.DemandDetails,
			...wso2QueryTroikaDemandStatusRes.resource
		};

		return {
			Success: true,
			IntegrationId: this.integrationId,
			Code: StatusCodeEnum.ACCEPTED
		};
	}
}

export default Demand;
