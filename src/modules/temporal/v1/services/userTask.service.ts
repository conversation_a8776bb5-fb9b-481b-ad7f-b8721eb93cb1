import { and, eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config.ts';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum.ts';
import { MwIntegration } from '../../../../integration/mw.integration.ts';
import type { TemporalUserTaskSignalRes } from '../../../../integration/temporal/schemas/api/temporal.schema.ts';
import { UE_ERROR } from '../../../../middleware/error.ts';
import type {
	AddUserTaskReq,
	TriggerUserTaskReq,
	UserTaskAddRes
} from '../schemas/api/userTask.schema.ts';
import {
	type SelectTemporalUserTask,
	temporalUserTaskTableSchema
} from '../schemas/db/temporalUserTask.schema.ts';

class UserTask {
	private db: NodePgDatabase;
	private mwIntegaration: MwIntegration;
	private integrationId: string;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
		this.mwIntegaration = new MwIntegration(integrationId);
	}

	async getAllUserTask(): Promise<SelectTemporalUserTask[]> {
		return await this.db.select().from(temporalUserTaskTableSchema);
	}

	async createUserTask(body: AddUserTaskReq): Promise<UserTaskAddRes> {
		await this.checkExistingUserTask(body.OrderId, body.UserTaskId);

		try {
			const insertValues = {
				OrderId: body.OrderId,
				WorkflowId: body.WorkflowId,
				UserTaskId: body.UserTaskId,
				TaskStatus: 'OPEN',
				TaskType: body.TaskType,
				IsOrderable: body.IsOrderable
			};

			const result: SelectTemporalUserTask[] = await this.db
				.insert(temporalUserTaskTableSchema)
				.values(insertValues)
				.returning();

			if (result.length > 0) {
				return {
					Success: true,
					Code: StatusCodeEnum.CREATED,
					IntegrationId: this.integrationId,
					Response: {
						Action: 'ADDED',
						Message: `Task added to database successfully: order_id- ${body.OrderId} workflow_id- ${body.WorkflowId} user_task_id- ${body.UserTaskId}`
					}
				};
			}

			throw new UE_ERROR(
				"We couldn't add this task this time. Please try again or reach out to our support team for help.",
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId }
			);
		} catch (e) {
			throw new UE_ERROR(
				'An error occurred while creating the user task.',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId }
			);
		}
	}

	async triggerSignal(
		bodyRequest: TriggerUserTaskReq
	): Promise<UserTaskAddRes> {
		const res: TemporalUserTaskSignalRes =
			await this.mwIntegaration.TemporalIntegration.triggerUserTaskSignal({
				WorkflowId: bodyRequest.WorkflowId
			});

		if (!res.status) {
			throw new UE_ERROR(
				'Temporal User Task Signal throw error',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId, response: res }
			);
		}

		return {
			Success: true,
			Code: StatusCodeEnum.ACCEPTED,
			IntegrationId: this.integrationId,
			Response: {
				Action: 'TRIGGERD',
				Message: `Temporal User Task Signal Triggered::: workflow_id- ${bodyRequest.WorkflowId}`
			}
		};
	}

	async checkExistingUserTask(
		orderId: string,
		userTaskId: string
	): Promise<void> {
		const existingTasks: SelectTemporalUserTask[] = await this.db
			.select()
			.from(temporalUserTaskTableSchema)
			.where(
				and(
					eq(temporalUserTaskTableSchema.OrderId, orderId),
					eq(temporalUserTaskTableSchema.UserTaskId, userTaskId)
				)
			);

		if (existingTasks.length > 0) {
			throw new UE_ERROR(
				'User task already exists.',
				StatusCodeEnum.BAD_REQUEST_ERROR,
				{ integrationId: this.integrationId }
			);
		}
	}
}

export default UserTask;
