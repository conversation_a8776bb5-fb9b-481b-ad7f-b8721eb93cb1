import { eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { pinoLog } from '../../../../config/pinoLog.config';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import type {
	DecisionLookupReq,
	DecisionLookupRes
} from '../schemas/api/decision.schema';
import { temporalDecisionTableSchema } from '../schemas/db/temporalDecision.schema';
import type {
	SelectTemporalDecisionTable,
	TemporalDecisionRule
} from '../schemas/db/temporalDecision.schema';

class Decision {
	private integrationId: string;
	private db: NodePgDatabase;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
		this.db = getDbInstance();
	}

	async lookupDecision(body: DecisionLookupReq): Promise<DecisionLookupRes> {
		try {
			const [decisionRule]: SelectTemporalDecisionTable[] = await this.db
				.select()
				.from(temporalDecisionTableSchema)
				.where(eq(temporalDecisionTableSchema.Name, body.DecisionName));

			if (!decisionRule || !decisionRule.Rules) {
				return {
					Success: false,
					Code: StatusCodeEnum.NOT_FOUND_ERROR,
					Message: 'Decision rule not found',
					IntegrationId: this.integrationId
				};
			}

			const ruleSet: TemporalDecisionRule = decisionRule.Rules;
			const { Inputs } = body;

			for (const rule of ruleSet.rules) {
				let isMatch = true;

				for (const condition of rule.conditions) {
					const inputValue = Inputs[condition.column];
					const conditionValue = condition.value;

					switch (condition.operator) {
						case 'equals':
							isMatch = String(inputValue) === String(conditionValue);
							break;
						case 'notEquals':
							isMatch = String(inputValue) !== String(conditionValue);
							break;
						case 'greaterThan':
							isMatch = Number(inputValue) > Number(conditionValue);
							break;
						case 'lessThan':
							isMatch = Number(inputValue) < Number(conditionValue);
							break;
						case 'in':
							isMatch =
								Array.isArray(conditionValue) &&
								conditionValue.map(String).includes(String(inputValue));
							break;
						case 'notIn':
							isMatch =
								Array.isArray(conditionValue) &&
								!conditionValue.map(String).includes(String(inputValue));
							break;
						default:
							pinoLog.warn(
								{ operator: condition.operator },
								'Unsupported operator'
							);
							isMatch = false;
					}

					if (!isMatch) break;
				}

				if (isMatch) {
					const result: Record<string, unknown> = {};
					for (const r of rule.results) {
						result[r.column] = r.value;
					}

					return {
						Success: true,
						Code: StatusCodeEnum.OK,
						Result: result,
						Message: 'Decision rule found',
						IntegrationId: this.integrationId
					};
				}
			}

			return {
				Success: false,
				Code: StatusCodeEnum.NOT_FOUND_ERROR,
				Message: 'No matching decision rule found',
				IntegrationId: this.integrationId
			};
		} catch (error) {
			console.error('Error in decision lookup:', error);
			return {
				Success: false,
				Code: StatusCodeEnum.UE_INTERNAL_SERVER,
				Message: 'Internal server error',
				IntegrationId: this.integrationId
			};
		}
	}
}

export default Decision;
