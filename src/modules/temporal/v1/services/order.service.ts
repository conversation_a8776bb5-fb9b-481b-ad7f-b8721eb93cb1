import { and, eq } from 'drizzle-orm'; // Query helpers
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import {
	type OrderableProgress,
	type SelectCustomerOrder,
	type SelectOrderablePlan,
	type SelectOrderableTxnHistory,
	customerOrderTableSchema,
	orderablePlanTableSchema,
	orderableTxnHistoryTableSchema
} from '../../../order/v1/schemas/db/orderable.schema.ts';

import { getDbInstance } from '../../../../config/db.config.ts';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum.ts';
import { MwIntegration } from '../../../../integration/mw.integration.ts';
import type { Wso2OrderMonitoringReq } from '../../../../integration/wso2/order/schemas/api/wso2OrderMonitoring.schema.ts';
import { UE_ERROR } from '../../../../middleware/error.ts';
import { getMyTimeZoneDate } from '../../../../shared/common.ts';
import type { BaseResponse } from '../../../../shared/schemas/api/responses.schema.ts';
import {
	type SelectNonOrderableTxnHistory,
	nonOrderableTxnHistoryTableSchema
} from '../../../order/v1/schemas/db/nonOrderableTxnHistory.schema.ts';
import type {
	GetNovaStatusReq,
	GetNovaStatusRes,
	OrderDetailsBodyReq,
	OrderUpdateDetailsReq,
	OrderUpdateReq
} from '../schemas/api/order.schema.ts';

class Order {
	private db: NodePgDatabase;
	private integrationId: string;
	private mwIntegration: MwIntegration;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
		this.db = getDbInstance();
		this.mwIntegration = new MwIntegration(integrationId);
	}

	async getAllOrderableHistory(): Promise<SelectOrderableTxnHistory[]> {
		return await this.db.select().from(orderableTxnHistoryTableSchema);
	}

	async updateOrderableTxn(
		orderTxn: SelectOrderableTxnHistory
	): Promise<SelectOrderableTxnHistory> {
		const result: SelectOrderableTxnHistory[] = await this.db
			.update(orderableTxnHistoryTableSchema)
			.set({
				...orderTxn,
				UpdatedAt: getMyTimeZoneDate()
			})
			.where(and(eq(orderableTxnHistoryTableSchema.OrderId, orderTxn.OrderId)))
			.returning();

		if (result.length === 0) {
			throw new UE_ERROR('Order not found', StatusCodeEnum.BAD_REQUEST_ERROR, {
				integrationId: this.integrationId
			});
		}

		return result[0];
	}

	async updateNonOrderableTxn(
		orderTxn: SelectNonOrderableTxnHistory
	): Promise<SelectNonOrderableTxnHistory> {
		const result: SelectNonOrderableTxnHistory[] = await this.db
			.update(nonOrderableTxnHistoryTableSchema)
			.set({
				...orderTxn,
				UpdatedAt: getMyTimeZoneDate()
			})
			.where(
				and(eq(nonOrderableTxnHistoryTableSchema.OrderId, orderTxn.OrderId))
			)
			.returning();

		if (result.length === 0) {
			throw new UE_ERROR('Order not found', StatusCodeEnum.BAD_REQUEST_ERROR, {
				integrationId: this.integrationId
			});
		}

		return result[0];
	}

	async updateOrderStatus(
		orderStatusData: OrderUpdateReq
	): Promise<BaseResponse> {
		const checkOrderExist: SelectOrderableTxnHistory[] =
			await this.checkOrderableExistByOrderId(orderStatusData.OrderId);

		if (checkOrderExist.length > 0) {
			const orderProgress: OrderableProgress =
				checkOrderExist[0].OrderProgress ?? [];
			orderProgress.push({
				Status: orderStatusData.OrderProgress,
				Timestamp: getMyTimeZoneDate().toISOString()
			});

			const result: SelectOrderableTxnHistory[] = await this.db
				.update(orderableTxnHistoryTableSchema)
				.set({
					OrderStatus: orderStatusData.OrderStatus,
					OrderProgress: orderProgress,
					UpdatedAt: getMyTimeZoneDate()
				})
				.where(
					eq(orderableTxnHistoryTableSchema.OrderId, orderStatusData.OrderId)
				)
				.returning();

			return {
				Success: result.length > 0,
				Code: StatusCodeEnum.ACCEPTED,
				IntegrationId: this.integrationId
			};
		}

		throw new UE_ERROR('Order not found', StatusCodeEnum.NOT_FOUND_ERROR, {
			integrationId: this.integrationId
		});
	}

	async updateOrderDetails(data: OrderUpdateDetailsReq): Promise<BaseResponse> {
		const checkOrderExist: SelectOrderableTxnHistory[] =
			await this.checkOrderableExistByOrderId(data.OrderId);

		if (checkOrderExist.length > 0) {
			if (data.Type === 'OrderStatus') {
				const orderDetailsByStatus: OrderUpdateReq = {
					OrderId: data.OrderId,
					OrderStatus: data.Details.OrderStatus as string,
					OrderProgress: data.Details.OrderProgress as string
				};

				await this.updateOrderStatus(orderDetailsByStatus);

				return {
					Success: true,
					Code: StatusCodeEnum.ACCEPTED,
					IntegrationId: this.integrationId
				};
			}

			if (data.Type === 'OrderData') {
				const [orderTxn]:
					| SelectOrderableTxnHistory[]
					| SelectNonOrderableTxnHistory[] = data.IsOrderable
					? await this.getOrderableTxnByOrderId(data.OrderId)
					: await this.getNonOrderableTxnByOrderId(data.OrderId);

				if (!orderTxn) {
					throw new UE_ERROR(
						'Order not found',
						StatusCodeEnum.BAD_REQUEST_ERROR,
						{ integrationId: this.integrationId }
					);
				}

				for (const [key, value] of Object.entries(data.Details)) {
					switch (key) {
						case 'AddressNotFoundKCICount':
							orderTxn.OrderData.AddressNotFoundKCICount = value;
							break;

						case 'CustomerAgreeToProceed':
							orderTxn.OrderData.CustomerAgreeToProceed = value;
							break;

						case 'PFLKCICount':
							orderTxn.OrderData.PFLKCICount = value;
							break;

						case 'RfsUpdateFlag':
							orderTxn.OrderData.RfsUpdateFlag = value;
							break;

						case 'DemandUnifiAir':
							orderTxn.OrderData.DemandDetails.UnifiAir = value;
							break;

						case 'DemandStatus':
							orderTxn.OrderData.DemandDetails.DemandStatus = value;
							break;

						default:
							throw new UE_ERROR(
								'Column Not Found',
								StatusCodeEnum.NOT_FOUND_ERROR
							);
					}
				}

				data.IsOrderable
					? await this.updateOrderableTxn(orderTxn as SelectOrderableTxnHistory)
					: await this.updateNonOrderableTxn(
							orderTxn as SelectNonOrderableTxnHistory
						);

				return {
					Success: true,
					Code: StatusCodeEnum.ACCEPTED,
					IntegrationId: this.integrationId
				};
			}
		}

		throw new UE_ERROR('Order not found', StatusCodeEnum.NOT_FOUND_ERROR);
	}

	async checkOrderableExistByOrderId(
		order_id: string
	): Promise<SelectOrderableTxnHistory[]> {
		return await this.db
			.select()
			.from(orderableTxnHistoryTableSchema)
			.where(and(eq(orderableTxnHistoryTableSchema.OrderId, order_id)));
	}

	async getOrderableTxnByOrderId(
		order_id: string
	): Promise<SelectOrderableTxnHistory[]> {
		return await this.db
			.select()
			.from(orderableTxnHistoryTableSchema)
			.where(eq(orderableTxnHistoryTableSchema.OrderId, order_id));
	}

	async getNonOrderableTxnByOrderId(
		order_id: string
	): Promise<SelectNonOrderableTxnHistory[]> {
		return await this.db
			.select()
			.from(nonOrderableTxnHistoryTableSchema)
			.where(
				eq(nonOrderableTxnHistoryTableSchema.OrderId, Number.parseInt(order_id))
			);
	}

	async getOrderCustByCustId(
		customer_id: string
	): Promise<SelectCustomerOrder[]> {
		return await this.db
			.select()
			.from(customerOrderTableSchema)
			.where(eq(customerOrderTableSchema.CustomerId, customer_id));
	}

	async getPlanDetailsByPlanId(
		plan_id: string
	): Promise<SelectOrderablePlan[]> {
		return await this.db
			.select()
			.from(orderablePlanTableSchema)
			.where(eq(orderablePlanTableSchema.PlanId, plan_id));
	}

	private async getOrderAndCustomerDetails(
		orderId: string,
		isOrderable: boolean
	): Promise<{
		orderTxn: SelectOrderableTxnHistory | SelectNonOrderableTxnHistory;
		customerDetails: SelectCustomerOrder;
	} | null> {
		// Fetch order transaction by order ID
		const orderTxn:
			| SelectOrderableTxnHistory[]
			| SelectNonOrderableTxnHistory[] = isOrderable
			? await this.getOrderableTxnByOrderId(orderId)
			: await this.getNonOrderableTxnByOrderId(orderId);
		if (orderTxn.length === 0) {
			return null;
		}

		// Fetch customer details using the CustomerId from order transaction
		if (orderTxn[0] && 'CustomerId' in orderTxn[0]) {
			const customerDetails: SelectCustomerOrder[] =
				await this.getOrderCustByCustId(orderTxn[0].CustomerId ?? '');
			return { orderTxn: orderTxn[0], customerDetails: customerDetails[0] };
		}
		return {
			orderTxn: orderTxn[0],
			customerDetails: {} as SelectCustomerOrder
		};
	}
	async getOrderDetails({ OrderId, Params, IsOrderable }: OrderDetailsBodyReq) {
		const result: Record<string, string> = {};

		const orderDetails = await this.getOrderAndCustomerDetails(
			OrderId,
			IsOrderable
		);
		if (!orderDetails) {
			throw new UE_ERROR('Order ID not found', StatusCodeEnum.NOT_FOUND_ERROR);
		}

		const { orderTxn, customerDetails } = orderDetails;

		let planDetails: SelectOrderablePlan | null = null;

		if (IsOrderable && 'PlanId' in orderTxn) {
			const plans = await this.getPlanDetailsByPlanId(orderTxn.PlanId ?? '');
			planDetails = plans.length > 0 ? plans[0] : null;
		}

		for (const param of Params) {
			switch (param) {
				case 'Status':
					result.OrderStatus = orderTxn.OrderStatus;
					if (
						'OrderProgress' in orderTxn &&
						orderTxn.OrderProgress &&
						orderTxn.OrderProgress.length > 0
					) {
						result.OrderProgress =
							orderTxn.OrderProgress[orderTxn.OrderProgress.length - 1].Status;
					}
					break;

				case 'IdType':
					result.IdType = customerDetails.IdType;
					break;

				case 'SmartDevice':
					result.SmartDevice =
						(orderTxn.OrderData?.Addons?.Devices?.SmartDevices?.length ?? 0) > 0
							? 'true'
							: 'false';
					break;

				case 'TroikaTicketId':
					result.TroikaTicketId =
						orderTxn.OrderData?.DemandDetails?.demandTicketId || null;
					break;

				case 'GraniteAddressId':
					if ('Address' in orderTxn && orderTxn.Address) {
						result.GraniteAddressId = orderTxn.Address.AddressId ?? '';
					}
					break;

				case 'AddressNotFoundKCI':
					result.AddressNotFoundKCICount =
						orderTxn.OrderData?.AddressNotFoundKCICount || null;
					break;

				case 'CustomerAgreeToProceed':
					result.CustomerAgreeToProceed =
						orderTxn.OrderData?.CustomerAgreeToProceed || null;
					break;

				case 'IsOrderable':
					result.IsOrderable = planDetails?.PlanTechnical?.orderable ?? false;
					break;

				case 'StatusEnterprise':
					result.StatusEnterprise =
						orderTxn.OrderData?.StatusEnterprise || null;
					break;

				case 'OrderBucket':
					result.OrderBucket = planDetails?.PlanTechnical?.dbs_bucket ?? null;
					break;

				case 'MMAG':
					result.MMAG =
						orderTxn.OrderData?.Products?.DeliveryPartner === 'MMAG'
							? 'true'
							: 'false';
					break;

				default:
					// No action needed for unrecognized params
					break;
			}
		}
		return {
			Success: true,
			Code: StatusCodeEnum.ACCEPTED,
			Response: result,
			IntegrationId: this.integrationId
		};
	}
	async getOmgNovaOrderStatusByOrderId(
		requestData: GetNovaStatusReq
	): Promise<GetNovaStatusRes> {
		const monitoringRequest: Wso2OrderMonitoringReq = {
			OrderMonitoringRequest: {
				OrderNumber: requestData.OrderId
			}
		};

		const res =
			await this.mwIntegration.Wso2OrderIntegration.getWso2OrderMonitoringStatus(
				monitoringRequest
			);

		const orderData = res?.Response?.OrderMonitoringResponse;

		if (orderData) {
			return {
				Success: true,
				Code: StatusCodeEnum.ACCEPTED,
				IntegrationId: this.integrationId,
				Response: orderData
			};
		}

		throw new UE_ERROR(
			'Empty or invalid response from WSO2 Order Monitoring',
			StatusCodeEnum.WSO2_ERROR,
			{ integrationId: this.integrationId, response: res }
		);
	}
}

export default Order;
