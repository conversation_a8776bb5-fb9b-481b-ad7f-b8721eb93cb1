import { and, eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config.ts';
import { EmailEnum } from '../../../../enum/notification.enum.ts';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum.ts';
import { MwIntegration } from '../../../../integration/mw.integration.ts';
import type { Wso2EmailReq } from '../../../../integration/wso2/notification/schemas/api/wso2Email.schema.ts';
import { UE_ERROR } from '../../../../middleware/error.ts';
import type { BaseResponse } from '../../../../shared/schemas/api/responses.schema.ts';
import {
	customerOrderTableSchema,
	orderableTxnHistoryTableSchema
} from '../../../order/v1/schemas/db/orderable.schema.ts';
import type { MailReq } from '../schemas/api/notification.schema.ts';
import {
	type SelectMailNotificationSchema,
	mailNotificationTableSchema
} from '../schemas/db/mailNotification.schema.ts';

class Notification {
	private db: NodePgDatabase;
	private mwIntegration: MwIntegration;
	private integrationId: string;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.mwIntegration = new MwIntegration(integrationId);
		this.integrationId = integrationId;
	}

	async sendEmail(jsonData: MailReq): Promise<BaseResponse> {
		const getDetail = await this.db
			.select({
				customerId: customerOrderTableSchema.CustomerId,
				orderId: orderableTxnHistoryTableSchema.OrderId,
				email: customerOrderTableSchema.Email,
				orderStatus: orderableTxnHistoryTableSchema.OrderStatus,
				orderProgress: orderableTxnHistoryTableSchema.OrderProgress
			})
			.from(customerOrderTableSchema)
			.leftJoin(
				orderableTxnHistoryTableSchema,
				eq(
					customerOrderTableSchema.CustomerId,
					orderableTxnHistoryTableSchema.CustomerId
				)
			)
			.where(and(eq(orderableTxnHistoryTableSchema.OrderId, jsonData.OrderId)))
			.execute();

		const orderStatus = getDetail.at(0)?.orderStatus;

		if (!orderStatus) {
			throw new UE_ERROR(
				'Order status not found.',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		const orderProgress = getDetail.at(0)?.orderProgress;

		if (!orderProgress) {
			throw new UE_ERROR(
				'Order progress not found.',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		const latestOrderProgress = orderProgress.at(-1);

		if (!latestOrderProgress) {
			throw new UE_ERROR(
				'Latest order progress is null.',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId }
			);
		}

		const customeremail: string | undefined = getDetail.at(0)?.email;

		if (!customeremail) {
			throw new UE_ERROR(
				'Customer email not found.',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		const mailNotification: SelectMailNotificationSchema[] = await this.db
			.select()
			.from(mailNotificationTableSchema)
			.where(
				and(
					eq(
						mailNotificationTableSchema.NextOrderProgress,
						latestOrderProgress.Status
					),
					eq(mailNotificationTableSchema.NextOrderStatus, orderStatus)
				)
			)
			.execute();

		if (!mailNotification) {
			throw new UE_ERROR(
				'Mail notification not found.',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		const titleText: string =
			(await mailNotification).at(0)?.TitleText ?? 'No Title Text';
		const bodyText: string =
			(await mailNotification).at(0)?.BodyText ?? 'No Body Text';

		const wso2EmailReq: Wso2EmailReq = {
			to: customeremail,
			from: EmailEnum.FROM_NOREPLY,
			subject: titleText,
			body: bodyText
		};

		const isEmailSent: boolean = await this.mwIntegration
			.getWso2NotificationIntegration()
			.getWso2SendEmail(wso2EmailReq, jsonData.OrderId, 'Temporal');

		if (!isEmailSent) {
			throw new UE_ERROR(
				'The email failed to send.',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId }
			);
		}

		return {
			Success: true,
			Code: StatusCodeEnum.ACCEPTED,
			IntegrationId: this.integrationId
		};
	}
}

export default Notification;
