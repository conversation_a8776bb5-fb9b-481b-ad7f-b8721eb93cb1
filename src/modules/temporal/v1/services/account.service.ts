import { and, eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { pinoLog } from '../../../../config/pinoLog.config';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import {
	LightweightFlagEnum,
	SystemNameEnum
} from '../../../../enum/wso2.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	Wso2CustomerProfileCheckReq,
	Wso2CustomerProfileCheckRes
} from '../../../../integration/wso2/eligibility/schemas/api/wso2CustomerProfileCheck.schema';
import type {
	Wso2CustomerAccountReq,
	Wso2CustomerAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2CustomerAccount.schema';
import { UE_ERROR } from '../../../../middleware/error';
import type {
	SelectCustomerOrder,
	SelectOrderableTxnHistory
} from '../../../order/v1/schemas/db/orderable.schema';
import {
	type SelectAccountSettings,
	accountSettingsTableSchema
} from '../../../user/v1/schemas/db/accountSettings.schema';
import type {
	AWCMSRReq,
	AWCMSRRes,
	CustomerAccountReq,
	CustomerAccountRes
} from '../schemas/api/account.schema';
import Order from './order.service';

class Account {
	private integrationId: string;
	private mwIntegration: MwIntegration;
	private db: NodePgDatabase;
	private order: Order;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
		this.mwIntegration = new MwIntegration(integrationId);
		this.order = new Order(integrationId);
		this.db = getDbInstance();
	}

	async getCustomerAccount(
		req: CustomerAccountReq
	): Promise<CustomerAccountRes> {
		const [orderTxn]: SelectOrderableTxnHistory[] =
			await this.order.getOrderableTxnByOrderId(req.OrderId);

		if (!orderTxn) {
			throw new UE_ERROR('Order ID not found', StatusCodeEnum.NOT_FOUND_ERROR);
		}

		const [orderCust]: SelectCustomerOrder[] =
			await this.order.getOrderCustByCustId(orderTxn.CustomerId ?? '');

		if (!orderCust) {
			throw new UE_ERROR(
				'Customer ID not found',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		let response = null;

		if (orderTxn.OrderData.ProfileCheck?.Status?.Type === 'OK') {
			response = orderTxn.OrderData.ProfileCheck;
		}

		try {
			const wso2CustomerAccountReq: Wso2CustomerAccountReq = {
				idType: orderCust.IdType,
				idValue: orderCust.IdValue || ''
			};

			const wso2CustomerAccountRes: Wso2CustomerAccountRes =
				await this.mwIntegration.Wso2UserIntegration.getWso2CustomerAccount(
					wso2CustomerAccountReq,
					LightweightFlagEnum.NO
				);

			if (wso2CustomerAccountRes?.Status?.Type === 'OK') {
				response = wso2CustomerAccountRes;

				orderTxn.OrderData.ProfileCheck = response;
				await this.order.updateOrderableTxn(orderTxn);
			}

			if (response) {
				return {
					Success: true,
					IntegrationId: this.integrationId,
					Code: StatusCodeEnum.OK,
					Response: {
						CustomerAccounts: response.Response?.CustomerAccounts || []
					}
				};
			}

			throw new UE_ERROR(
				'Customer account not found',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		} catch (error: unknown) {
			if (error instanceof Error) {
				throw new UE_ERROR(error.message, StatusCodeEnum.UE_INTERNAL_SERVER);
			}
			throw new UE_ERROR('Unknown error', StatusCodeEnum.UNKNOWN_ERROR);
		}
	}

	async getSystemName(
		idValue: string,
		idType: string,
		email: string
	): Promise<string | null> {
		const result: SelectAccountSettings[] = await this.db
			.select()
			.from(accountSettingsTableSchema)
			.where(
				and(
					eq(accountSettingsTableSchema.IdValue, idValue),
					eq(accountSettingsTableSchema.IdType, idType),
					eq(accountSettingsTableSchema.Email, email)
				)
			)
			.execute()
			.catch(err => {
				pinoLog.error(err);
				return [];
			});

		const systemName = result.at(0)?.SystemName ?? null;

		return systemName;
	}
	async checkProfileAwcMsr(req: AWCMSRReq): Promise<AWCMSRRes> {
		const [orderTxn]: SelectOrderableTxnHistory[] =
			await this.order.getOrderableTxnByOrderId(req.OrderId);

		if (!orderTxn) {
			throw new UE_ERROR(
				'Order ID not found.',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		if (!orderTxn.CustomerId) {
			throw new UE_ERROR(
				'Customer ID not found for this order',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		const [orderCust]: SelectCustomerOrder[] =
			await this.order.getOrderCustByCustId(orderTxn.CustomerId);

		if (!orderCust) {
			throw new UE_ERROR(
				'Customer not found for this order',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		let response = null;
		if (orderTxn.OrderData.AwcCheck?.Status?.Type === 'OK') {
			response = orderTxn.OrderData.AwcCheck;
		}

		const wso2ProfileCheckReq: Wso2CustomerProfileCheckReq = {
			CustomerProfileCheckRequest: {
				SystemName:
					(await this.getSystemName(
						orderCust.IdValue,
						orderCust.IdType,
						orderCust.Email
					)) || SystemNameEnum.NOVA,
				IdType: orderCust.IdType,
				IdValue: orderCust.IdValue
			}
		};

		const wso2ProfileCheckRes: Wso2CustomerProfileCheckRes =
			await this.mwIntegration.Wso2EligibilityIntegration.getWso2CustomerProfileCheck(
				wso2ProfileCheckReq
			);

		if (wso2ProfileCheckRes?.Status?.Type === 'OK') {
			response = wso2ProfileCheckRes;

			orderTxn.OrderData.AwcCheck = response;
			await this.order.updateOrderableTxn(orderTxn);
		}

		if (response) {
			return {
				Success: true,
				IntegrationId: this.integrationId,
				Code: StatusCodeEnum.OK,
				Response: {
					CustomerProfileCheck: response.Response?.CustomerProfileCheckResponse
				}
			};
		}

		throw new UE_ERROR(
			'Customer profile check not found',
			StatusCodeEnum.NOT_FOUND_ERROR,
			{ integrationId: this.integrationId }
		);
	}
}

export default Account;
