import Elysia from 'elysia';
import accountV1Routes from './v1/controllers/account.controller';
import addressV1Routes from './v1/controllers/address.controller';
import decisionsV1Routes from './v1/controllers/decision.controller';
import demandV1Routes from './v1/controllers/demand.controller';
import notificationV1Routes from './v1/controllers/notification.controller';
import ordersV1Routes from './v1/controllers/order.controller';
import {
	privateSlofsV1Routes,
	protectedSlofsV1Routes
} from './v1/controllers/slof.controller';
import userTaskV1Routes from './v1/controllers/userTask.controller';

export const protectedTemporalV1Routes = new Elysia({ prefix: '/v1/temporal' })
	.use(ordersV1Routes)
	.use(accountV1Routes)
	.use(addressV1Routes)
	.use(demandV1Routes)
	.use(notificationV1Routes)
	.use(userTaskV1Routes)
	.use(protectedSlofsV1Routes)
	.use(decisionsV1Routes);

export const privateTemporalV1Routes = new Elysia({
	prefix: '/v1/temporal'
}).use(privateSlofsV1Routes);
