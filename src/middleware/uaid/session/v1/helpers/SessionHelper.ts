import { and, eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../../config/db.config';
import { StatusCodeEnum } from '../../../../../enum/statusCode.enum';
import { UE_ERROR } from '../../../../error';
import { sha256 } from '../../../util/encryption';
import {
	type SelectSession,
	sessionDbSchema,
	userAuthDbSchema
} from '../schemas/models/session';
import type CheckSession from '../services/CheckSession';

class SessionHelper {
	private db: NodePgDatabase;
	private integrationId: string;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
	}

	async getSessionId(
		userId: string,
		deviceId: string,
		checkSession: CheckSession
	): Promise<string> {
		const hashDeviceId = sha256(userId, deviceId).toString();
		const getSessionData = await this.db
			.select()
			.from(userAuthDbSchema)
			.innerJoin(
				sessionDbSchema,
				eq(userAuthDbSchema.Id, sessionDbSchema.UserAuthId)
			)
			.where(
				and(
					eq(userAuthDbSchema.UserId, userId),
					eq(userAuthDbSchema.DeviceId, hashDeviceId)
				)
			)
			.limit(1);

		// Session not exist, then create a new one
		let sessionId = '';
		if (getSessionData.length === 0) {
			const getUserData = await this.db
				.select()
				.from(userAuthDbSchema)
				.where(
					and(
						eq(userAuthDbSchema.UserId, userId),
						eq(userAuthDbSchema.DeviceId, hashDeviceId)
					)
				)
				.limit(1);

			let userAuthId = '';
			if (getUserData.length === 0) {
				const createUserRes = await this.db
					.insert(userAuthDbSchema)
					.values({
						UserId: userId,
						DeviceId: hashDeviceId
					})
					.returning();

				if (createUserRes.length === 0) {
					throw new UE_ERROR(
						'Failed to insert into User!',
						StatusCodeEnum.UNPROCESSABLE_ENTITY,
						{ integrationId: this.integrationId, response: null }
					);
				}
				userAuthId = createUserRes[0].Id;
			} else {
				userAuthId = getUserData[0].Id;
			}

			const sessionData: SelectSession =
				await checkSession.createSession(userAuthId);

			sessionId = sessionData.Id;
		} else {
			sessionId = getSessionData[0].mw_session.Id;
		}

		return sessionId;
	}
}

export default SessionHelper;
