import { sha256 } from '@oslojs/crypto/sha2';
import {
	encodeBase32LowerCaseNoPadding,
	encodeHexLowerCase
} from '@oslojs/encoding';
import { eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../../config/db.config';
import { StatusCodeEnum } from '../../../../../enum/statusCode.enum';
import { UE_ERROR } from '../../../../error';
import type { SessionValidationResult } from '../schemas/models/checkSession';
import {
	type SelectSession,
	type SelectUserAuth,
	sessionDbSchema,
	userAuthDbSchema
} from '../schemas/models/session';

//* This class is to replace all lucia's functions
class CheckSession {
	private db: NodePgDatabase;
	private integrationId: string;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
	}

	async createSession(userId: string): Promise<SelectSession> {
		let bytes = new Uint8Array(20);
		bytes = crypto.getRandomValues(bytes);
		const token = encodeBase32LowerCaseNoPadding(bytes);
		const sessionId = encodeHexLowerCase(
			sha256(new TextEncoder().encode(token))
		);

		// expired in 30 days
		const session: SelectSession = {
			Id: sessionId,
			UserAuthId: userId,
			ExpiredAt: new Date(Date.now() + 1000 * 60 * 60 * 24 * 30)
		};
		await this.db.insert(sessionDbSchema).values(session);
		return session;
	}

	async validateSession(sessionId: string): Promise<void> {
		if (!sessionId.startsWith('Bearer')) {
			throw new UE_ERROR(
				'Invalid token! You are not authorized!',
				StatusCodeEnum.UNAUTHORIZED_ERROR,
				{ integrationId: this.integrationId, response: null }
			);
		}

		const match = sessionId.match(/^Bearer\s+(\S+)$/i);
		if (!match) {
			throw new UE_ERROR(
				'Invalid token! You are not authorized!',
				StatusCodeEnum.UNAUTHORIZED_ERROR,
				{ integrationId: this.integrationId, response: null }
			);
		}

		const id = sessionId.replace(/Bearer\s+/i, '');
		const getSessionData = await this.db
			.select()
			.from(sessionDbSchema)
			.where(eq(sessionDbSchema.Id, id))
			.limit(1);

		// exit and return 401 error if Session not-exist
		if (getSessionData.length === 0) {
			throw new UE_ERROR(
				'Session does not exist!',
				StatusCodeEnum.UNAUTHORIZED_ERROR,
				{ integrationId: this.integrationId, response: null }
			);
		}
	}

	async getUserId(sessionId: string): Promise<string> {
		const id = sessionId.replace(/Bearer\s+/i, '');
		const getSessionData = await this.db
			.select()
			.from(sessionDbSchema)
			.where(eq(sessionDbSchema.Id, id))
			.limit(1);

		// exit and return 401 error if Session not-exist
		if (getSessionData.length === 0) {
			throw new UE_ERROR(
				'Session does not exist!',
				StatusCodeEnum.UNAUTHORIZED_ERROR,
				{ integrationId: this.integrationId, response: null }
			);
		}

		const userAuthId = getSessionData[0].UserAuthId;

		const getUser: SelectUserAuth[] = await this.db
			.select()
			.from(userAuthDbSchema)
			.where(eq(userAuthDbSchema.Id, userAuthId));

		// UserId for Profile/Identity
		return getUser[0].UserId;
	}

	//TODO: This is lucia`s suggested implementation. If it`s not use at all, delete it
	async validateSessionToken(token: string): Promise<SessionValidationResult> {
		const sessionId = encodeHexLowerCase(
			sha256(new TextEncoder().encode(token))
		);
		const result = await this.db
			.select({ user: userAuthDbSchema, session: sessionDbSchema })
			.from(sessionDbSchema)
			.innerJoin(
				userAuthDbSchema,
				eq(sessionDbSchema.UserAuthId, userAuthDbSchema.Id)
			)
			.where(eq(sessionDbSchema.Id, sessionId));
		if (result.length < 1) {
			throw new UE_ERROR(
				'Session does not exist!',
				StatusCodeEnum.UNAUTHORIZED_ERROR,
				{ integrationId: this.integrationId, response: null }
			);
		}
		const { user, session } = result[0];
		if (Date.now() >= session.ExpiredAt.getTime()) {
			await this.db
				.delete(sessionDbSchema)
				.where(eq(sessionDbSchema.Id, session.Id));
			return { session: null, user: null };
		}
		if (Date.now() >= session.ExpiredAt.getTime() - 1000 * 60 * 60 * 24 * 15) {
			session.ExpiredAt = new Date(Date.now() + 1000 * 60 * 60 * 24 * 30);
			await this.db
				.update(sessionDbSchema)
				.set({
					ExpiredAt: session.ExpiredAt
				})
				.where(eq(sessionDbSchema.Id, session.Id));
		}
		return { session, user };
	}

	async invalidateSession(sessionId: string): Promise<void> {
		await this.db
			.delete(sessionDbSchema)
			.where(eq(sessionDbSchema.Id, sessionId));
	}
}
export default CheckSession;
