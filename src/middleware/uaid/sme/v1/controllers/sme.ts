import { randomUUID } from 'node:crypto';
import Elysia from 'elysia';
import { errorBaseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';
import CheckSession from '../../../session/v1/services/CheckSession';
import {
	type CreateSmeProfileRes,
	createSmeProfileReqSchema,
	createSmeProfileResSchema
} from '../schemas/api/sme';
import Sme from '../services/Sme';

export const smeRoutes = new Elysia({ prefix: '/sme' })
	.resolve(async ({ headers: { authorization } }) => {
		return {
			userId: await new CheckSession(randomUUID()).getUserId(
				authorization ?? ''
			)
		};
	})
	.post(
		'/create-profile',
		async (ctx): Promise<CreateSmeProfileRes> => {
			return await new Sme(randomUUID()).createSmeProfile(ctx.userId, ctx.body);
		},
		{
			body: createSmeProfileReqSchema,
			response: {
				200: createSmeProfileResSchema,
				401: errorBaseResponseSchema,
				404: errorBaseResponseSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Create SME Profile by SessionId (as Bearer token in Authorization header).<br>' +
					'User to enter the IdType and IdValue (for IdType BRN or NON_BRN only).<br>' +
					'If User submitted new IdValue, system will automatically set the Role as SUPER_ADMIN.<br>' +
					'Remark: The Role can be managed/updated by Super Admin.<br><br>' +
					'Tables: mw_identity, mw_identification, mw_credential, mw_sme_profile',
				tags: ['UAID: SME']
			}
		}
	);
// PENDING (USER REQUIREMENT) TO BE FINALIZED
// .post(
// 	'/add-dept',
// 	async (ctx): Promise<AddSmeDeptRes> => {
// 		return await new Sme(randomUUID()).addSmeDepartment(ctx.userId, ctx.body);
// 	},
// 	{
// 		body: addSmeDeptReqSchema,
// 		response: {
// 			200: addSmeDeptResSchema,
// 			401: errorBaseResponseSchema,
// 			404: errorBaseResponseSchema,
// 			500: errorBaseResponseSchema
// 		},
// 		detail: {
// 			description:
// 				'Operation done by Admin(s) only. (use SessionId as Bearer token in Authorization header).<br>' +
// 				'Add new SME Department by selected "BrnId" (use "Business.IdToken" property from /session/user-info).<br>' +
// 				'Super Admin or Department Admin to select which Company (BrnId) to view before enter the Department Name (free text)',
// 			tags: ['UAID: SME']
// 		}
// 	}
// )
// .post(
// 	'/list-dept',
// 	async (ctx): Promise<SmeDeptRes> => {
// 		return await new Sme(randomUUID()).listSmeDepartment(
// 			ctx.userId,
// 			ctx.body
// 		);
// 	},
// 	{
// 		body: smeProfileReqSchema,
// 		response: {
// 			200: smeDeptResSchema,
// 			401: errorBaseResponseSchema,
// 			404: errorBaseResponseSchema,
// 			500: errorBaseResponseSchema
// 		},
// 		detail: {
// 			description:
// 				'List of SME Department by selected "BrnId" (use "Business.IdToken" property from /session/user-info).',
// 			tags: ['UAID: SME']
// 		}
// 	}
// );
