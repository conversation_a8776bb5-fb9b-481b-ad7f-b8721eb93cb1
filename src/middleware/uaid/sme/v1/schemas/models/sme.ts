import {
	index,
	integer,
	pgTable,
	text,
	timestamp,
	varchar
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm/sql';
import {
	identificationDbSchema,
	identityDbSchema
} from '../../../../identity/v1/schemas/models/identity';

export const smeProfileDbSchema = pgTable(
	'mw_sme_profile',
	{
		Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
		UserId: varchar('user_id', { length: 50 })
			.notNull()
			.references(() => identityDbSchema.UserId, { onDelete: 'cascade' }),
		IdKey: varchar('id_key', { length: 150 }),
		DeptId: integer('dept_id').default(0),
		Role: text('role').default('USER'),
		CreatedAt: timestamp('created_at', { mode: 'date', withTimezone: true })
			.notNull()
			.default(sql`now()`),
		UpdatedAt: timestamp('updated_at', { mode: 'date', withTimezone: true })
	},
	t => [
		index('idx_mw_sme_profile_user_id').on(t.UserId),
		index('idx_mw_sme_profile_id_key').on(t.IdKey),
		index('idx_mw_sme_profile_dept_id').on(t.DeptId)
	]
);

export type SelectSmeProfile = typeof smeProfileDbSchema.$inferSelect;
export type InsertSmeProfile = typeof smeProfileDbSchema.$inferInsert;

export const smeDeptDbSchema = pgTable(
	'mw_sme_department',
	{
		Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
		IdKey: varchar('id_key', { length: 150 })
			.notNull()
			.references(() => identificationDbSchema.IdKey, { onDelete: 'cascade' }),
		DeptName: text('dept_name')
	},
	t => [index('idx_mw_sme_department_id_key').on(t.IdKey)]
);

export type SelectSmeDept = typeof smeDeptDbSchema.$inferSelect;
export type InsertSmeDept = typeof smeDeptDbSchema.$inferInsert;
