import { and, eq, gt, sql } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../../config/db.config';
import { EmailEnum } from '../../../../../enum/notification.enum';
import { StatusCodeEnum } from '../../../../../enum/statusCode.enum';
import type { Wso2EmailReq } from '../../../../../integration/wso2/notification/schemas/api/wso2Email.schema';
import type {
	Wso2SmsReq,
	Wso2SmsRes
} from '../../../../../integration/wso2/notification/schemas/api/wso2Sms.schema';
import Wso2NotificationIntegration from '../../../../../integration/wso2/notification/wso2Notification.integration';
import { UE_ERROR } from '../../../../error';
import CredentialHelper from '../../../identity/v1/helpers/CredentialHelper';
import IdentityHelper from '../../../identity/v1/helpers/IdentityHelper';
import type {
	IdentityRes,
	SelectIdentityApi
} from '../../../identity/v1/schemas/api/identity';
import { credentialDbSchema } from '../../../identity/v1/schemas/models/credential';
import { identityDbSchema } from '../../../identity/v1/schemas/models/identity';
import { sendTacTemplate } from '../../../integration/templates/email';
import ProfileHelper from '../../../profile/v1/helpers/ProfileHelper';
import type { ProfileData } from '../../../profile/v1/schemas/api/profile';
import SessionHelper from '../../../session/v1/helpers/SessionHelper';
import CheckSession from '../../../session/v1/services/CheckSession';
import {
	decrypt,
	encrypt,
	getEncryptedKeyValue,
	getMaskedValue,
	sha256
} from '../../../util/encryption';
import type { EncryptedKeyValueData } from '../../../util/schemas/encryption';
import { dateFormatter, getExpiredDate } from '../../../util/utils';
import TacHelper from '../helpers/TacHelper';
import type {
	RequestTacReq,
	RequestTacRes,
	SignUpVerificationReq,
	VerifyTacReq
} from '../schemas/api/tac';
import {
	type InsertTac,
	type SelectTac,
	tacDbSchema
} from '../schemas/models/tac';

class Tac {
	private db: NodePgDatabase;
	private integrationId: string;
	private checkSession: CheckSession;
	private wso2NotificationIntegration: Wso2NotificationIntegration;
	private credentialHelper: CredentialHelper;
	private profileHelper: ProfileHelper;
	private identityHelper: IdentityHelper;
	private tacHelper: TacHelper;
	private sessionHelper: SessionHelper;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
		this.checkSession = new CheckSession(integrationId);
		this.wso2NotificationIntegration = new Wso2NotificationIntegration(
			integrationId
		);
		this.credentialHelper = new CredentialHelper(integrationId);
		this.profileHelper = new ProfileHelper(integrationId);
		this.identityHelper = new IdentityHelper(integrationId);
		this.tacHelper = new TacHelper(integrationId);
		this.sessionHelper = new SessionHelper(integrationId);
	}

	async requestTac(req: RequestTacReq): Promise<RequestTacRes> {
		await this.credentialHelper.checkCredentialReq(req);

		const {
			credentialValue,
			credentialType,
			credentialKey,
			credentialValueEnc
		} = await this.credentialHelper.getCredentialProperty(req);

		// if credential is temporarily locked, return 423 LOCKED
		await this.credentialHelper.checkLockedCredential(credentialKey);

		if (req.Process === 'sign-up') {
			const isProfileExist =
				await this.profileHelper.isUserProfileExist(credentialKey);
			if (isProfileExist)
				throw new UE_ERROR(
					'Unable to proceed. Profile already exists',
					StatusCodeEnum.UNPROCESSABLE_ENTITY,
					{ integrationId: this.integrationId, response: null }
				);
		} else {
			// if profile not-exist, return 404
			await this.profileHelper.getUserProfileByCredential(credentialKey);
		}

		const maskedValue = await getMaskedValue(
			credentialValueEnc,
			credentialType,
			credentialKey
		);

		const res = {
			Success: true,
			Code: 200,
			IntegrationId: this.integrationId,
			Response: {
				TacNumber: '****',
				ExpiredAt: getExpiredDate(5),
				MaskedValue: maskedValue
			}
		};

		const skipTacRequest =
			await this.tacHelper.isWhitelistedCredential(credentialValue);

		if (skipTacRequest) {
			res.Response.TacNumber = '8888';
		} else {
			let TacNumber: string = Math.floor(
				1000 + Math.random() * 9000
			).toString();
			let tacNumberKey = '';
			let tacNumberEnc = '';

			const existingTac: SelectTac[] = await this.db
				.select()
				.from(tacDbSchema)
				.where(
					and(
						eq(tacDbSchema.CredentialKey, credentialKey),
						gt(tacDbSchema.ExpiredAt, sql`now()`)
					)
				)
				.limit(1);

			if (existingTac.length > 0) {
				if (existingTac[0].ResendCount === 3)
					await this.credentialHelper.lockCredential(credentialKey);

				TacNumber = await decrypt(
					existingTac[0].EncTacNumber,
					existingTac[0].TacNumber
				);

				try {
					// plus 1 for resend-count
					await this.db
						.update(tacDbSchema)
						.set({ ResendCount: sql`${tacDbSchema.ResendCount} + 1` })
						.where(eq(tacDbSchema.CredentialKey, credentialKey));
				} catch (error) {
					throw new UE_ERROR(
						'Error update resend-count in mw_locked_acc',
						StatusCodeEnum.UE_INTERNAL_SERVER,
						{ integrationId: this.integrationId, response: error }
					);
				}

				const expiredAtDb = new Date(`${existingTac[0].ExpiredAt}`);
				res.Response.ExpiredAt = dateFormatter(expiredAtDb);
			} else {
				tacNumberKey = sha256(credentialKey, TacNumber).toString();
				tacNumberEnc = await encrypt(TacNumber, tacNumberKey);

				// delete (if any) before insert new one
				await this.db
					.delete(tacDbSchema)
					.where(eq(tacDbSchema.CredentialKey, credentialKey));
				const insertTac: InsertTac[] = await this.db
					.insert(tacDbSchema)
					.values({
						CredentialKey: credentialKey,
						TacNumber: tacNumberKey,
						EncTacNumber: tacNumberEnc
					})
					.returning();

				if (insertTac.length === 0)
					throw new UE_ERROR(
						'Error in inserting TAC!',
						StatusCodeEnum.UE_INTERNAL_SERVER,
						{ integrationId: this.integrationId, response: null }
					);

				const expiredAtDb = new Date(`${insertTac[0].ExpiredAt}`);
				res.Response.ExpiredAt = dateFormatter(expiredAtDb);
			}

			const env = `${process.env.ENVIRONMENT}`;
			if (env.toLowerCase() !== 'dev') {
				// calling wso2 for send TAC via SMS/Email
				if (credentialType.includes('mobile')) {
					const bodyRequest: Wso2SmsReq = {
						phoneno: credentialValue,
						sms: `You recently requested a OTP for your MyUnifi App. Here's your OTP: ${TacNumber}. Valid for 5 mins`
					};
					const wso2Response: Wso2SmsRes =
						await this.wso2NotificationIntegration.getWso2SendSms(bodyRequest);
					if (wso2Response?.status !== 0)
						throw new UE_ERROR(
							'TAC send failed. Please try again later',
							StatusCodeEnum.WSO2_ERROR
						);
				} else {
					const wso2EmailReq: Wso2EmailReq = {
						to: credentialValue,
						from: EmailEnum.FROM_NOREPLY,
						subject: 'One-Time PIN (OTP) Login',
						body: sendTacTemplate(TacNumber)
					};

					await this.wso2NotificationIntegration.getWso2SendEmail(
						wso2EmailReq,
						this.integrationId,
						'UAID TAC'
					);
				}
			}
		}

		return res;
	}

	async verifyTac(deviceId: string, req: VerifyTacReq): Promise<IdentityRes> {
		await this.credentialHelper.checkCredentialReq(req);
		const { credentialValue, credentialKey } =
			await this.credentialHelper.getCredentialProperty(req);

		const skipTacValidation: boolean = await this.tacHelper.isSkipTacValidation(
			req.TacNumber,
			credentialValue
		);

		if (!skipTacValidation)
			await this.tacHelper.verifyTacNumber(
				this.credentialHelper,
				credentialKey,
				req.TacNumber
			);

		const profileRes: ProfileData =
			await this.profileHelper.getUserProfileByCredential(credentialKey);

		const userId = profileRes[0].mw_identity.UserId;

		// 1st time tac-verified for this credential
		if (!profileRes[0].mw_credential.IsVerified) {
			await this.db
				.update(credentialDbSchema)
				.set({ IsVerified: true, UpdatedAt: sql`now()` })
				.where(
					and(
						eq(credentialDbSchema.CredentialKey, credentialKey),
						eq(credentialDbSchema.IsVerified, false)
					)
				);
		}

		const res: IdentityRes = {
			Success: true,
			Code: 200,
			IntegrationId: this.integrationId,
			Response: {} as SelectIdentityApi
		};

		res.Response = await this.identityHelper.getUserInfoDetails(profileRes);
		res.Response.SessionId = await this.sessionHelper.getSessionId(
			userId,
			deviceId,
			this.checkSession
		);

		return res;
	}

	async signUpVerification(
		deviceId: string,
		req: SignUpVerificationReq
	): Promise<IdentityRes> {
		const credValue = req.CredentialType?.includes('mobile')
			? req.MobileNumber
			: req.Email;

		const credValueFiltered: string =
			await this.credentialHelper.filterCredentialValue(
				credValue,
				req.CredentialType
			);

		const creds: RequestTacReq = {
			CredentialType: req.CredentialType,
			CredentialValue: credValueFiltered
		};
		const { credentialValue, credentialKey } =
			await this.credentialHelper.getCredentialProperty(creds);

		const skipTacValidation: boolean = await this.tacHelper.isSkipTacValidation(
			req.TacNumber,
			credentialValue
		);
		if (!skipTacValidation)
			await this.tacHelper.verifyTacNumber(
				this.credentialHelper,
				credentialKey,
				req.TacNumber
			);

		// duplicate checking for re-submit scenario
		const email: string = await this.credentialHelper.filterCredentialValue(
			req.Email,
			'email'
		);
		const emailKey = sha256('email', email).toString();
		await this.credentialHelper.checkDuplicateCredential('email', emailKey);

		const mobileNumber: string =
			await this.credentialHelper.filterCredentialValue(
				req.MobileNumber,
				'mobile'
			);
		const mobileNumberKey = sha256('mobile', mobileNumber).toString();
		await this.credentialHelper.checkDuplicateCredential(
			'mobile',
			mobileNumberKey
		);

		// prepare ID Number data before INSERT
		const IdData: EncryptedKeyValueData = await getEncryptedKeyValue(
			req.IdValue,
			req.IdType
		);

		const isIdNumberInserted = await this.identityHelper.insertIdNumber(
			IdData,
			null
		);
		if (!isIdNumberInserted)
			throw new UE_ERROR(
				'The ID Number has been taken (registered) by other User',
				StatusCodeEnum.CONFLICT,
				{ integrationId: this.integrationId, response: null }
			);

		const insertNewUser = await this.db
			.insert(identityDbSchema)
			.values({
				Name: req.Name,
				IdKey: IdData.key,
				CreatedAt: sql`now()`
			})
			.returning({ userId: identityDbSchema.UserId });
		const userId = insertNewUser[0].userId;

		const isEmailVerified = !!req.CredentialType?.includes('email');
		await this.credentialHelper.updateCredential(
			userId,
			email,
			'email',
			isEmailVerified
		);
		const isMobileNumberVerified = !!req.CredentialType?.includes('mobile');
		await this.credentialHelper.updateCredential(
			userId,
			mobileNumber,
			'mobile',
			isMobileNumberVerified
		);

		const profileRes: ProfileData =
			await this.profileHelper.getUserProfileByCredential(credentialKey);

		const res: IdentityRes = {
			Success: true,
			Code: 200,
			IntegrationId: this.integrationId,
			Response: {} as SelectIdentityApi
		};

		res.Response = await this.identityHelper.getUserInfoDetails(profileRes);
		res.Response.SessionId = await this.sessionHelper.getSessionId(
			userId,
			deviceId,
			this.checkSession
		);

		return res;
	}
}

export default Tac;
