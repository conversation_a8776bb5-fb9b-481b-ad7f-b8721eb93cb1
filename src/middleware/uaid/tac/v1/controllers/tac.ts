import { randomUUID } from 'node:crypto';
import <PERSON><PERSON> from 'elysia';
import { StatusCodeEnum } from '../../../../../enum/statusCode.enum';
import { errorBaseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';
import { UE_ERROR } from '../../../../error';
import {
	type IdentityRes,
	identityResSchema
} from '../../../identity/v1/schemas/api/identity';
import {
	verifyTacHeaderSchema,
	xApiKeyHeaderSchema
} from '../../../util/schemas/headers';
import {
	type RequestTacRes,
	requestTacReqSchema,
	requestTacResSchema,
	signUpVerificationReqSchema,
	verifyTacReqSchema
} from '../schemas/api/tac';
import Tac from '../services/Tac';

const tacRoutes = new Elysia({ prefix: '/tac' })
	.post(
		'/code',
		async (ctx): Promise<RequestTacRes> => {
			return await new Tac(randomUUID()).requestTac(ctx.body);
		},
		{
			headers: xApiKeyHeaderSchema,
			body: requestTacReqSchema,
			response: {
				200: requestTacResSchema,
				401: errorBaseResponseSchema,
				404: errorBaseResponseSchema,
				409: errorBaseResponseSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Request TAC via SMS or Email. ' +
					'API will send the TAC based on the credential.<br>' +
					'Maximum resend is 3 times. Credential will be temporarily locked (if exceed max resend)<br>' +
					'Alt: Set "Id" property only. Get the value from Response.Credentials.Id<br><br>' +
					'Tables: mw_tac, mw_credential, mw_locked_acc<br><br>' +
					'Backend: MCS',
				tags: ['UAID: TAC']
			}
		}
	)
	.post(
		'/verification',
		async (ctx): Promise<IdentityRes> => {
			if (ctx.headers['device-id']) {
				return await new Tac(randomUUID()).verifyTac(
					ctx.headers['device-id'],
					ctx.body
				);
			}
			throw new UE_ERROR(
				'Invalid device!',
				StatusCodeEnum.UNPROCESSABLE_ENTITY,
				{ integrationId: randomUUID(), response: null }
			);
		},
		{
			headers: verifyTacHeaderSchema,
			body: verifyTacReqSchema,
			response: {
				200: identityResSchema,
				401: errorBaseResponseSchema,
				404: errorBaseResponseSchema,
				422: errorBaseResponseSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Verify TAC (expired in 5 min). If TAC verified, API will return user info and SessionId (to be used as Bearer token). <br>' +
					'Maximum retry is 3 times. Credential will be temporarily locked (if exceed max retry)<br>' +
					'SessionId will be generated based on DeviceId.<br>' +
					'Alt: Set "Id" & TacNumber property only.<br><br>' +
					'Tables: mw_tac, mw_credential, mw_locked_acc, mw_identity, mw_identification, mw_user_auth, mw_session',
				tags: ['UAID: TAC']
			}
		}
	)
	.post(
		'/signup-verification',
		async (ctx): Promise<IdentityRes> => {
			if (ctx.headers['device-id']) {
				return await new Tac(randomUUID()).signUpVerification(
					ctx.headers['device-id'],
					ctx.body
				);
			}
			throw new UE_ERROR(
				'Invalid device!',
				StatusCodeEnum.UNPROCESSABLE_ENTITY,
				{ integrationId: randomUUID(), response: null }
			);
		},
		{
			headers: verifyTacHeaderSchema,
			body: signUpVerificationReqSchema,
			response: {
				200: identityResSchema,
				401: errorBaseResponseSchema,
				404: errorBaseResponseSchema,
				422: errorBaseResponseSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Sign Up verification by TAC via Mobile Number (expired in 5 min).<br>' +
					'If TAC verified, API will return user info and SessionId (to be used as Bearer token). <br>' +
					'Maximum retry is 3 times. Credential will be temporarily locked (if exceed max retry)<br>' +
					'SessionId will be generated based on DeviceId.<br><br>' +
					'Tables: mw_tac, mw_credential, mw_locked_acc, mw_identity, mw_identification, mw_user_auth, mw_session',
				tags: ['UAID: TAC']
			}
		}
	);

export default tacRoutes;
