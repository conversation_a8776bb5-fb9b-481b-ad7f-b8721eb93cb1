import { sql } from 'drizzle-orm';
import {
	index,
	integer,
	pgTable,
	text,
	timestamp,
	varchar
} from 'drizzle-orm/pg-core';

export const tacDbSchema = pgTable(
	'mw_tac',
	{
		Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
		CredentialKey: varchar('credential_key', { length: 150 })
			.notNull()
			.unique(),
		TacNumber: varchar('tac_number', { length: 150 }).notNull(),
		EncTacNumber: text('enc_tac_number').notNull(),
		ResendCount: integer('resend').notNull().default(0),
		RetryCount: integer('retry').notNull().default(0),
		ExpiredAt: timestamp('expired_at', { mode: 'date', withTimezone: true })
			.notNull()
			.default(sql`now() + INTERVAL '5 minutes'`)
	},
	t => [index('idx_mw_tac_credential_key').on(t.CredentialKey)]
);

export type SelectTac = typeof tacDbSchema.$inferSelect;
export type InsertTac = typeof tacDbSchema.$inferInsert;

export const lockedAccDbSchema = pgTable(
	'mw_locked_acc',
	{
		Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
		CredentialKey: varchar('credential_key', { length: 150 })
			.notNull()
			.unique(),
		CreatedAt: timestamp('created_at', { mode: 'date', withTimezone: true })
			.notNull()
			.default(sql`now()`),
		UnlockedAt: timestamp('unlocked_at', { mode: 'date', withTimezone: true })
			.notNull()
			.default(sql`now() + INTERVAL '2 hours'`)
	},
	t => [index('idx_mw_locked_acc_credential_key').on(t.CredentialKey)]
);

export type SelectLockedCredential = typeof lockedAccDbSchema.$inferSelect;
export type InsertLockedCredential = typeof lockedAccDbSchema.$inferInsert;
