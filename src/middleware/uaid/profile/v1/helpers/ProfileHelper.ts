import { and, eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../../config/db.config';
import { pinoLog } from '../../../../../config/pinoLog.config';
import { StatusCodeEnum } from '../../../../../enum/statusCode.enum';
import { SmeUserRoleEnum } from '../../../../../enum/user.enum';
import {
	LightweightFlagEnum,
	SystemNameEnum
} from '../../../../../enum/wso2.enum';
import { MwIntegration } from '../../../../../integration/mw.integration';
import type {
	Wso2CustomerAccountReq,
	Wso2CustomerAccountRes
} from '../../../../../integration/wso2/user/schemas/api/wso2CustomerAccount.schema';
import type {
	Wso2ServiceAccountReq,
	Wso2ServiceAccountRes
} from '../../../../../integration/wso2/user/schemas/api/wso2ServiceAccount.schema';
import { UE_ERROR } from '../../../../error';
import { credentialDbSchema } from '../../../identity/v1/schemas/models/credential';
import {
	type SelectIdentification,
	identificationDbSchema,
	identityDbSchema
} from '../../../identity/v1/schemas/models/identity';
import { smeProfileDbSchema } from '../../../sme/v1/schemas/models/sme';
import type { EncryptedKeyValueData } from '../../../util/schemas/encryption';
import type { ProfileData, VerifyProfileReq } from '../schemas/api/profile';

class ProfileHelper {
	private db: NodePgDatabase;
	private integrationId: string;
	private mwIntegration: MwIntegration;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
		this.mwIntegration = new MwIntegration(integrationId);
	}

	async getServiceAccountVerification(req: VerifyProfileReq): Promise<boolean> {
		// Build the request for the Customer Account (CA)
		const wso2CAReq: Wso2CustomerAccountReq = {
			idType: req.IdType,
			idValue: req.IdValue ?? ''
		};

		// Call the Customer Account API (CA)
		const wso2CARes: Wso2CustomerAccountRes =
			await this.mwIntegration.Wso2UserIntegration.getWso2CustomerAccount(
				wso2CAReq,
				LightweightFlagEnum.YES
			);

		// Proceed only if Customer Accounts are returned
		if (wso2CARes?.Response?.CustomerAccounts) {
			for (const ca of wso2CARes.Response.CustomerAccounts) {
				if (ca.BillingAccounts) {
					// Loop through Billing Accounts
					for (const ba of ca.BillingAccounts) {
						// Scenario 1: BillingAccountNo is provided, match with CA and BA
						if (
							req.BillingAccountNo &&
							ba.AccountNumber === req.BillingAccountNo
						) {
							// Create Service Account (SA) request
							const wso2SAReq: Wso2ServiceAccountReq = {
								idType: req.IdType,
								idValue: req.IdValue ?? '',
								BillingAccountNo: ba.AccountNumber,
								SystemName: ca.SystemName ?? SystemNameEnum.NOVA
							};

							// Call the Service Account API (SA)
							const wso2SARes: Wso2ServiceAccountRes =
								(await this.mwIntegration.Wso2UserIntegration.getWso2ServiceAccount(
									wso2SAReq,
									LightweightFlagEnum.YES
								)) as Wso2ServiceAccountRes;

							// Check if Service Accounts are returned
							if (wso2SARes?.Response?.ServiceAccount) {
								for (const sa of wso2SARes.Response.ServiceAccount) {
									// Scenario 1: Validate that there are products for the matched BA
									if (sa?.Products && sa.Products.length > 0) {
										return true;
									}

									// NOVA system-specific check (Scenario 1)
									if (
										ca.SystemName === SystemNameEnum.NOVA &&
										sa &&
										sa.ServiceID
									) {
										return true;
									}
								}
							}
						}

						// Scenario 2: ServiceId is provided, match with Service ID from SA
						if (req.ServiceId) {
							const wso2SAReq: Wso2ServiceAccountReq = {
								idType: req.IdType,
								idValue: req.IdValue ?? '',
								BillingAccountNo: ba.AccountNumber ?? '',
								SystemName: ca.SystemName ?? SystemNameEnum.NOVA
							};

							// Call the Service Account API (SA)
							const wso2SARes: Wso2ServiceAccountRes =
								(await this.mwIntegration.Wso2UserIntegration.getWso2ServiceAccount(
									wso2SAReq,
									LightweightFlagEnum.YES
								)) as Wso2ServiceAccountRes;

							if (wso2SARes?.Response?.ServiceAccount) {
								for (const sa of wso2SARes.Response.ServiceAccount) {
									// Check if products contain the Internet service and matching serviceId
									if (sa?.Products && sa.Products.length > 0) {
										for (const product of sa?.Products ?? []) {
											if (
												product.ProductName === 'Internet' &&
												product.SerialNumber === req.ServiceId
											) {
												return true;
											}
										}
									}

									// NOVA system-specific check (Scenario 2)
									if (
										ca.SystemName === SystemNameEnum.NOVA &&
										sa &&
										sa.ServiceID &&
										req.ServiceId === sa.ServiceID
									) {
										return true;
									}
								}
							}
						}
					}
				}
			}
		}

		return false;
	}

	async getUserProfileByUserId(userId: string): Promise<ProfileData> {
		// Get existing profile + credential by UserId
		const getIdentityRes: ProfileData = await this.db
			.select()
			.from(identityDbSchema)
			.innerJoin(
				credentialDbSchema,
				eq(identityDbSchema.UserId, credentialDbSchema.UserId)
			)
			.leftJoin(
				identificationDbSchema,
				eq(identityDbSchema.IdKey, identificationDbSchema.IdKey)
			)
			.where(eq(identityDbSchema.UserId, userId))
			.limit(1);

		// If profile not-exist, exit and return error
		if (getIdentityRes.length === 0) {
			throw new UE_ERROR(
				'User profile does not exist in Identity!',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId, response: null }
			);
		}

		return getIdentityRes;
	}

	async getUserProfileByCredential(
		credentialKey: string
	): Promise<ProfileData> {
		// Get existing profile by credential
		const profileRes: ProfileData = await this.db
			.select()
			.from(identityDbSchema)
			.innerJoin(
				credentialDbSchema,
				eq(identityDbSchema.UserId, credentialDbSchema.UserId)
			)
			.leftJoin(
				identificationDbSchema,
				eq(identityDbSchema.IdKey, identificationDbSchema.IdKey)
			)
			.where(eq(credentialDbSchema.CredentialKey, credentialKey))
			.limit(1);

		if (profileRes.length === 0)
			throw new UE_ERROR(
				'Profile does not exist',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId, response: null }
			);

		return profileRes;
	}

	async isUserProfileExist(credentialKey: string): Promise<boolean> {
		const profileRes: ProfileData = await this.db
			.select()
			.from(identityDbSchema)
			.innerJoin(
				credentialDbSchema,
				eq(identityDbSchema.UserId, credentialDbSchema.UserId)
			)
			.leftJoin(
				identificationDbSchema,
				eq(identityDbSchema.IdKey, identificationDbSchema.IdKey)
			)
			.where(eq(credentialDbSchema.CredentialKey, credentialKey))
			.limit(1);

		return profileRes.length > 0;
	}

	async isSmeProfileExist(userId: string, IdKey: string): Promise<boolean> {
		// check if the SME Profile has been created/added
		const smeProfileRes = await this.db
			.select()
			.from(smeProfileDbSchema)
			.where(
				and(
					eq(smeProfileDbSchema.UserId, userId),
					eq(smeProfileDbSchema.IdKey, IdKey)
				)
			)
			.limit(1);

		return smeProfileRes.length > 0;
	}

	async insertSmeProfile(
		userId: string,
		IdData: EncryptedKeyValueData
	): Promise<void> {
		const isSmeProfileExist = await this.isSmeProfileExist(userId, IdData.key);
		if (!isSmeProfileExist) {
			const smeIdentification: SelectIdentification[] = await this.db
				.select()
				.from(identificationDbSchema)
				.where(eq(identificationDbSchema.IdKey, IdData.key))
				.limit(1);

			if (smeIdentification.length === 0) {
				try {
					await this.db.insert(identificationDbSchema).values({
						IdKey: IdData.key,
						IdType: IdData.type,
						IdValue: IdData.encValue,
						IsIdVerified: true
					});
				} catch (error) {
					pinoLog.error('createSmeIdentification: ', error);
					throw new UE_ERROR(
						'Error when insert into mw_identification table',
						StatusCodeEnum.UE_INTERNAL_SERVER,
						{ integrationId: this.integrationId, response: error }
					);
				}
			}

			try {
				await this.db.insert(smeProfileDbSchema).values({
					UserId: userId,
					IdKey: IdData.key,
					DeptId: 0,
					Role: SmeUserRoleEnum.SUPER_ADMIN
				});
			} catch (error) {
				pinoLog.error('createSmeUser: ', error);
				throw new UE_ERROR(
					'Error when insert into mw_sme_profile table',
					StatusCodeEnum.UE_INTERNAL_SERVER,
					{ integrationId: this.integrationId, response: error }
				);
			}
		}
	}

	// add hypens
	async reformatIdNumber(nric: string): Promise<string> {
		return nric.length === 12
			? `${nric.slice(0, 6)}-${nric.slice(6, 8)}-${nric.slice(8)}`
			: nric;
	}
}

export default ProfileHelper;
