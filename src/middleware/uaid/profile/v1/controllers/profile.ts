import { randomUUID } from 'node:crypto';
import Elysia from 'elysia';
import { errorBaseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';
import CheckSession from '../../../session/v1/services/CheckSession';
import {
	type VerifyProfileRes,
	verifyProfileReqSchema,
	verifyProfileResSchema
} from '../schemas/api/profile';
import Profile from '../services/Profile';

const profileRoutes = new Elysia({ prefix: '/profile' })
	.resolve(async ({ headers: { authorization } }) => {
		return {
			userId: await new CheckSession(randomUUID()).getUserId(
				authorization ?? ''
			)
		};
	})
	.post(
		'/id-verification',
		async (ctx): Promise<VerifyProfileRes> => {
			return await new Profile(randomUUID()).verifyAccountStatus(
				ctx.userId,
				ctx.body
			);
		},
		{
			body: verifyProfileReqSchema,
			response: {
				200: verifyProfileResSchema,
				404: errorBaseResponseSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Verify the service subscription by the user (using SessionId as Bearer token in Authorization header).<br><br>' +
					'To update isIdVerified = true (if service existed)<br><br>' +
					'Backend: NOVA, SIEBEL, ICP<br><br>' +
					'Tables: mw_identity, mw_identification, mw_sme_profile, wso2_consumer_accounts, wso2_sme_accounts, wso2_service_accounts<br><br>',
				tags: ['UAID: Profile']
			}
		}
	);

export default profileRoutes;
