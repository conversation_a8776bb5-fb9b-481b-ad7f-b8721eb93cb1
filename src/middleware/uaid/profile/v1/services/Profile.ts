import { and, eq, sql } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../../config/db.config';
import { StatusCodeEnum } from '../../../../../enum/statusCode.enum';
import { IdTypeEnum } from '../../../../../enum/user.enum';
import { UE_ERROR } from '../../../../error';
import IdentityHelper from '../../../identity/v1/helpers/IdentityHelper';
import type { GetId } from '../../../identity/v1/schemas/api/identity';
import {
	type SelectIdentity,
	identificationDbSchema,
	identityDbSchema
} from '../../../identity/v1/schemas/models/identity';
import { getEncryptedKeyValue } from '../../../util/encryption';
import type { EncryptedKeyValueData } from '../../../util/schemas/encryption';
import ProfileHelper from '../helpers/ProfileHelper';
import type {
	VerifyProfileReq,
	VerifyProfileRes
} from '../schemas/api/profile';

class Profile {
	private db: NodePgDatabase;
	private integrationId: string;
	private profileHelper: ProfileHelper;
	private identityHelper: IdentityHelper;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
		this.profileHelper = new ProfileHelper(integrationId);
		this.identityHelper = new IdentityHelper(integrationId);
	}

	async verifyAccountStatus(
		userId: string,
		req: VerifyProfileReq
	): Promise<VerifyProfileRes> {
		// check if user profile exist
		const profileRes: SelectIdentity[] = await this.db
			.select()
			.from(identityDbSchema)
			.where(eq(identityDbSchema.UserId, userId))
			.limit(1);

		if (!profileRes)
			throw new UE_ERROR(
				'Identity does not exist!',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{
					integrationId: this.integrationId,
					response: null
				}
			);

		if (!profileRes[0].IdKey)
			throw new UE_ERROR(
				'Unable to proceed due to incomplete profile (ID Number not existed)',
				StatusCodeEnum.UNPROCESSABLE_ENTITY,
				{
					integrationId: this.integrationId,
					response: null
				}
			);

		const oriIdentification: GetId =
			await this.identityHelper.decodeIdentificationToken(profileRes[0].IdKey);
		if (!oriIdentification?.IdValue)
			throw new UE_ERROR(
				'Decoded-IdValue is null or undefined',
				StatusCodeEnum.UNPROCESSABLE_ENTITY,
				{
					integrationId: this.integrationId,
					response: null
				}
			);

		const businessIdTypes = [IdTypeEnum.BRN, IdTypeEnum.NON_BRN];
		if (!businessIdTypes.includes(req.IdType))
			req.IdValue = oriIdentification.IdValue;

		// re-assign new val (NRIC with hyphens)
		if (req.IdType === IdTypeEnum.NEW_NRIC) {
			req.IdValue = await this.profileHelper.reformatIdNumber(
				oriIdentification.IdValue
			);
		}

		// Call WSO2 for SA verification
		const serviceFlag: boolean =
			await this.profileHelper.getServiceAccountVerification(req);

		if (serviceFlag) {
			// for SME Profile
			if (businessIdTypes.includes(req.IdType)) {
				const IdData: EncryptedKeyValueData = await getEncryptedKeyValue(
					req.IdValue,
					req.IdType
				);
				await this.profileHelper.insertSmeProfile(userId, IdData);
			} else {
				// for Consumer Profile
				// update only if IsIdVerified === false
				await this.db
					.update(identificationDbSchema)
					.set({
						IsIdVerified: serviceFlag,
						UpdatedAt: sql`now()`
					})
					.where(
						and(
							eq(identificationDbSchema.IdKey, profileRes[0].IdKey),
							eq(identificationDbSchema.IsIdVerified, false)
						)
					);
			}
		}

		const res: VerifyProfileRes = {
			Success: true,
			Code: 200,
			Response: {
				accountExist: serviceFlag
			}
		};
		return res;
	}
}
export default Profile;
