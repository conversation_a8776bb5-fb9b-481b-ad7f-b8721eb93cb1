import { type Static, t } from 'elysia';

// ** INFO: elysia parse headers as a lowercase key only
// ** Source: https://elysiajs.com/essential/validation#specs-3
export const xApiKeyHeaderSchema = t.Object({
	'x-api-key': t.String()
});

export type XApiKeyHeader = Static<typeof xApiKeyHeaderSchema>;

export const verifyTacHeaderSchema = t.Object({
	'x-api-key': t.String(),
	'device-id': t.String()
});

export type VerifyTacHeader = Static<typeof verifyTacHeaderSchema>;

export const authorizedHeaderSchema = t.Object({
	authorization: t.String()
});

export type AuthorizedHeader = Static<typeof authorizedHeaderSchema>;
