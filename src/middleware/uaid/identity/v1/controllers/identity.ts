import { randomUUID } from 'node:crypto';
import Elysia from 'elysia';
import { errorBaseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';
import CheckSession from '../../../session/v1/services/CheckSession';
import { xApiKeyHeaderSchema } from '../../../util/schemas/headers';
import {
	type DeleteIdentityRes,
	type ForgotLoginIdRes,
	type IdentityRes,
	type SignUpRes,
	type UpdateIdentityRes,
	deleteIdentityResSchema,
	fetchIdentityReqSchema,
	forgotLoginIdReqSchema,
	forgotLoginIdResSchema,
	identityResSchema,
	signUpReqSchema,
	signUpResSchema,
	updateIdentityReqSchema,
	updateIdentityResSchema
} from '../schemas/api/identity';
import Identity from '../services/Identity';

export const identityRoutes = new Elysia({ prefix: '/identity' })
	.resolve(async ({ headers: { authorization } }) => {
		return {
			userId: await new CheckSession(randomUUID()).getUserId(
				authorization ?? ''
			)
		};
	})
	.put(
		'',
		async (ctx): Promise<UpdateIdentityRes> => {
			return await new Identity(randomUUID()).updateIdentity(
				ctx.userId,
				ctx.body
			);
		},
		{
			body: updateIdentityReqSchema,
			response: {
				200: updateIdentityResSchema,
				401: errorBaseResponseSchema,
				404: errorBaseResponseSchema,
				422: errorBaseResponseSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Update user info by SessionId (as Bearer token in Authorization header).<br><br>' +
					'Tables : mw_identity, mw_identification, mw_credential',
				tags: ['UAID: Identity']
			}
		}
	)
	.get(
		'/hard-delete',
		async (ctx): Promise<DeleteIdentityRes> => {
			return await new Identity(randomUUID()).deleteIdentity(ctx.userId);
		},
		{
			response: {
				200: deleteIdentityResSchema,
				401: errorBaseResponseSchema,
				404: errorBaseResponseSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Delete user info by SessionId (as Bearer token in Authorization header).<br><br>' +
					'Tables : mw_identity, mw_identification, mw_credential, mw_user_auth, mw_session, mw_tac, mw_sme_profile',
				tags: ['UAID: Identity']
			}
		}
	);

export const privateIdentityRoutes = new Elysia({ prefix: '/identity' })
	.post(
		'/signup',
		async (ctx): Promise<SignUpRes> => {
			return await new Identity(randomUUID()).registerNewProfile(ctx.body);
		},
		{
			body: signUpReqSchema,
			response: {
				200: signUpResSchema,
				401: errorBaseResponseSchema,
				404: errorBaseResponseSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Onboarding / Create profile for new User.<br>API will do the duplicate checking on the Credential & ID Number.<br><br>' +
					'Tables : mw_identity, mw_identification, mw_credential',
				tags: ['UAID: Identity']
			}
		}
	)
	.post(
		'/forgot-login-id',
		async (ctx): Promise<ForgotLoginIdRes> => {
			return await new Identity(randomUUID()).forgotLoginId(ctx.body);
		},
		{
			headers: xApiKeyHeaderSchema,
			body: forgotLoginIdReqSchema,
			response: {
				200: forgotLoginIdResSchema,
				401: errorBaseResponseSchema,
				404: errorBaseResponseSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Existing User: To retrieve credential by ID Number via sms/email/both (if ID Number existed).<br><br>' +
					'Tables : mw_identity, mw_identification, mw_credential',
				tags: ['UAID: Identity']
			}
		}
	)
	.post(
		'/get-details',
		async (ctx): Promise<IdentityRes> => {
			return await new Identity(randomUUID()).fetchIdentity(ctx.body);
		},
		{
			headers: xApiKeyHeaderSchema,
			body: fetchIdentityReqSchema,
			response: {
				200: identityResSchema,
				401: errorBaseResponseSchema,
				404: errorBaseResponseSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Get user identity info by credential (if exist).<br>' +
					'Tables: mw_identity, mw_identification, mw_credential<br><br>' +
					'Endpoint Status: To be deleted before merge to PROD',
				tags: ['UAID: Identity']
			}
		}
	);
