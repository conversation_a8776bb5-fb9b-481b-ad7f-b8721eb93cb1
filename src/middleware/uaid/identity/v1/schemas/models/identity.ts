import { sql } from 'drizzle-orm';
import {
	boolean,
	index,
	integer,
	pgTable,
	text,
	timestamp,
	varchar
} from 'drizzle-orm/pg-core';
import randomString from 'random-string-gen';

export const identityDbSchema = pgTable(
	'mw_identity',
	{
		Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
		UserId: varchar('user_id', { length: 15 })
			.notNull()
			.$defaultFn(() => randomString(15))
			.unique(),
		Name: varchar('name', { length: 150 }),
		IdKey: varchar('id_key', { length: 150 }).unique(),
		CreatedAt: timestamp('created_at', { mode: 'date', withTimezone: true })
			.notNull()
			.default(sql`now()`),
		UpdatedAt: timestamp('updated_at', { mode: 'date', withTimezone: true })
	},
	t => [
		index('idx_mw_identity_user_id').on(t.UserId),
		index('idx_mw_identity_id_key').on(t.IdKey)
	]
);

export type SelectIdentity = typeof identityDbSchema.$inferSelect;
export type InsertIdentity = typeof identityDbSchema.$inferInsert;

export const identificationDbSchema = pgTable(
	'mw_identification',
	{
		Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
		IdKey: varchar('id_key', { length: 150 }).unique(),
		IdType: varchar('id_type', { length: 50 }),
		IdValue: text('id_value'),
		IsIdVerified: boolean('is_id_verified').default(false),
		CreatedAt: timestamp('created_at', { mode: 'date', withTimezone: true })
			.notNull()
			.default(sql`now()`),
		UpdatedAt: timestamp('updated_at', { mode: 'date', withTimezone: true })
	},
	t => [index('idx_mw_identification_id_key').on(t.IdKey)]
);

export type SelectIdentification = typeof identificationDbSchema.$inferSelect;
export type InsertIdentification = typeof identificationDbSchema.$inferInsert;
