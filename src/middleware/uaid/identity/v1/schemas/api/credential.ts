import { type Static, t } from 'elysia';

const getCredentialsSchema = t.Object({
	Id: t.String(),
	Type: t.String({ examples: ['mobile'] }),
	Key: t.String({
		examples: [
			'680aje0d9cc158d1f1ce112c262d4cdf485a3a6a3cbca712c9a73e216be14957'
		]
	}),
	MaskedValue: t.String({ examples: ['601234****89'] }),
	IsPrimary: t.Nullable(t.<PERSON>()),
	IsUnifiNumber: t.Nullable(t.<PERSON>()),
	IsVerified: t.Nullable(t.<PERSON>())
});

export type GetCredentials = Static<typeof getCredentialsSchema>;

export const credentialsResSchema = t.Object({
	Id: t.String({
		examples: [
			'0x244417f956ac7c599f191593f7e441a4fafa20a4158fd52e154f1dc4c8ed92'
		]
	}),
	Type: t.String({ examples: ['mobile'] }),
	MaskedValue: t.String({ examples: ['601234****89'] }),
	IsVerified: t.Nullable(t.<PERSON>olean({ examples: true }))
});
