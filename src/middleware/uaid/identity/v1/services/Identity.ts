import { eq, sql } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../../config/db.config';
import { pinoLog } from '../../../../../config/pinoLog.config';
import { StatusCodeEnum } from '../../../../../enum/statusCode.enum';
import { UE_ERROR } from '../../../../error';
import ProfileHelper from '../../../profile/v1/helpers/ProfileHelper';
import type { ProfileData } from '../../../profile/v1/schemas/api/profile';
import { getEncryptedKeyValue, sha256 } from '../../../util/encryption';
import type { EncryptedKeyValueData } from '../../../util/schemas/encryption';
import CredentialHelper from '../helpers/CredentialHelper';
import IdentityHelper from '../helpers/IdentityHelper';
import type { GetCredentials } from '../schemas/api/credential';
import type {
	DeleteIdentityRes,
	FetchIdentityReq,
	ForgotLoginIdReq,
	ForgotLoginIdRes,
	IdentityRes,
	SelectIdentityApi,
	SignUpReq,
	SignUpRes,
	UpdateIdentityReq,
	UpdateIdentityRes
} from '../schemas/api/identity';
import { credentialDbSchema } from '../schemas/models/credential';
import {
	type SelectIdentity,
	identificationDbSchema,
	identityDbSchema
} from '../schemas/models/identity';

class Identity {
	private db: NodePgDatabase;
	private integrationId: string;
	private identityHelper: IdentityHelper;
	private profileHelper: ProfileHelper;
	private credentialHelper: CredentialHelper;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
		this.identityHelper = new IdentityHelper(integrationId);
		this.profileHelper = new ProfileHelper(integrationId);
		this.credentialHelper = new CredentialHelper(integrationId);
	}

	async fetchIdentity(req: FetchIdentityReq): Promise<IdentityRes> {
		const credentialValue: string =
			await this.credentialHelper.filterCredentialValue(
				req.CredentialValue,
				req.CredentialType
			);

		const credentialKey = sha256(
			req.CredentialType,
			credentialValue
		).toString();

		const res: IdentityRes = {
			Success: true,
			Code: 200,
			IntegrationId: this.integrationId,
			Response: {} as SelectIdentityApi
		};

		// get user-profile by credentialKey
		const profileRes: ProfileData = await this.db
			.select()
			.from(identityDbSchema)
			.innerJoin(
				credentialDbSchema,
				eq(identityDbSchema.UserId, credentialDbSchema.UserId)
			)
			.leftJoin(
				identificationDbSchema,
				eq(identityDbSchema.IdKey, identificationDbSchema.IdKey)
			)
			.where(eq(credentialDbSchema.CredentialKey, credentialKey))
			.limit(1);

		// populate userinfo attributes if profile exist
		if (profileRes.length > 0) {
			res.Response = await this.identityHelper.getUserInfoDetails(profileRes);
			return res;
		}

		throw new UE_ERROR('Profile Not Exist', StatusCodeEnum.NOT_FOUND_ERROR, {
			integrationId: this.integrationId,
			response: null
		});
	}

	async updateIdentity(
		userId: string,
		req: UpdateIdentityReq
	): Promise<UpdateIdentityRes> {
		// check existing profile by UserId
		const userInfo = await this.profileHelper.getUserProfileByUserId(userId);

		let isIdNumberInserted = true;
		let isDataResubmited = true;
		let isEmailUpdated = false;
		let isMobileNumberUpdated = false;

		if (req.Name && !userInfo[0].mw_identity.Name?.includes(req.Name)) {
			try {
				await this.db
					.update(identityDbSchema)
					.set({ Name: req.Name, UpdatedAt: sql`now()` })
					.where(eq(identityDbSchema.UserId, userId));
			} catch (error) {
				pinoLog.error('update Name: ', error);
				throw new UE_ERROR(
					'Error when updating mw_identity table',
					StatusCodeEnum.UE_INTERNAL_SERVER,
					{ integrationId: this.integrationId, response: error }
				);
			}
			isDataResubmited = false;
		}

		let credentials: GetCredentials[] = [];
		if (req.IdType && req.IdValue) {
			const IdData: EncryptedKeyValueData = await getEncryptedKeyValue(
				req.IdValue,
				req.IdType
			);

			if (!userInfo[0].mw_identity.IdKey?.includes(IdData.key)) {
				// ignore insert and return false if ID existed
				isIdNumberInserted = await this.identityHelper.insertIdNumber(
					IdData,
					userId
				);
				isDataResubmited = false;
			}

			if (!isIdNumberInserted)
				credentials = await this.credentialHelper.getCredentialsByIdNumber(
					IdData.key
				);
		}

		if (req.Email) {
			isEmailUpdated = await this.credentialHelper.updateCredential(
				userId,
				req.Email,
				'email',
				true
			);
		}

		if (req.MobileNumber) {
			isMobileNumberUpdated = await this.credentialHelper.updateCredential(
				userId,
				req.MobileNumber,
				'mobile',
				true
			);
		}

		const res = {
			Success: true,
			Code: 200,
			IntegrationId: this.integrationId,
			Response: {
				Action: 'UPDATED',
				Message: 'Identity/profile was successfully updated',
				Credentials: credentials
			}
		};

		if (isDataResubmited && !isEmailUpdated && !isMobileNumberUpdated) {
			res.Response.Action = 'NO_CHANGE';
			res.Response.Message =
				'No updates were made as the submitted data is unchanged.';
		}

		if (!isIdNumberInserted) {
			res.Success = false;
			res.Code = 200;
			res.Response.Action = 'ID_NUMBER_EXISTED';
			res.Response.Message =
				'The ID Number has been taken (registered) by other User';
		}

		return res;
	}

	async deleteIdentity(userId: string): Promise<DeleteIdentityRes> {
		// check existing profile by UserId
		const profileRes: ProfileData =
			await this.profileHelper.getUserProfileByUserId(userId);

		try {
			await this.db
				.delete(identityDbSchema)
				.where(eq(identityDbSchema.UserId, userId));

			if (profileRes[0].mw_identity.IdKey) {
				await this.db
					.delete(identificationDbSchema)
					.where(
						eq(identificationDbSchema.IdKey, profileRes[0].mw_identity.IdKey)
					);
			}
		} catch (error) {
			pinoLog.error('Error upon account deletion: ', error);
			throw new UE_ERROR('Failed to delete', StatusCodeEnum.NOT_FOUND_ERROR, {
				integrationId: this.integrationId,
				response: error
			});
		}

		return {
			Success: true,
			Code: 200,
			IntegrationId: this.integrationId,
			Response: {
				Action: 'DELETED',
				Message: 'Identity/profile was successfully deleted'
			}
		};
	}

	async registerNewProfile(req: SignUpReq): Promise<SignUpRes> {
		const IdData: EncryptedKeyValueData = await getEncryptedKeyValue(
			req.IdValue,
			req.IdType
		);

		let credentials: GetCredentials[] = [];
		let isIdNumberExisted = false;

		const existedIdNumber: SelectIdentity[] = await this.db
			.select()
			.from(identityDbSchema)
			.where(eq(identityDbSchema.IdKey, IdData.key))
			.limit(1);

		if (existedIdNumber.length > 0) {
			isIdNumberExisted = true;
			credentials = await this.credentialHelper.getCredentialsByIdNumber(
				IdData.key
			);
		}

		const email: string = await this.credentialHelper.filterCredentialValue(
			req.Email,
			'email'
		);
		const emailCredKey = sha256('email', email).toString();
		await this.credentialHelper.checkDuplicateCredential('email', emailCredKey);

		const mobileNumber: string =
			await this.credentialHelper.filterCredentialValue(
				req.MobileNumber,
				'mobile'
			);
		const mobileCredKey = sha256('mobile', mobileNumber).toString();
		await this.credentialHelper.checkDuplicateCredential(
			'mobile',
			mobileCredKey
		);

		const res = {
			Success: true,
			Code: 200,
			IntegrationId: this.integrationId,
			Response: {
				Action: 'OK',
				Message: 'Proceed to Sign Up Verification',
				Credentials: credentials
			}
		};

		if (isIdNumberExisted) {
			res.Response.Action = 'ID_NUMBER_EXISTED';
			res.Response.Message =
				'The ID Number has been taken/registered by other User';
		}

		return res;
	}

	async forgotLoginId(req: ForgotLoginIdReq): Promise<ForgotLoginIdRes> {
		const IdData: EncryptedKeyValueData = await getEncryptedKeyValue(
			req.IdValue,
			req.IdType
		);

		const credentials: GetCredentials[] =
			await this.credentialHelper.getCredentialsByIdNumber(IdData.key);

		if (credentials.length === 0)
			throw new UE_ERROR(
				'Profile does not exist',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId, response: null }
			);

		return {
			Success: true,
			Code: 200,
			IntegrationId: this.integrationId,
			Response: {
				Action: 'ID_NUMBER_EXISTED',
				Message: 'List of credentials for the provided ID number.',
				Credentials: credentials
			}
		};
	}

	// POC: UE - UAID Integration by token to decode IdType and IdValue
	// async decodeIdentification(bearerToken: string): Promise<GetId> {
	// 	const encodedToken = bearerToken.replace(/Bearer\s+/i, '');
	// 	return this.identityHelper.decodeIdentificationToken(encodedToken);
	// }
}
export default Identity;
