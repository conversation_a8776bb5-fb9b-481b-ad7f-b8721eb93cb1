import { and, eq, gt, sql } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../../config/db.config';
import { StatusCodeEnum } from '../../../../../enum/statusCode.enum';
import { CredentialTypeEnum } from '../../../../../enum/user.enum';
import { UE_ERROR } from '../../../../error';
import type { RequestTacReq } from '../../../tac/v1/schemas/api/tac';
import { lockedAccDbSchema } from '../../../tac/v1/schemas/models/tac';
import {
	decrypt,
	encrypt,
	getEncryptedKeyValue,
	getMaskedValue,
	sha256
} from '../../../util/encryption';
import type { EncryptedKeyValueData } from '../../../util/schemas/encryption';
import type { GetCredentials } from '../schemas/api/credential';
import {
	type InsertCredential,
	type SelectCredential,
	credentialDbSchema
} from '../schemas/models/credential';
import {
	identificationDbSchema,
	identityDbSchema
} from '../schemas/models/identity';

class CredentialHelper {
	private db: NodePgDatabase;
	private integrationId: string;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
	}

	async getCredentialByKey(key: string): Promise<SelectCredential[]> {
		// Get credential by Id
		const getCredentialRes: SelectCredential[] = await this.db
			.select()
			.from(credentialDbSchema)
			.where(eq(credentialDbSchema.CredentialKey, key))
			.limit(1);

		// If credential not-exist, exit and return error
		if (getCredentialRes.length === 0) {
			throw new UE_ERROR(
				'Credential does not exist!',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId, response: null }
			);
		}

		return getCredentialRes;
	}

	async updateCredential(
		userId: string,
		credentialValue: string,
		credentialType: string,
		isVerified: boolean
	): Promise<boolean> {
		const credValueFiltered: string = await this.filterCredentialValue(
			credentialValue,
			credentialType
		);

		const credData: EncryptedKeyValueData = await getEncryptedKeyValue(
			credValueFiltered,
			credentialType
		);

		const isProceed = await this.isUpdateCredentialNeeded(userId, credData.key);

		if (!isProceed) return false;

		await this.checkDuplicateCredential(credentialType, credData.key);

		let updateStatus = false;
		const existingCredentialType = await this.db
			.select()
			.from(credentialDbSchema)
			.where(
				and(
					eq(credentialDbSchema.UserId, userId),
					eq(credentialDbSchema.CredentialType, credentialType)
				)
			)
			.limit(1);

		if (existingCredentialType.length === 0) {
			const createCredential: InsertCredential[] = await this.db
				.insert(credentialDbSchema)
				.values({
					UserId: userId,
					CredentialKey: credData.key,
					CredentialType: credentialType,
					CredentialValue: credData.encValue,
					IsPrimary: true,
					IsVerified: isVerified
				})
				.returning();
			if (createCredential.length > 0) updateStatus = true;
		} else {
			const updateRes: InsertCredential[] = await this.db
				.update(credentialDbSchema)
				.set({
					CredentialKey: credData.key,
					CredentialType: credentialType,
					CredentialValue: credData.encValue,
					IsVerified: isVerified,
					UpdatedAt: sql`now()`
				})
				.where(eq(credentialDbSchema.Id, existingCredentialType[0].Id))
				.returning();

			if (updateRes.length > 0) updateStatus = true;
		}

		if (!updateStatus)
			throw new UE_ERROR(
				'Failed to add/update Credential',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId, response: null }
			);

		return updateStatus;
	}

	async isUpdateCredentialNeeded(
		userId: string,
		credentialKey: string
	): Promise<boolean> {
		const existingCredential = await this.db
			.select()
			.from(credentialDbSchema)
			.where(
				and(
					eq(credentialDbSchema.UserId, userId),
					eq(credentialDbSchema.CredentialKey, credentialKey)
				)
			)
			.limit(1);

		// if existed, return false (meaning data no-change)
		// else, return true (update needed)
		return existingCredential.length === 0;
	}

	async getCredentialsByIdNumber(IdKey: string): Promise<GetCredentials[]> {
		let credentials: GetCredentials[] = [];
		const credentialRes = await this.db
			.select({
				Id: credentialDbSchema.CredentialKey,
				Type: credentialDbSchema.CredentialType,
				Value: credentialDbSchema.CredentialValue,
				Key: credentialDbSchema.CredentialKey,
				MaskedValue: credentialDbSchema.CredentialValue,
				IsPrimary: credentialDbSchema.IsPrimary,
				IsUnifiNumber: credentialDbSchema.IsUnifiNumber,
				IsVerified: credentialDbSchema.IsVerified
			})
			.from(identityDbSchema)
			.innerJoin(
				credentialDbSchema,
				eq(identityDbSchema.UserId, credentialDbSchema.UserId)
			)
			.innerJoin(
				identificationDbSchema,
				eq(identityDbSchema.IdKey, identificationDbSchema.IdKey)
			)
			.where(eq(identificationDbSchema.IdKey, IdKey));

		if (credentialRes.length > 0) {
			await Promise.all(
				credentialRes.map(async element => {
					const maskedValueRemap = await getMaskedValue(
						element.MaskedValue,
						element.Type,
						element.Key
					);
					element.Value = '************';
					element.Key = '************';
					element.MaskedValue = maskedValueRemap;
				})
			);
			credentials = credentialRes;
		}

		return credentials;
	}

	async checkDuplicateCredential(
		credentialType: string,
		credentialKey: string
	): Promise<void> {
		const duplicateCredential = await this.db
			.select()
			.from(credentialDbSchema)
			.where(eq(credentialDbSchema.CredentialKey, credentialKey))
			.limit(1);
		const errorMsg = credentialType.includes('email')
			? 'Email already exist'
			: 'Mobile Number already exist';
		if (duplicateCredential.length > 0) {
			throw new UE_ERROR(errorMsg, StatusCodeEnum.CONFLICT, {
				integrationId: this.integrationId,
				response: null
			});
		}
	}

	async filterCredentialValue(
		credValue: string,
		credType: string
	): Promise<string> {
		return credType.includes(CredentialTypeEnum.MOBILE) &&
			credValue.startsWith('6')
			? credValue.slice(1)
			: credValue.toLowerCase();
	}

	async getCredentialProperty(req: RequestTacReq): Promise<{
		credentialValue: string;
		credentialType: string;
		credentialKey: string;
		credentialValueEnc: string;
	}> {
		let credentialValue = '';
		let credentialType = '';
		let credentialKey = '';
		let credentialValueEnc = '';
		if (req.CredentialType && req.CredentialValue) {
			credentialValue = await this.filterCredentialValue(
				req.CredentialValue,
				req.CredentialType
			);
			credentialType = req.CredentialType;
			credentialKey = sha256(credentialType, credentialValue).toString();
			credentialValueEnc = await encrypt(credentialValue, credentialKey);
		}
		if (req.Id) {
			const getCredentialRes: SelectCredential[] =
				await this.getCredentialByKey(req.Id);
			credentialValue = await decrypt(
				getCredentialRes[0].CredentialValue,
				getCredentialRes[0].CredentialKey
			);
			credentialType = getCredentialRes[0].CredentialType;
			credentialKey = getCredentialRes[0].CredentialKey;
			credentialValueEnc = getCredentialRes[0].CredentialValue;
		}

		return {
			credentialValue,
			credentialType,
			credentialKey,
			credentialValueEnc
		};
	}

	async lockCredential(credentialKey: string): Promise<void> {
		const isLocked = await this.checkLockedCredential(credentialKey);
		if (!isLocked) {
			try {
				await this.db
					.delete(lockedAccDbSchema)
					.where(eq(lockedAccDbSchema.CredentialKey, credentialKey));
				await this.db
					.insert(lockedAccDbSchema)
					.values({ CredentialKey: credentialKey });
			} catch (error) {
				throw new UE_ERROR(
					'Error delete/insert into mw_locked_acc',
					StatusCodeEnum.UE_INTERNAL_SERVER,
					{ integrationId: this.integrationId, response: null }
				);
			}
		}

		throw new UE_ERROR(
			'Your credential is temporarily locked due to exceeding the maximum failed attempts. Please try again later.',
			StatusCodeEnum.LIMIT_EXCEEDED_ERROR
		);
	}

	async checkLockedCredential(credentialKey: string): Promise<boolean> {
		const locked = await this.db
			.select()
			.from(lockedAccDbSchema)
			.where(
				and(
					eq(lockedAccDbSchema.CredentialKey, credentialKey),
					gt(lockedAccDbSchema.UnlockedAt, sql`now()`)
				)
			)
			.limit(1);

		if (locked.length > 0) {
			throw new UE_ERROR(
				'Your credential is temporarily locked due to exceeding the maximum failed attempts. Please try again later.',
				StatusCodeEnum.LOCKED,
				{ integrationId: this.integrationId, response: null }
			);
		}

		return false;
	}

	async checkCredentialReq(req: RequestTacReq): Promise<void> {
		if (!req.Id && (!req.CredentialType || !req.CredentialValue))
			throw new UE_ERROR(
				'Either Id or (Credential Type and Credential Value) must be provided.',
				StatusCodeEnum.UNPROCESSABLE_ENTITY,
				{ integrationId: this.integrationId, response: null }
			);
	}
}
export default CredentialHelper;
