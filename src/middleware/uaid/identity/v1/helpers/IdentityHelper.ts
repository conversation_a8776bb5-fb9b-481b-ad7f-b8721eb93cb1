import { eq, sql } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../../config/db.config';
import { pinoLog } from '../../../../../config/pinoLog.config';
import { StatusCodeEnum } from '../../../../../enum/statusCode.enum';
import { UE_ERROR } from '../../../../error';
import type { ProfileData } from '../../../profile/v1/schemas/api/profile';
import type { BrnIdSchema } from '../../../sme/v1/schemas/api/sme';
import {
	smeDeptDbSchema,
	smeProfileDbSchema
} from '../../../sme/v1/schemas/models/sme';
import {
	decrypt,
	getMaskedValue,
	maskIdNumber
} from '../../../util/encryption';
import type { EncryptedKeyValueData } from '../../../util/schemas/encryption';
import type { GetCredentials } from '../schemas/api/credential';
import type { GetId, SelectIdentityApi } from '../schemas/api/identity';
import { credentialDbSchema } from '../schemas/models/credential';
import {
	type SelectIdentification,
	type SelectIdentity,
	identificationDbSchema,
	identityDbSchema
} from '../schemas/models/identity';

class IdentityHelper {
	private db: NodePgDatabase;
	private integrationId: string;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
	}

	async getUserInfoDetails(
		profileRes: ProfileData
	): Promise<SelectIdentityApi> {
		let credentials: GetCredentials[] = [];
		const credentialRes = await this.db
			.select({
				Id: credentialDbSchema.CredentialKey,
				Type: credentialDbSchema.CredentialType,
				Key: credentialDbSchema.CredentialKey,
				MaskedValue: credentialDbSchema.CredentialValue,
				IsPrimary: credentialDbSchema.IsPrimary,
				IsUnifiNumber: credentialDbSchema.IsUnifiNumber,
				IsVerified: credentialDbSchema.IsVerified
			})
			.from(credentialDbSchema)
			.where(eq(credentialDbSchema.UserId, profileRes[0].mw_identity.UserId));

		if (credentialRes.length > 0) {
			await Promise.all(
				credentialRes.map(async element => {
					const maskedValueRemap = await getMaskedValue(
						element.MaskedValue,
						element.Type,
						element.Key
					);
					//element.Value = await decrypt(element.Value, element.Key);
					element.MaskedValue = maskedValueRemap;
				})
			);
			credentials = credentialRes;
		}

		let decryptedIdValue = '';
		if (
			profileRes[0].mw_identification?.IdValue &&
			profileRes[0].mw_identification?.IdKey
		) {
			decryptedIdValue = await decrypt(
				profileRes[0].mw_identification.IdValue,
				profileRes[0].mw_identification.IdKey
			);
		}

		const consumerId = {
			IdType: profileRes[0].mw_identification?.IdType ?? null,
			IdValueMasked:
				decryptedIdValue.length === 0 ? null : maskIdNumber(decryptedIdValue),
			IdToken: profileRes[0].mw_identification?.IdKey ?? undefined,
			IsIdVerified: profileRes[0].mw_identification?.IsIdVerified ?? false
		};

		let businessId: BrnIdSchema[] = [];
		const SmeProfileRes = await this.db
			.select({
				IdType: identificationDbSchema.IdType,
				IdValueMasked: identificationDbSchema.IdValue,
				IdToken: identificationDbSchema.IdKey,
				Role: smeProfileDbSchema.Role,
				DeptName: smeDeptDbSchema.DeptName,
				IsIdVerified: identificationDbSchema.IsIdVerified
			})
			.from(smeProfileDbSchema)
			.innerJoin(
				identificationDbSchema,
				eq(smeProfileDbSchema.IdKey, identificationDbSchema.IdKey)
			)
			.leftJoin(
				smeDeptDbSchema,
				eq(smeProfileDbSchema.DeptId, smeDeptDbSchema.Id)
			)
			.where(eq(smeProfileDbSchema.UserId, profileRes[0].mw_identity.UserId));

		if (SmeProfileRes.length > 0) {
			await Promise.all(
				SmeProfileRes.map(async element => {
					const decryptedIdValue = await decrypt(
						element.IdValueMasked ?? '',
						element.IdToken ?? ''
					);
					element.IdValueMasked = maskIdNumber(decryptedIdValue);
				})
			);
			businessId = SmeProfileRes;
		}

		return {
			UserId: profileRes[0].mw_identity.UserId,
			Name: profileRes[0].mw_identity.Name,
			Consumer: consumerId,
			Business: businessId,
			Credentials: credentials
		};
	}

	async insertIdNumber(
		IdData: EncryptedKeyValueData,
		userId: string | null
	): Promise<boolean> {
		const isIdKeyExist: SelectIdentity[] = await this.db
			.select()
			.from(identityDbSchema)
			.where(eq(identityDbSchema.IdKey, IdData.key))
			.limit(1);

		if (isIdKeyExist.length === 0) {
			// to prevent table "Identity" violates foreign key constraint
			await this.db
				.delete(identificationDbSchema)
				.where(eq(identificationDbSchema.IdKey, IdData.key));

			try {
				await this.db.insert(identificationDbSchema).values({
					IdKey: IdData.key,
					IdType: IdData.type,
					IdValue: IdData.encValue,
					IsIdVerified: false
				});
			} catch (error) {
				pinoLog.error('onboarding: ', error);
				throw new UE_ERROR(
					'Error when insert into mw_identification table',
					StatusCodeEnum.UE_INTERNAL_SERVER,
					{ integrationId: this.integrationId, response: error }
				);
			}

			if (userId) {
				try {
					await this.db
						.update(identityDbSchema)
						.set({ IdKey: IdData.key, UpdatedAt: sql`now()` })
						.where(eq(identityDbSchema.UserId, userId));
				} catch (error) {
					pinoLog.error('onboarding: ', error);
					throw new UE_ERROR(
						'Error when update IdKey in mw_identity table',
						StatusCodeEnum.UE_INTERNAL_SERVER,
						{ integrationId: this.integrationId, response: error }
					);
				}
			}
			return true;
		}
		// ID_NUMBER_EXISTED
		return false;
	}

	async decodeIdentificationToken(encodedToken: string): Promise<GetId> {
		// get from cache 1st (if exist)
		// const cacheName = `getDecodedToken-${encodedToken}`;
		// const cache = await getCache(cacheName);
		// if (cache) {
		// 	return JSON.parse(cache) as GetId;
		// }

		// Get Identification by IdKey (a.k.a bearerToken)
		const getIdRes: SelectIdentification[] = await this.db
			.select()
			.from(identificationDbSchema)
			.where(eq(identificationDbSchema.IdKey, encodedToken))
			.limit(1);

		// If Identification not-exist, exit and return error
		if (getIdRes.length === 0) {
			throw new UE_ERROR(
				'Identification does not exist!',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId, response: null }
			);
		}

		let decryptedIdValue = null;
		if (getIdRes[0].IdValue && getIdRes[0].IdKey) {
			decryptedIdValue = await decrypt(getIdRes[0].IdValue, getIdRes[0].IdKey);
		}

		const res: GetId = {
			IdType: getIdRes[0].IdType,
			IdValue: decryptedIdValue,
			IsIdVerified: getIdRes[0].IsIdVerified ?? false
			// CreatedAt: getIdRes[0].CreatedAt ?? null,
			// UpdatedAt: getIdRes[0].UpdatedAt ?? null
		};

		// Set cache for 24 hrs
		// await setCache(cacheName, JSON.stringify(res), 86400);
		return res;
	}
}
export default IdentityHelper;
