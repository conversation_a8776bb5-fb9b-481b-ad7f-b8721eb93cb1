import { StatusCodeEnum } from '../enum/statusCode.enum';
import { getEnumKeyByValue } from '../shared/common';

export class UE_ERROR extends Error {
	enumCode: number;
	message: string;
	integrationId: string | null | undefined;

	constructor(
		message: string,
		enumCode: StatusCodeEnum,
		cause?: { integrationId: string | null; response?: unknown }
	) {
		super(message);
		this.name = getEnumKeyByValue(StatusCodeEnum, enumCode);
		this.enumCode = Math.floor(enumCode);
		this.message = message;
		this.integrationId = cause?.integrationId;
		this.cause = cause?.response;
	}
}
