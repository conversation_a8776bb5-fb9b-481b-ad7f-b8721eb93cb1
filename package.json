{"name": "universal-engine", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "bunx tsc --noEmit && dotenv -e environments/.env.dev -- bun --watch src/index.ts", "sit": "bunx tsc --noEmit && dotenv -e environments/.env.sit -- bun --watch src/index.ts", "uat": "bunx tsc --noEmit && dotenv -e environments/.env.uat -- bun --watch src/index.ts", "preprod": "bunx tsc --noEmit && dotenv -e environments/.env.preprod -- bun --watch src/index.ts", "compose": "bunx tsc --noEmit && bun --watch src/index.ts", "test": "bun test", "db:up": "drizzle-kit up --config=drizzle.config.ts", "db:generate": "drizzle-kit generate --config=drizzle.config.ts", "db:drop": "drizzle-kit drop --config=drizzle.config.ts", "db:studio": "drizzle-kit studio --config=drizzle.config.ts", "db:migrate": "drizzle-kit migrate --config=drizzle.config.ts", "lint": "biome check ./src", "lint:fix": "biome check --write", "lint:unsafe": "biome check ./src --fix --unsafe", "depcheck": "depcheck", "trace": "bunx tsc -p tsconfig.json --noEmit --generateTrace trace && bunx analyze-trace trace", "precleanup": "bunx tsc -p tsconfig.json --noEmit", "cleanup": "bun run depcheck && rm -rf node_modules && rm -rf bun.lock && bun update --latest && bun install", "prepare": "husky || true"}, "dependencies": {"@biomejs/biome": "^1.9.4", "@bogeychan/elysia-logger": "^0.1.8", "@elysiajs/bearer": "^1.3.0", "@elysiajs/cors": "^1.3.1", "@elysiajs/jwt": "^1.3.0", "@elysiajs/opentelemetry": "1.2.0", "@elysiajs/swagger": "^1.3.0", "@opentelemetry/api": "^1.9.0", "@opentelemetry/exporter-trace-otlp-proto": "0.55.0", "@opentelemetry/sdk-trace-base": "^1.30.1", "@opentelemetry/sdk-trace-node": "^1.30.1", "@oslojs/crypto": "^1.0.1", "@oslojs/encoding": "^1.1.0", "@types/bun": "^1.2.12", "@types/random-string-gen": "^1.1.2", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "drizzle-kit": "^0.31.1", "drizzle-orm": "^0.43.1", "elysia": "^1.3.1", "fetch-retry": "^6.0.0", "ioredis": "^5.6.1", "opossum": "^8.4.0", "pg": "^8.15.6", "random-string-gen": "^1.1.4", "xml-js": "^1.6.11"}, "devDependencies": {"@types/opossum": "^8.1.8", "@types/pg": "^8.15.1", "depcheck": "^1.4.7", "dotenv-cli": "^8.0.0", "husky": "^9.1.7", "pino-pretty": "^13.0.0", "typescript": "^5.8.3"}, "depcheck": {"ignores": ["@opentelemetry/sdk-trace-base", "@types/bun", "vitest", "pino-pretty"]}, "module": "src/index.ts"}