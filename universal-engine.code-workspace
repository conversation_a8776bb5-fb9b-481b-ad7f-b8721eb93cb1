{
	"folders": [
		{
			"path": "."
		}
	],
	"settings": {
		// Editor Settings
		"editor.defaultFormatter": "esbenp.prettier-vscode",
		"editor.formatOnSave": true,
		"editor.tabSize": 2,
		"editor.insertSpaces": true,
		"[markdown]": {
			"files.trimTrailingWhitespace": false
		},
		"editor.suggest.snippetsPreventQuickSuggestions": false,
		"editor.codeActionsOnSave": {
			"source.fixAll": "explicit",
			"source.organizeImports": "explicit"
		},
		"cSpell.words": ["ogradient"],
		// Files Settings
		"files.eol": "\n",
		"files.encoding": "utf8",
		// Markdown Settings
		"markdown.styles": [
			"./workspace/markdown/markdown_style/bootstrap.min.css",
			"./workspace/markdown/markdown_style/dark-mode.css"
		],
		"markdown.preview.scrollEditorWithPreview": false,
		"markdown.preview.scrollPreviewWithEditor": false
	},
	"extensions": {
		"recommendations": [
			"PKief.material-icon-theme",
			"oderwat.indent-rainbow",
			"mhutchie.git-graph",
			"BracketPairColorDLW.bracket-pair-color-dlw",
			"tamasfe.even-better-toml",
			"biomejs.biome",
			"ms-azuretools.vscode-docker",
			"aaron-bond.better-comments"
		]
	}
}
